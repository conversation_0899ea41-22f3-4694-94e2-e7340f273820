diff --git a/node_modules/react-native-reanimated/src/createAnimatedComponent/setAndForwardRef.ts b/node_modules/react-native-reanimated/src/createAnimatedComponent/setAndForwardRef.ts
index 17d1bb3..cc84a7c 100644
--- a/node_modules/react-native-reanimated/src/createAnimatedComponent/setAndForwardRef.ts
+++ b/node_modules/react-native-reanimated/src/createAnimatedComponent/setAndForwardRef.ts
@@ -1,43 +1,7 @@
 'use strict';
-/**
- * imported from react-native
- */
 
 import type { MutableRefObject } from 'react';
 
-/* eslint-disable */
-/**
- * This is a helper function for when a component needs to be able to forward a ref
- * to a child component, but still needs to have access to that component as part of
- * its implementation.
- *
- * Its main use case is in wrappers for native components.
- *
- * Usage:
- *
- *   class MyView extends React.Component {
- *     _nativeRef = null;
- *
- *     _setNativeRef = setAndForwardRef({
- *       getForwardedRef: () => this.props.forwardedRef,
- *       setLocalRef: ref => {
- *         this._nativeRef = ref;
- *       },
- *     });
- *
- *     render() {
- *       return <View ref={this._setNativeRef} />;
- *     }
- *   }
- *
- *   const MyViewWithRef = React.forwardRef((props, ref) => (
- *     <MyView {...props} forwardedRef={ref} />
- *   ));
- *
- *   module.exports = MyViewWithRef;
- */
-/* eslint-enable */
-
 type ForwardedRef<T> = () => MutableRefObject<T> | ((ref: T) => void);
 
 function setAndForwardRef<T>({
@@ -57,8 +21,15 @@ function setAndForwardRef<T>({
       // Handle function-based refs. String-based refs are handled as functions.
       forwardedRef(ref);
     } else if (typeof forwardedRef === 'object' && forwardedRef != null) {
-      // Handle createRef-based refs
-      forwardedRef.current = ref;
+      // Handle createRef-based refs, but safely
+      try {
+        forwardedRef.current = ref;
+      } catch (e) {
+        console.warn(
+          '[setAndForwardRef] Unable to assign to forwardedRef.current. It might be read-only:',
+          e
+        );
+      }
     }
   };
 }
