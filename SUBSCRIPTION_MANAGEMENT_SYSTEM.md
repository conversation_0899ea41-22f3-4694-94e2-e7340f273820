# Subscription Management System Implementation

## 🎯 **Overview**

I've implemented a comprehensive subscription management system that properly handles:

- **Monthly vs Annual subscription detection** (for live plans only)
- **Upgrade/Downgrade functionality** (between Basic Plan ↔ Annual Basic Plan only)
- **Current plan identification**
- **Smart button logic based on user's subscription state**
- **Billing portal integration**
- **ProPlans as hardcoded "Coming Soon" plans** (no upgrade/downgrade logic)

## ✅ **Key Features Implemented**

### 1. **Smart Subscription Detection**

```typescript
// ✅ Utility functions added to src/util/index.tsx
export const getUserSubscriptionType = (user): 'monthly' | 'annual' | 'none'
export const getUserCurrentPlan = (user) // Returns current plan object
export const canUpgradeToPlan = (user, targetPlan): boolean
export const canDowngradeToPlan = (user, targetPlan): boolean
```

### 2. **Enhanced SubscriptionItem Component**

The `SubscriptionItem` component now shows different button text based on user's state:

- **Not Subscribed**: "Get Started" (only for Basic Plan)
- **Current Plan**: "Current Plan (Monthly/Annual)"
- **Upgrade Available**: "Upgrade to [Plan Name]"
- **Downgrade Available**: "Downgrade to [Plan Name]"
- **Not Available**: "Coming Soon"

### 3. **Subscription Management Actions**

```typescript
// ✅ New action types added
UPGRADE_SUBSCRIPTION;
DOWNGRADE_SUBSCRIPTION;
SWITCH_SUBSCRIPTION;
GET_BILLING_PORTAL_URL;

// ✅ Action creators
upgradeSubscriptionRequest(payload, callback);
downgradeSubscriptionRequest(payload, callback);
getBillingPortalUrlRequest(payload, callback);
```

### 4. **Enhanced User Experience Flow**

#### **For Unsubscribed Users:**

```
View Plans → Click "Get Started" → Purchase Flow → Subscription Active
```

#### **For Subscribed Users:**

```
View Plans → See Current Plan + Available Options
├── Click Current Plan → Billing Portal
├── Click Upgrade Option → Confirmation → Upgrade
└── Click Downgrade Option → Confirmation → Downgrade
```

### 5. **ProPlans Approach (Simplified)**

**✅ ProPlans (Pro, Pro Max, Pro Max Plus) are hardcoded "Coming Soon" plans:**

- **Always show "Coming Soon" button** (disabled)
- **No upgrade/downgrade logic** - they're just placeholders
- **Click shows alert**: "Pro will be available soon. Stay tuned for updates!"
- **No backend integration** needed for these plans

**✅ Live Plans (Basic Plan & Annual Basic Plan) have full functionality:**

- **Upgrade/Downgrade**: Between monthly ↔ annual Basic Plan only
- **Billing Portal**: Access to manage live subscriptions
- **Receipt Validation**: Full purchase flow with cooling-off period

### 6. **Updated User Experience Examples**

#### **For Unsubscribed Users:**

- **Basic Plan**: "Get Started" → Purchase Flow
- **Annual Basic Plan**: "Get Started" → Purchase Flow with Cooling-Off
- **ProPlans**: "Coming Soon" → Alert message

#### **For Subscribed Users (Basic Plan):**

- **Basic Plan**: "Current Plan (Monthly Plan)" → Billing Portal
- **Annual Basic Plan**: "Upgrade to Annual Basic Plan" → Upgrade Flow
- **ProPlans**: "Coming Soon" → Alert message

#### **For Subscribed Users (Annual Basic Plan):**

- **Annual Basic Plan**: "Current Plan (Annual Plan)" → Billing Portal
- **Basic Plan**: "Downgrade to Basic Plan" → Downgrade Flow
- **ProPlans**: "Coming Soon" → Alert message

## 🔧 **Technical Implementation**

### 1. **User Subscription Data Structure**

Updated `UserInfo` type to include subscription details:

```typescript
interface UserInfo {
  // ... existing fields
  subscriptionData?: {
    productId?: string;
    billingCycle?: 'monthly' | 'annual';
    isAnnual?: boolean;
    planName?: string;
    coolingOffStatus?: string;
    coolingOffEndDate?: string;
  };
}
```

### 2. **Button Logic Algorithm**

```typescript
const getButtonConfig = () => {
  // 1. Check if user is subscribed
  if (!isSubscribed) {
    return {text: 'Get Started', disabled: false};
  }

  // 2. Check if this is current plan
  if (isCurrentPlan) {
    return {text: 'Current Plan (Monthly/Annual)', disabled: false};
  }

  // 3. Check upgrade possibility
  if (canUpgrade) {
    return {text: 'Upgrade to [Plan]', disabled: false};
  }

  // 4. Check downgrade possibility
  if (canDowngrade) {
    return {text: 'Downgrade to [Plan]', disabled: false};
  }

  // 5. Default: Not available
  return {text: 'Coming Soon', disabled: true};
};
```

### 3. **Subscription Management Handlers**

```typescript
// ✅ Billing Portal Access
const handleBillingPortal = () => {
  getBillingPortalUrlRequest(payload, response => {
    navigation.navigate('PaymentScreen', {url: response.url});
  });
};

// ✅ Upgrade Confirmation
const handleSubscriptionUpgrade = targetPlan => {
  Alert.alert('Upgrade Confirmation', 'Are you sure?', [
    {text: 'Cancel'},
    {text: 'Upgrade', onPress: () => performUpgrade(targetPlan)},
  ]);
};

// ✅ Downgrade Confirmation
const handleSubscriptionDowngrade = targetPlan => {
  Alert.alert(
    'Downgrade Confirmation',
    'Changes take effect at end of billing period',
    [
      {text: 'Cancel'},
      {text: 'Downgrade', onPress: () => performDowngrade(targetPlan)},
    ],
  );
};
```

## 🔗 **Backend Integration Required**

### 1. **API Endpoints Needed**

```typescript
// Get billing portal URL
GET /api/v2/subscription/billing-portal?userId={userId}
Response: { success: true, url: "https://billing.stripe.com/..." }

// Upgrade subscription
POST /api/v2/subscription/upgrade
{
  userId: string,
  currentPlanId: string,
  targetPlanId: string,
  platform: 'ios' | 'android'
}

// Downgrade subscription
POST /api/v2/subscription/downgrade
{
  userId: string,
  currentPlanId: string,
  targetPlanId: string,
  platform: 'ios' | 'android'
}
```

### 2. **User Info API Enhancement**

Update the `userInfo` API response to include subscription details:

```json
{
  "subscriptionStatus": "active",
  "subscriptionData": {
    "productId": "Basic_plans",
    "billingCycle": "monthly",
    "isAnnual": false,
    "planName": "Basic Plan",
    "coolingOffStatus": null,
    "coolingOffEndDate": null
  }
}
```

## 📱 **User Experience Examples**

### **Scenario 1: Monthly Basic Plan User**

- **Current Plan Button**: "Current Plan (Monthly Plan)" → Opens billing portal
- **Annual Basic Plan Button**: "Upgrade to Annual Basic Plan" → Shows upgrade confirmation
- **Pro Plan Button**: "Upgrade to Pro" → Shows upgrade confirmation
- **Other Plans**: "Coming Soon" (disabled)

### **Scenario 2: Annual Basic Plan User (Cooling-Off)**

- **Current Plan Button**: "Current Plan (Annual Plan)" → Opens billing portal
- **Monthly Basic Plan Button**: "Downgrade to Basic Plan" → Shows downgrade confirmation
- **Pro Plan Button**: "Upgrade to Pro" → Shows upgrade confirmation

### **Scenario 3: Unsubscribed User**

- **Basic Plan Button**: "Get Started" → Purchase flow
- **Annual Basic Plan Button**: "Get Started" → Purchase flow with cooling-off
- **Other Plans**: "Coming Soon" (disabled)

## 🎨 **Visual Indicators**

### **Button Colors**

- **Current Plan**: Green background
- **Upgrade Options**: Blue background
- **Downgrade Options**: Orange background
- **Get Started**: Black background
- **Not Available**: Gray background

### **Plan Status Indicators**

- **Active Monthly**: "Current Plan (Monthly Plan)"
- **Active Annual**: "Current Plan (Annual Plan)"
- **Cooling-Off**: "Current Plan (Cooling-Off Period)"

## 🚀 **Next Steps**

1. **Backend Implementation**: Implement the required API endpoints
2. **Testing**: Test upgrade/downgrade flows with sandbox accounts
3. **Billing Portal**: Configure Stripe/Apple/Google billing portals
4. **User Communication**: Add email notifications for plan changes
5. **Analytics**: Track subscription management usage

This implementation provides a complete subscription management system that gives users full control over their subscription while maintaining a smooth user experience! 🎯
