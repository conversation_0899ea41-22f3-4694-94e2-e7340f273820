# Annual Subscription Cooling-Off Period Implementation Guide

## 🎯 **Overview**

This guide covers the complete implementation of a 14-day cooling-off period for Annual subscription plans, including both mobile app changes and Apple App Store configuration.

## ✅ **Mobile App Implementation (COMPLETED)**

### 1. **New Annual Plan Configuration**
- Added Annual Basic Plan with cooling-off period
- Updated ProPlans constant with `isAnnual` flag and `coolingOffPeriod`
- Added product IDs for annual subscriptions

### 2. **Cooling-Off Period Logic**
- **Receipt Validation**: Validates purchases immediately with backend
- **Annual Plan Detection**: Checks if purchased product is annual
- **Cooling-Off Start**: Initiates 14-day cooling-off period for annual plans
- **Status Management**: Tracks cooling-off status and remaining days

### 3. **User Experience Flow**
```
User Purchases Annual Plan
    ↓
Receipt Validated Immediately
    ↓
Cooling-Off Period Started (14 days)
    ↓
User Sees Cooling-Off Message
    ↓
[During 14 days] User Can Cancel Without Charge
    ↓
[After 14 days] Subscription Activated & Charged
```

### 4. **Components Added**
- **CoolingOffPeriod Component**: Shows cooling-off status and cancel option
- **Updated Subscription Component**: Handles annual plan logic
- **New String Constants**: All cooling-off period messages

## 🍎 **Apple App Store Configuration (YOU NEED TO DO THIS)**

### 1. **Create Annual Subscription Product**

1. **Go to App Store Connect**
   - Navigate to your app
   - Go to "Features" → "In-App Purchases"

2. **Create New Auto-Renewable Subscription**
   - Click "+" to add new subscription
   - Choose "Auto-Renewable Subscription"
   - Product ID: `Basic_plans_annual` (must match your app)

3. **Configure Subscription Details**
   ```
   Reference Name: Annual Basic Plan
   Product ID: Basic_plans_annual
   Subscription Group: [Create new group or use existing]
   ```

4. **Set Pricing**
   ```
   Price: £35.60 (or equivalent in other currencies)
   Duration: 1 Year
   ```

### 2. **Configure Introductory Offers (CRITICAL for Cooling-Off)**

Apple doesn't have a built-in "cooling-off period" feature, so we need to use **Introductory Offers**:

1. **Add Introductory Offer**
   ```
   Type: Free Trial
   Duration: 14 Days
   Eligible: New Subscribers
   ```

2. **This Creates the Cooling-Off Effect**
   - User gets 14 days free trial
   - During trial: No charge, can cancel anytime
   - After trial: Automatically charged full annual price

### 3. **Alternative: Pay Up Front + Refund Policy**

If you prefer immediate charging with refunds:

1. **Set Normal Annual Subscription**
   - No introductory offer
   - Charge immediately

2. **Implement Refund Logic**
   - Use App Store Server API to issue refunds
   - Handle refunds within 14 days automatically

## 🔧 **Backend API Implementation (YOU NEED TO IMPLEMENT)**

### 1. **Required API Endpoints**

```typescript
// Start cooling-off period
POST /api/v2/subscription/start-cooling-off
{
  userId: string,
  subscriptionId: string,
  productId: string,
  transactionId: string,
  coolingOffDays: number,
  platform: 'ios' | 'android'
}

// Cancel during cooling-off
POST /api/v2/subscription/cancel-cooling-off
{
  userId: string,
  subscriptionId: string
}

// Check cooling-off status
GET /api/v2/subscription/cooling-off-status?userId={userId}
```

### 2. **Database Schema Updates**

```sql
-- Add cooling-off fields to subscriptions table
ALTER TABLE subscriptions ADD COLUMN cooling_off_start_date TIMESTAMP;
ALTER TABLE subscriptions ADD COLUMN cooling_off_end_date TIMESTAMP;
ALTER TABLE subscriptions ADD COLUMN cooling_off_status ENUM('active', 'expired', 'cancelled');
ALTER TABLE subscriptions ADD COLUMN is_annual BOOLEAN DEFAULT FALSE;
```

### 3. **Cooling-Off Logic**

```javascript
// Start cooling-off period
async function startCoolingOff(data) {
  const coolingOffEndDate = new Date();
  coolingOffEndDate.setDate(coolingOffEndDate.getDate() + data.coolingOffDays);

  await updateSubscription(data.userId, {
    status: 'cooling_off',
    cooling_off_start_date: new Date(),
    cooling_off_end_date: coolingOffEndDate,
    cooling_off_status: 'active',
    is_annual: true
  });
}

// Check if cooling-off period expired (run daily)
async function checkExpiredCoolingOff() {
  const expiredSubscriptions = await getExpiredCoolingOffSubscriptions();
  
  for (const subscription of expiredSubscriptions) {
    // Activate subscription and charge user
    await activateSubscription(subscription.userId);
    await chargeAnnualSubscription(subscription);
  }
}
```

## 📱 **Testing Guide**

### 1. **Sandbox Testing**
1. Create sandbox test account in App Store Connect
2. Test annual subscription purchase
3. Verify cooling-off period starts
4. Test cancellation during cooling-off
5. Test automatic activation after 14 days

### 2. **Test Scenarios**
- ✅ Purchase annual plan → Cooling-off starts
- ✅ Cancel within 14 days → No charge, access revoked
- ✅ Don't cancel → Charged after 14 days, subscription active
- ✅ Cancel after 14 days → No refund, continues until end of period

## 🚀 **Deployment Checklist**

- [ ] **Apple App Store**: Create annual subscription product
- [ ] **Apple App Store**: Configure introductory offer (14-day free trial)
- [ ] **Backend**: Implement cooling-off APIs
- [ ] **Backend**: Add database schema changes
- [ ] **Backend**: Implement daily job to check expired cooling-off periods
- [ ] **Mobile App**: Test with sandbox environment
- [ ] **Mobile App**: Update app with new annual plan
- [ ] **Testing**: Complete end-to-end testing
- [ ] **Production**: Deploy backend changes
- [ ] **Production**: Submit app update to App Store

## 📋 **User Communication**

### Email Templates Needed:
1. **Cooling-Off Started**: "Your annual subscription is in cooling-off period"
2. **Cooling-Off Reminder**: "3 days left to cancel without charge"
3. **Subscription Activated**: "Your annual subscription is now active"
4. **Cancellation Confirmed**: "Your subscription has been cancelled"

This implementation provides a complete cooling-off period solution that complies with consumer protection laws while maintaining a smooth user experience.
