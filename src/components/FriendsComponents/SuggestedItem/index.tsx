// @flow
import React from 'react';
import {View} from 'react-native';
import {Text, ButtonView, Avatar, AppButton} from '../../../components';
import styles from './styes';
import {Colors, AppStyles, Metrics} from '../../../theme';
import {PLACEHOLDER_IMAGE, svgComponents} from '../../../constants';
import _ from 'lodash';
import {ISuggestedFriendsItem} from '../../../types';
import {SUGGESTED_FRIENDS} from '../../../constants/StringConstants';

interface SuggestedItemProps {
  item: ISuggestedFriendsItem;
  selectedItems: string[];
  onItemPress: () => void;
}

const SuggestedItem: React.FC<SuggestedItemProps> = ({
  item,
  onItemPress,
  selectedItems,
}) => {
  return (
    <View style={styles.itemContainer}>
      <Avatar
        image={!_.isEmpty(item.avatarUrl) ? item.avatarUrl : PLACEHOLDER_IMAGE}
        style={styles.avatarContainer}
        imageStyle={styles.avatar}
      />

      <Text
        numberOfLines={1}
        type="medium"
        size="normal"
        textAlign="center"
        color={Colors.itemColors.titleColor}
        style={styles.textMarginTop}>
        {!_.isEmpty(item.displayName) ? item.displayName : 'No Display Name'}
      </Text>
      <Text
        textAlign="center"
        numberOfLines={1}
        type="medium"
        size="xSmall"
        color={Colors.itemColors.subTitleColor}
        style={AppStyles.mLeftBase}>
        {item.userName}
      </Text>
      <View style={styles.svgContainer}>
        {Object.entries(item.scores).map(([key, value]) => {
          const SvgComponent = svgComponents[key.toLowerCase()];
          if (SvgComponent) {
            return (
              <View key={key} style={styles.reactionContainer}>
                <SvgComponent height={25} width={25} />
              </View>
            );
          }
          return null;
        })}
      </View>

      <ButtonView
        style={[
          styles.inviteButton,
          selectedItems.includes(item.id)
            ? {backgroundColor: Colors.black}
            : {},
        ]}
        onPress={onItemPress}>
        <Text
          size={'xxSmall'}
          color={selectedItems.includes(item.id) ? Colors.white : Colors.black}>
          {SUGGESTED_FRIENDS.INVITE}
        </Text>
      </ButtonView>
    </View>
  );
};

export default SuggestedItem;
