import {StyleSheet} from 'react-native';
import {Colors, Fonts, Metrics} from '../../../theme';

export default StyleSheet.create({
  container: {
    backgroundColor: Colors.gray,
    flex: 1,
  },
  userinfoContainer: {
    backgroundColor: Colors.white,
    paddingVertical: Metrics.baseMargin,
    marginVertical: Metrics.smallMargin,
    marginHorizontal: Metrics.baseMargin,
    borderRadius: Metrics.ratio(10),
  },
  imageContainer: {
    marginLeft: Metrics.ratio(20),
    marginTop: Metrics.ratio(0),
    height: Metrics.ratio(45),
    width: Metrics.ratio(45),
  },
  image: {
    height: Metrics.ratio(45),
    width: Metrics.ratio(45),
  },

  confirmButton: {
    minWidth: Metrics.ratio(120),
    paddingHorizontal: Metrics.ratio(24),
    paddingVertical: Metrics.ratio(6),
    backgroundColor: Colors.BlackButton,
    borderColor: Colors.black,
    borderWidth: 1,
    borderRadius: Metrics.ratio(6),
    marginHorizontal: 0,
  },
  deleteButton: {
    minWidth: Metrics.ratio(120),
    paddingVertical: Metrics.ratio(6),
    marginLeft: Metrics.baseMargin,
    backgroundColor: Colors.gray2,
    paddingHorizontal: Metrics.ratio(24),
  },
  buttonContainer: {
    marginLeft: Metrics.doubleBaseMargin,
    justifyContent: 'center',
    marginTop: Metrics.smallMargin,
    flexDirection: 'row',
  },
  dotButton: {
    flex: 1,
    justifyContent: 'flex-start',
    marginRight: Metrics.baseMargin,
    alignItems: 'flex-end',
  },
});
