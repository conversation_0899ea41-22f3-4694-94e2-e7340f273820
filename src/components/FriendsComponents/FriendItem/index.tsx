import {View} from 'react-native';
import {AppButton, Avatar, Text, ButtonView} from '../../';
import {AppStyles, Colors, Metrics} from '../../../theme';
import {PLACEHOLDER_IMAGE} from '../../../constants';
import {HORIZONTAL_DOT} from '../../../constants/AssetSVGConstants';
import {FOLLOWERS} from '../../../types';
import styles from './styles';
import {FRIENDS} from '../../../constants/StringConstants';

interface FriendsListItem {
  item: FOLLOWERS;
  isButton: boolean;
  moreOptionsPress: () => void;
  isMoreOptionAvailable: boolean;
  onConfirmPress?: (item: FOLLOWERS) => void;
  onDeletePress: () => void;
  confirmLoading: boolean;
  deleteLoading: boolean;
  onItemPress?: (item: FOLLOWERS) => void;
}

const FriendItem: React.FC<FriendsListItem> = ({
  isButton,
  isMoreOptionAvailable,
  item,
  moreOptionsPress,
  onConfirmPress,
  onDeletePress,
  confirmLoading,
  deleteLoading,
  onItemPress,
}) => {
  return (
    <View style={styles.userinfoContainer}>
      <View style={AppStyles.flexRow}>
        <Avatar
          image={item?.avatarUrl ?? PLACEHOLDER_IMAGE}
          style={styles.imageContainer}
          imageStyle={styles.image}
        />
        <ButtonView
          style={AppStyles.alignStart}
          onPress={() => onItemPress && onItemPress(item)}>
          <Text
            color={Colors.itemColors.titleColor}
            textAlign="center"
            style={AppStyles.mLeft10}
            type="bold"
            size={'buttonText'}>
            {item?.displayName}
          </Text>
          <Text
            color={Colors.text.placeHolderTextColor}
            style={AppStyles.mLeft10}
            type="base"
            size={'xSmall'}>
            {item?.userName ?? ''}
          </Text>
        </ButtonView>
        {isMoreOptionAvailable && (
          <ButtonView onPress={moreOptionsPress} style={styles.dotButton}>
            <HORIZONTAL_DOT />
          </ButtonView>
        )}
      </View>

      {isButton && (
        <View style={styles.buttonContainer}>
          <AppButton
            isLoading={confirmLoading}
            buttonStye={styles.confirmButton}
            text={FRIENDS.CONFIRM}
            onPress={() => {
              onConfirmPress && onConfirmPress(item);
            }}
            textColor={Colors.white}
            type={'semi_bold'}
          />
          <AppButton
            isLoading={deleteLoading}
            buttonStye={styles.deleteButton}
            text={FRIENDS.DELETE}
            textColor={Colors.black}
            onPress={onDeletePress}
            type={'semi_bold'}
          />
        </View>
      )}
    </View>
  );
};

export default FriendItem;
