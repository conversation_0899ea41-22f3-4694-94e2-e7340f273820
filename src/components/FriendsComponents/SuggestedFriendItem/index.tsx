// @flow
import React from 'react';
import {View} from 'react-native';
import {Text, ButtonView, Avatar} from '../../../components';
import styles from './styes';
import {Colors, AppStyles, Metrics} from '../../../theme';
import {TICK_ICON} from '../../../constants/AssetSVGConstants';
import {PLACEHOLDER_IMAGE} from '../../../constants';
import _ from 'lodash';
import {User} from '../../../types';

interface ItemProps {
  item: User;
  selectedItems: string[];
  onItemPress: () => void;
}

const SuggestedFriendItem: React.FC<ItemProps> = ({
  item,
  onItemPress,
  selectedItems,
}) => {
  return (
    <ButtonView style={styles.itemContainer} onPress={onItemPress}>
      <View style={styles.subContainer}>
        <Avatar
          image={
            !_.isEmpty(item.avatarUrl) ? item.avatarUrl : PLACEHOLDER_IMAGE
          }
          style={{marginTop: 0, height: 45, width: 45}}
          imageStyle={{height: 45, width: 45}}
        />
        <View>
          <Text
            numberOfLines={2}
            type="medium"
            size="normal"
            color={Colors.itemColors.titleColor}
            style={AppStyles.mLeftBase}>
            {!_.isEmpty(item.displayName)
              ? item.displayName
              : 'No Display Name'}
          </Text>
          <Text
            numberOfLines={2}
            type="medium"
            size="xSmall"
            color={Colors.itemColors.subTitleColor}
            style={AppStyles.mLeftBase}>
            {item.userName}
          </Text>
        </View>
        <View style={styles.tickStyle}>
          {!selectedItems.includes(item.id) ? (
            <View
              style={{
                borderRadius: Metrics.ratio(100),
                height: Metrics.ratio(22),
                width: Metrics.ratio(22),
                backgroundColor: Colors.white,
                borderColor: Colors.borderColors.activeBorderColor,
              }}
            />
          ) : (
            <TICK_ICON />
          )}
        </View>
      </View>
    </ButtonView>
  );
};

export default SuggestedFriendItem;
