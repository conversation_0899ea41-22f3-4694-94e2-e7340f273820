// @flow
import {StyleSheet} from 'react-native';
import {Colors, Metrics, AppStyles} from '../../../theme';

export default StyleSheet.create({
  itemContainer: {
    marginVertical: Metrics.smallMargin,
    backgroundColor: Colors.itemColors.itemBackgroundColor,
    borderRadius: Metrics.ratio(6),
    alignItems: 'center',
    marginHorizontal: Metrics.baseMargin,
    padding: Metrics.baseMargin,
    borderColor: Colors.itemColors.itemBorderColor,
    borderWidth: Metrics.ratio(1),
    shadowColor: '#000',
  },
  subContainer: {
    alignSelf: 'center',
    justifyContent: 'center',
    flex: 8,
    alignItems: 'center',
    flexDirection: 'row',
  },
  tickStyle: {flex: 2, alignItems: 'flex-end'},
  extraMargin: {marginLeft: Metrics.ratio(50)},
});
