import React from 'react';
import {View} from 'react-native';
import {AppStyles, Colors} from '../../../theme';
import {ButtonView, Text, BottomSheetModal, AppButton} from '../../';
import {FRIENDS} from '../../../constants/StringConstants';
import styles from './styles';
import {
  CROSS_YELLOW,
  MESSAGE_GREEN,
  PROFILE_ICON,
  TRASH,
} from '../../../constants/AssetSVGConstants';
import {FOLLOWERS} from '../../../types';

interface BottomSheetModalProps {
  hideModal: () => void;
  onMessagePress: () => void;
  onRemovePress: () => void;
  onBlockPress: () => void;
  onBlockUserPress: () => void;
  visibleModal: boolean;
  selectedItem?: FOLLOWERS;
  isBlockUser?: boolean;
}

const BottomModal: React.FC<BottomSheetModalProps> = ({
  hideModal,
  onMessagePress,
  onRemovePress,
  onBlockPress,
  visibleModal,
  selectedItem,
  onBlockUserPress,
  isBlockUser,
}) => {
  return (
    <BottomSheetModal
      customStyle={!isBlockUser ? styles.modalHeight : styles.modalHeightBlock}
      visible={visibleModal}
      onClose={hideModal}
      onBackdropPress={hideModal}>
      <View>
        {!isBlockUser && (
          <>
            <ButtonView
              onPress={onRemovePress}
              style={styles.commonButtonStyle}>
              <TRASH />
              <Text
                color={Colors.itemColors.subTitleColor}
                style={AppStyles.mLeft10}
                size={'medium'}
                type="medium">
                {FRIENDS.REMOVE}
              </Text>
            </ButtonView>
            <ButtonView
              onPress={onMessagePress}
              style={styles.message}
              disabled={selectedItem?.isUserBlocked}>
              <MESSAGE_GREEN />
              <Text
                color={
                  !selectedItem?.isUserBlocked
                    ? Colors.itemColors.subTitleColor
                    : Colors.ChatColors.unreadItem
                }
                style={AppStyles.mLeft10}
                size={'medium'}
                type="medium">
                {selectedItem?.blockedMessages
                  ? FRIENDS.UN_BLOCK_MESSAGE
                  : FRIENDS.BLOCK_MESSAGE}
              </Text>
            </ButtonView>

            <ButtonView
              onPress={onBlockPress}
              style={styles.commonButtonStyle}
              disabled={selectedItem?.isUserBlocked}>
              <CROSS_YELLOW />
              <Text
                color={
                  !selectedItem?.isUserBlocked
                    ? Colors.itemColors.subTitleColor
                    : Colors.ChatColors.unreadItem
                }
                style={AppStyles.mLeft10}
                size={'medium'}
                type="medium">
                {selectedItem?.blockedImprints
                  ? FRIENDS.UN_BLOCK
                  : FRIENDS.BLOCK}
              </Text>
            </ButtonView>
          </>
        )}
        <ButtonView onPress={onBlockUserPress} style={styles.hideImprint}>
          <PROFILE_ICON height={20} width={20} />
          <Text
            color={Colors.itemColors.subTitleColor}
            style={AppStyles.mLeft10}
            size={'medium'}
            type="medium">
            {selectedItem?.isUserBlocked || isBlockUser
              ? FRIENDS.UN_BLOCK_USER
              : FRIENDS.BLOCK_USER}
          </Text>
        </ButtonView>
      </View>
    </BottomSheetModal>
  );
};
export default BottomModal;
