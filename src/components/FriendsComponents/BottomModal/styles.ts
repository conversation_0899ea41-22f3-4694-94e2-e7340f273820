import {StyleSheet} from 'react-native';
import {Colors, Fonts, Metrics} from '../../../theme';

export default StyleSheet.create({
  message: {
    height: Metrics.ratio(50),
    borderRadius: Metrics.ratio(6),
    backgroundColor: Colors.itemColors.itemBackgroundColor,
    alignItems: 'center',
    marginHorizontal: Metrics.ratio(24),
    flexDirection: 'row',
    paddingHorizontal: Metrics.ratio(10),
  },

  commonButtonStyle: {
    height: Metrics.ratio(50),
    borderRadius: Metrics.ratio(6),
    backgroundColor: Colors.itemColors.itemBackgroundColor,
    alignItems: 'center',
    marginHorizontal: Metrics.ratio(24),
    flexDirection: 'row',
    marginVertical: Metrics.baseMargin,
    paddingHorizontal: Metrics.ratio(10),
  },
  hideImprint: {
    height: Metrics.ratio(50),
    paddingHorizontal: Metrics.ratio(10),
    borderRadius: Metrics.ratio(6),
    backgroundColor: Colors.itemColors.itemBackgroundColor,
    alignItems: 'center',
    marginHorizontal: Metrics.ratio(24),
    flexDirection: 'row',
  },

  modalHeight: {height: '35%'},
  modalHeightBlock: {height: '13%'},
});
