import {StyleSheet} from 'react-native';
import {Colors, Metrics} from '../../../theme';

export const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    marginHorizontal: Metrics.baseMargin,
  },
  activeButton: {
    borderRadius: Metrics.ratio(100),
    backgroundColor: Colors.black1,
    paddingHorizontal: Metrics.ratio(10),
    paddingVertical: Metrics.ratio(5),
    alignItems: 'center',
    justifyContent: 'center',
  },

  requestButton: {
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: Metrics.smallMargin,
    backgroundColor: Colors.white,
    flexDirection: 'row',
    paddingHorizontal: Metrics.ratio(10),
    paddingVertical: Metrics.ratio(2),
    borderRadius: Metrics.ratio(6),
  },
});
