import {View, ScrollView} from 'react-native';
import {styles} from './styles';
import {ButtonView, Text} from '../..';
import {FRIENDS} from '../../../constants/StringConstants';
import {Colors} from '../../../theme';
import {FOLLOWERS, TABS_ENUMS} from '../../../types';
import {useRef} from 'react';

interface TabsProps {
  onFriendsListPress: () => void;
  onRequestedFriendsPress: () => void;
  onBlockedFriendsPress: () => void;
  onSuggestedFriendsPress: () => void;
  friendsList: FOLLOWERS[];
  requestedList: FOLLOWERS[];
  blockedList: FOLLOWERS[];
  selectedTab:
    | TABS_ENUMS.ALL
    | TABS_ENUMS.REQUESTED
    | TABS_ENUMS.BLOCKED
    | TABS_ENUMS.SUGGESTED;
}

const FriendTabs: React.FC<TabsProps> = ({
  onFriendsList<PERSON>ress,
  onRequestedFriendsPress,
  onBlocked<PERSON>riends<PERSON>ress,
  onSuggested<PERSON>riends<PERSON><PERSON>,
  friendsList,
  requestedList,
  blockedList,
  selectedTab,
}) => {
  const scrollRef = useRef<ScrollView>(null);
  const buttonRefs = useRef<Record<string, View | null>>({
    ALL: null,
    REQUESTED: null,
    BLOCKED: null,
    SUGGESTED: null,
  });

  const scrollToTab = (tab: string) => {
    buttonRefs.current[tab]?.measureLayout(
      scrollRef.current?.getScrollableNode?.() ?? 0,
      x => {
        scrollRef.current?.scrollTo({x: x - 20, animated: true});
      },
      () => {},
    );
  };

  const getButtonStyle = (tab: string) => [
    styles.requestButton,
    {
      backgroundColor: selectedTab === tab ? Colors.black1 : Colors.white,
    },
  ];

  const getTextColor = (tab: string) =>
    selectedTab === tab
      ? Colors.white
      : Colors.tabsTextColor.inActiveFriendList;

  const formatCount = (count?: number) =>
    !count ? '0' : count.toString().padStart(2, '0');

  const handlePress = (
    tab:
      | TABS_ENUMS.ALL
      | TABS_ENUMS.REQUESTED
      | TABS_ENUMS.BLOCKED
      | TABS_ENUMS.SUGGESTED,
    callback: () => void,
  ) => {
    if (tab === TABS_ENUMS.REQUESTED) {
      scrollRef.current?.scrollTo({x: 0, animated: true});
      callback();
    } else {
      scrollToTab(tab);
      callback();
    }
  };

  return (
    <ScrollView
      ref={scrollRef}
      style={styles.container}
      horizontal
      showsHorizontalScrollIndicator={false}>
      <View
        ref={ref => (buttonRefs.current[TABS_ENUMS.ALL] = ref)}
        collapsable={false}>
        <ButtonView
          style={getButtonStyle(TABS_ENUMS.ALL)}
          onPress={() => handlePress(TABS_ENUMS.ALL, onFriendsListPress)}>
          <Text
            size="buttonText"
            color={getTextColor(TABS_ENUMS.ALL)}
            type="semi_bold">
            {`${FRIENDS.ALL}(${formatCount(friendsList?.length)})`}
          </Text>
        </ButtonView>
      </View>
      <View
        ref={ref => (buttonRefs.current[TABS_ENUMS.REQUESTED] = ref)}
        collapsable={false}>
        <ButtonView
          style={getButtonStyle(TABS_ENUMS.REQUESTED)}
          onPress={() =>
            handlePress(TABS_ENUMS.REQUESTED, onRequestedFriendsPress)
          }>
          <Text
            size="buttonText"
            color={getTextColor(TABS_ENUMS.REQUESTED)}
            type="semi_bold">
            {`${FRIENDS.REQUEST}(${formatCount(requestedList?.length)})`}
          </Text>
        </ButtonView>
      </View>
      <View
        ref={ref => (buttonRefs.current[TABS_ENUMS.BLOCKED] = ref)}
        collapsable={false}>
        <ButtonView
          style={getButtonStyle(TABS_ENUMS.BLOCKED)}
          onPress={onBlockedFriendsPress}>
          <Text
            size="buttonText"
            color={getTextColor(TABS_ENUMS.BLOCKED)}
            type="semi_bold">
            {`${FRIENDS.BLOCK_USERS}(${formatCount(blockedList?.length)})`}
          </Text>
        </ButtonView>
      </View>
      <View
        ref={ref => (buttonRefs.current[TABS_ENUMS.SUGGESTED] = ref)}
        collapsable={false}>
        <ButtonView
          style={getButtonStyle(TABS_ENUMS.SUGGESTED)}
          onPress={() =>
            handlePress(TABS_ENUMS.SUGGESTED, onSuggestedFriendsPress)
          }>
          <Text
            size="buttonText"
            color={getTextColor(TABS_ENUMS.SUGGESTED)}
            type="semi_bold">
            {`${FRIENDS.SUGGESTED_FRIENDS}`}
          </Text>
        </ButtonView>
      </View>
    </ScrollView>
  );
};

export default FriendTabs;
