// @flow
import {StyleSheet} from 'react-native';
import {Colors, Metrics} from '../../theme';

export default StyleSheet.create({
  itemContainer: {
    marginVertical: Metrics.ratio(4),
    backgroundColor: Colors.white,
    borderRadius: Metrics.ratio(6),
    alignItems: 'center',
    marginLeft: Metrics.ratio(4),
    marginRight: Metrics.ratio(4),
    padding: Metrics.baseMargin,
    borderColor: Colors.itemColors.itemBorderColor,
    borderWidth: Metrics.ratio(1),
    shadowColor: '#000',
    width: Metrics.screenWidth * 0.45,
  },
  subContainer: {
    alignSelf: 'center',
    justifyContent: 'center',
  },
  extraMargin: {marginLeft: Metrics.ratio(50)},
  avatarContainer: {
    marginTop: Metrics.ratio(0),
    height: Metrics.ratio(45),
    width: Metrics.ratio(45),
  },
  avatar: {height: 45, width: 45},
  reactionContainer: {
    marginLeft: Metrics.ratio(10),
    flexDirection: 'row',
    width: Metrics.ratio(33),
    height: Metrics.ratio(33),
    borderRadius: Metrics.ratio(50),
    backgroundColor: '#EBEBEB',
    justifyContent: 'center',
    alignItems: 'center',
  },
  inviteButton: {
    width: Metrics.ratio(130),
    alignItems: 'center',
    backgroundColor: Colors.gray2,
    borderRadius: Metrics.ratio(5),
    marginVertical: Metrics.ratio(10),
    paddingVertical: Metrics.ratio(3),
  },
  textMarginTop: {marginTop: Metrics.ratio(5)},
  svgContainer: {
    flexDirection: 'row',
    margin: Metrics.ratio(10),
    alignSelf: 'center',
  },
});
