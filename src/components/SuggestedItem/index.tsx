// @flow
import React from 'react';
import {View} from 'react-native';
import {Text, ButtonView, Avatar} from '../../components';
import styles from './styes';
import {Colors, AppStyles} from '../../theme';
import {PLACEHOLDER_IMAGE, svgComponents} from '../../constants';
import _ from 'lodash';
import {FOLLOWERS, ISuggestedFriendsItem} from '../../types';
import {SUGGESTED_FRIENDS} from '../../constants/StringConstants';

interface FriendsSuggestionsItemProps {
  item: ISuggestedFriendsItem;
  onItemPress: () => void;
  navigateToProfile?: (item: FOLLOWERS) => void;
}

const FriendsSuggestionsItem: React.FC<FriendsSuggestionsItemProps> = ({
  item,
  onItemPress,
  navigateToProfile,
}) => {
  return (
    <View style={styles.itemContainer}>
      <ButtonView onPress={() => navigateToProfile && navigateToProfile(item)}>
        <Avatar
          image={
            !_.isEmpty(item.avatarUrl) ? item.avatarUrl : PLACEHOLDER_IMAGE
          }
          style={styles.avatarContainer}
          imageStyle={styles.avatar}
        />

        <Text
          numberOfLines={1}
          type="medium"
          size="normal"
          textAlign="center"
          color={Colors.itemColors.titleColor}
          style={styles.textMarginTop}>
          {!_.isEmpty(item.displayName) ? item.displayName : 'No Display Name'}
        </Text>
      </ButtonView>
      <Text
        textAlign="center"
        numberOfLines={1}
        type="medium"
        size="xSmall"
        color={Colors.itemColors.subTitleColor}
        style={AppStyles.mLeftBase}>
        {item.userName}
      </Text>
      <View style={styles.svgContainer}>
        {Object.entries(item.scores).map(([key, value]) => {
          const SvgComponent = svgComponents[key.toLowerCase()];
          if (SvgComponent) {
            return (
              <View key={key} style={styles.reactionContainer}>
                <SvgComponent height={25} width={25} />
              </View>
            );
          }
          return null;
        })}
      </View>

      <ButtonView style={[styles.inviteButton]} onPress={onItemPress}>
        <Text size={'xxSmall'} color={Colors.black}>
          {SUGGESTED_FRIENDS.INVITE}
        </Text>
      </ButtonView>
    </View>
  );
};

export default FriendsSuggestionsItem;
