import {FlatList, View} from 'react-native';
import {styles} from './styles';
import {Text} from '../';
import {ButtonView} from '..';
import {AppStyles, Colors, Metrics} from '../../theme';
import {useRef, useState} from 'react';
import {PROFILE_OPTIONS} from '../../constants';
import {IProfileOptions, UserState} from '../../types';
import {isUserSubscribed} from '../../util';

interface ProfileOptionsProps {
  profileOptions: IProfileOptions[];
  onItemPress: (selectedItem: IProfileOptions) => void;
  selectedItem?: IProfileOptions;
  isOtherProfile?: boolean;
  user: UserState;
}

const ProfileOptions: React.FC<ProfileOptionsProps> = ({
  onItemPress,
  selectedItem,
  isOtherProfile = false,
  profileOptions,
  user,
}) => {
  const [profileList, setProfileList] =
    useState<IProfileOptions[]>(PROFILE_OPTIONS);
  const flatListRef = useRef<any>();

  const handlePress = (item: IProfileOptions, index: number) => {
    flatListRef.current?.scrollToIndex({
      animated: true,
      index: index,
      viewPosition: 0.5,
    });
    onItemPress(item);
  };

  return (
    <View
      style={{
        marginVertical: Metrics.baseMargin,
      }}>
      <FlatList
        ref={flatListRef}
        showsHorizontalScrollIndicator={false}
        horizontal
        style={{marginHorizontal: Metrics.baseMargin}}
        data={profileOptions}
        extraData={profileOptions}
        renderItem={({item, index}) => {
          const subscribed = isUserSubscribed(user);
          const isRestrictedForUnsubscribed =
            !subscribed && (item.id === 3 || item.id === 4);
          const isOtherRestricted =
            isOtherProfile && (item.id === 3 || item.id === 4);

          const textColor =
            isRestrictedForUnsubscribed || isOtherRestricted
              ? Colors.whit1
              : selectedItem == item
              ? Colors.black1
              : Colors.text.placeHolderTextColor;
          return (
            <ButtonView
              style={[
                styles.itemContainer,
                selectedItem == item
                  ? styles.selectedColor
                  : styles.unselectedColor,
              ]}
              disabled={isRestrictedForUnsubscribed || isOtherRestricted}
              onPress={() => handlePress(item, index)}>
              <Text
                style={AppStyles.mLeft5}
                color={textColor}
                size={'buttonText'}
                type="medium">
                {item.name}
              </Text>
            </ButtonView>
          );
        }}
      />
    </View>
  );
};

export default ProfileOptions;
