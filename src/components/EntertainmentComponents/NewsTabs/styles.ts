import {StyleSheet} from 'react-native';
import {Colors, Metrics} from '../../../theme';

export const styles = StyleSheet.create({
  container: {
    paddingTop: Metrics.ratio(10),
    alignItems: 'center',
    justifyContent: 'space-around',
    flexDirection: 'row',
    backgroundColor: Colors.white,
  },
  tabChild: {
    justifyContent: 'center',
    height: Metrics.ratio(52),
    alignItems: 'center',
    flex: 1,
  },
  selectedTab: {
    borderBottomWidth: 1.5,
  },
  whiteColor: {backgroundColor: Colors.white, marginBottom: Metrics.ratio(10)},
});
