import {StyleSheet} from 'react-native';
import {Colors, Metrics, Fonts, AppStyles} from '../../../theme';

export default StyleSheet.create({
  container: {
    flexDirection: 'row',
    paddingBottom: Metrics.ratio(20),
  },
  nameContainer: {
    marginLeft: Metrics.ratio(15),
    flex: 1,
  },
  name: {
    fontFamily: Fonts.type.bold,
    fontSize: Metrics.ratio(16),
    color: Colors.black,
    paddingTop: Metrics.ratio(2),
    maxWidth: Metrics.screenWidth * 0.4,
  },
  timeContainer: {
    paddingTop: Metrics.ratio(2),
    flexDirection: 'row',
  },
  time: {
    fontFamily: Fonts.type.regular,
    fontSize: Metrics.ratio(12),
    color: Colors.black,
    paddingLeft: Metrics.ratio(5),
  },
  rightContainer: {
    marginLeft: Metrics.ratio(4),
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  image: {
    width: Metrics.ratio(45),
    height: Metrics.ratio(45),
    borderRadius: Metrics.ratio(100),
  },
  imagePadding: {
    paddingLeft: Metrics.ratio(6),
  },
});
