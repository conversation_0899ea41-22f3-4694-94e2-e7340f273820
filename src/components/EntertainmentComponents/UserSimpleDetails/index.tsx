import React from 'react';
import {View, Text, Image, Pressable} from 'react-native';
import styles from './styles';
import {
  EARTH_ICON,
  LAUGHTER_GLOBAL,
  LAUGHTER_GRAY,
  LIFE,
  LIFE_GRAY,
  LOVE,
  LOVE_GRAY,
  PURPOSE,
  PURPOSE_GRAY,
  RESPECT,
  RESPECT_GRAY,
  SAFETY,
  SAFETY_GRAY,
} from '../../../constants/AssetSVGConstants';
import util from '../../../util';
import {DATE_FORMAT_7} from '../../../constants';

interface IUserSimpleDetailsProps {
  avatarUrl: string;
  displayName: string;
  valueType: string;
  isGlobal: boolean;
  onUserNameClick?: () => void;
}

const UserSimpleDetails: React.FC<IUserSimpleDetailsProps> = ({
  avatarUrl,
  displayName,
  valueType,
  isGlobal,
  onUserNameClick,
}) => {
  const renderSVG = () => {
    switch (valueType) {
      case 'LAUGHTER':
        return isGlobal ? (
          <LAUGHTER_GLOBAL width={40} height={40} />
        ) : (
          <LAUGHTER_GRAY width={40} height={40} />
        );
      case 'LIFE':
        return isGlobal ? (
          <LIFE width={40} height={40} />
        ) : (
          <LIFE_GRAY width={40} height={40} />
        );
      case 'LOVE':
        return isGlobal ? (
          <LOVE width={40} height={40} />
        ) : (
          <LOVE_GRAY width={40} height={40} />
        );
      case 'PURPOSE':
        return isGlobal ? (
          <PURPOSE width={40} height={40} />
        ) : (
          <PURPOSE_GRAY width={40} height={40} />
        );
      case 'RESPECT':
        return isGlobal ? (
          <RESPECT width={40} height={40} />
        ) : (
          <RESPECT_GRAY width={40} height={40} />
        );
      case 'SAFETY':
        return isGlobal ? (
          <SAFETY width={40} height={40} />
        ) : (
          <SAFETY_GRAY width={40} height={40} />
        );
      default:
        return null;
    }
  };

  return (
    <View style={styles.container}>
      <Image source={{uri: avatarUrl}} style={styles.image} />
      <View style={styles.nameContainer}>
        <Pressable onPress={onUserNameClick}>
          <Text style={styles.name} numberOfLines={1}>
            {displayName}
          </Text>
        </Pressable>
        <View style={styles.timeContainer}>
          <EARTH_ICON />
          <Text style={styles.time}>
            {util.getFormattedDateTime(new Date(), DATE_FORMAT_7)}
          </Text>
        </View>
      </View>
      <View style={styles.rightContainer}>{renderSVG()}</View>
    </View>
  );
};

export default UserSimpleDetails;
