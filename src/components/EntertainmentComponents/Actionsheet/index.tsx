import React, {forwardRef, useImperativeHandle} from 'react';
import {useActionSheet} from '@expo/react-native-action-sheet';
import {AskRudiOptions} from '../../../constants';

type AskRudiActonSheetProps = {
  onSelect: (selectedOption: string) => void;
};

export type AskRudiActonSheetRef = {
  handlePress: () => void;
};

const AskRudiActionSheetComponent = forwardRef<
  AskRudiActonSheetRef,
  AskRudiActonSheetProps
>(({onSelect}, ref) => {
  const {showActionSheetWithOptions} = useActionSheet();

  const handlePress = () => {
    const options = AskRudiOptions;
    const cancelButtonIndex = 3;

    showActionSheetWithOptions(
      {
        options,
        cancelButtonIndex,
        title: 'Ask Rudi',
      },
      (selectedIndex?: number) => {
        if (
          selectedIndex !== undefined &&
          selectedIndex !== cancelButtonIndex
        ) {
          onSelect(options[selectedIndex]);
        }
      },
    );
  };

  useImperativeHandle(ref, () => ({
    handlePress,
  }));

  return null;
});

export default AskRudiActionSheetComponent;
