import {FlatList, View} from 'react-native';
import {styles} from './styles';
import {Text} from '../../';
import {ButtonView} from '../../';
import {AppStyles, Colors, Metrics} from '../../../theme';
import {useRef} from 'react';
import {IProfileOptions} from '../../../types';

interface ProfileOptionsProps {
  entertainmentsList: IProfileOptions[];
  onItemPress: (selectedItem: IProfileOptions) => void;
  selectedItem?: IProfileOptions;
  isOtherProfile?: boolean;
}

const EntertainmentList: React.FC<ProfileOptionsProps> = ({
  onItemPress,
  selectedItem,
  entertainmentsList,
}) => {
  const flatListRef = useRef<any>();

  const handlePress = (item: IProfileOptions, index: number) => {
    flatListRef.current?.scrollToIndex({
      animated: true,
      index: index,
      viewPosition: 0.5,
    });
    onItemPress(item);
  };

  return (
    <View
      style={{
        marginVertical: Metrics.baseMargin,
      }}>
      <FlatList
        ref={flatListRef}
        showsHorizontalScrollIndicator={false}
        horizontal
        style={{marginHorizontal: Metrics.baseMargin}}
        data={entertainmentsList}
        extraData={entertainmentsList}
        renderItem={({item, index}) => {
          const textColor =
            selectedItem == item
              ? Colors.black1
              : Colors.text.placeHolderTextColor;
          return (
            <ButtonView
              style={[
                styles.itemContainer,
                selectedItem == item
                  ? styles.selectedColor
                  : styles.unselectedColor,
              ]}
              onPress={() => handlePress(item, index)}>
              <Text
                style={AppStyles.mLeft5}
                color={textColor}
                size={'buttonText'}
                type="medium">
                {item.name}
              </Text>
            </ButtonView>
          );
        }}
      />
    </View>
  );
};

export default EntertainmentList;
