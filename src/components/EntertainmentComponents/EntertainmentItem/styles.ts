import {StyleSheet} from 'react-native';
import {Metrics, Colors} from '../../../theme';

export const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.white,
    marginHorizontal: Metrics.ratio(24),
    marginVertical: Metrics.ratio(12),
    padding: Metrics.ratio(12),
    borderRadius: Metrics.ratio(12),
  },
  header: {
    overflow: 'visible',
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  title: {
    width: Metrics.screenWidth * 0.5,
  },
  actions: {
    // backgroundColor: 'red',
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'flex-end',
  },
  button: {
    // backgroundColor: Colors.red,
    borderRadius: Metrics.ratio(6),
    backgroundColor: Colors.gray2,
    paddingVertical: Metrics.ratio(4),
    paddingHorizontal: Metrics.ratio(12),
  },
  buttonText: {
    marginHorizontal: Metrics.ratio(10),
    marginVertical: Metrics.ratio(4),
  },
  imagePadding: {
    paddingRight: Metrics.ratio(8),
  },
});
