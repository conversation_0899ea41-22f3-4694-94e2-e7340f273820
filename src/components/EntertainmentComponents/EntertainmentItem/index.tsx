import React from 'react';
import {View} from 'react-native';
import {Colors, Fonts, AppStyles} from '../../../theme';
import {ButtonView, Text} from '../../';
import {
  ENTERTAINMENTS_ENUMS,
  IEntertainmentContentResponse,
} from '../../../types';
import {
  ENTERTAINMENTS,
  IMPRINT_SCORES,
} from '../../../constants/StringConstants';
import {
  LAUGHTER,
  LIFE,
  LOVE,
  PURPOSE,
  RESPECT,
  SAFETY,
} from '../../../constants/AssetSVGConstants';
import {styles} from './styles';

interface EntertainmentItemProps {
  item: IEntertainmentContentResponse;
  onSharePress: (item: IEntertainmentContentResponse) => void;
}

const EntertainmentItem: React.FC<EntertainmentItemProps> = ({
  item,
  onSharePress,
}) => {
  const renderSVG = (valueType: string) => {
    switch (valueType) {
      case IMPRINT_SCORES.LAUGHTER.toLocaleUpperCase():
        return (
          <View style={styles.imagePadding}>
            <LAUGHTER width={26} height={26} />
          </View>
        );
      case IMPRINT_SCORES.LIFE.toLocaleUpperCase():
        return (
          <View style={styles.imagePadding}>
            <LIFE width={26} height={26} />
          </View>
        );
      case IMPRINT_SCORES.LOVE.toLocaleUpperCase():
        return (
          <View style={styles.imagePadding}>
            <LOVE width={26} height={26} />
          </View>
        );
      case IMPRINT_SCORES.PURPOSE.toLocaleUpperCase():
        return (
          <View style={styles.imagePadding}>
            <PURPOSE width={26} height={26} />
          </View>
        );
      case IMPRINT_SCORES.RESPECT.toLocaleUpperCase():
        return (
          <View style={styles.imagePadding}>
            <RESPECT width={26} height={26} />
          </View>
        );
      case IMPRINT_SCORES.SAFETY.toLocaleUpperCase():
        return (
          <View style={styles.imagePadding}>
            <SAFETY width={26} height={26} />
          </View>
        );
      default:
        return <></>;
    }
  };
  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text
          color={Colors.black}
          size={'buttonText'}
          type={'semi_bold'}
          style={styles.title}>
          {item.title}
        </Text>
        <View style={styles.actions}>
          {renderSVG(item.valueType)}
          <ButtonView style={styles.button} onPress={() => onSharePress(item)}>
            <Text
              color={Colors.black}
              size={Fonts.size.xxSmall}
              style={styles.buttonText}>
              {ENTERTAINMENTS.SHARE}
            </Text>
          </ButtonView>
        </View>
      </View>
      <Text
        color={Colors.text.modalText}
        size={'xSmall'}
        style={AppStyles.marginVerticalBase}>
        {item.body}
      </Text>
      {item.type === ENTERTAINMENTS_ENUMS.QUOTES && (
        <Text color={Colors.text.modalText} size={'xSmall'}>
          {item.author}
        </Text>
      )}
    </View>
  );
};

export default EntertainmentItem;
