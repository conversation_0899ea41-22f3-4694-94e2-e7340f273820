import {StyleSheet} from 'react-native';
import {Metrics, Colors, Fonts} from '../../../theme';

export const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.white,
    marginHorizontal: Metrics.ratio(24),
    marginVertical: Metrics.ratio(12),
    padding: Metrics.ratio(16),
    borderRadius: Metrics.ratio(12),
  },
  header: {
    overflow: 'visible',
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  title: {
    width: Metrics.screenWidth * 0.75,
  },

  button: {
    marginTop: Metrics.ratio(10),
    alignItems: 'center',
    flex: 1,
    borderRadius: Metrics.ratio(6),
    backgroundColor: Colors.gray2,
    paddingVertical: Metrics.ratio(4),
    paddingHorizontal: Metrics.ratio(12),
  },
  buttonText: {
    marginHorizontal: Metrics.ratio(10),
    marginVertical: Metrics.ratio(4),
  },
  imagePadding: {},
  bottomContainer: {flexDirection: 'row', flex: 1},
  buttonLeft: {marginLeft: Metrics.ratio(20), backgroundColor: Colors.black},
  shareIcon: {
    padding: Metrics.ratio(5),
    borderRadius: Metrics.ratio(2),
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.gray,
  },
  symbol: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: Metrics.ratio(10),
    paddingVertical: Metrics.ratio(5),
    marginRight: Metrics.ratio(10),
    borderRadius: Metrics.ratio(16),
    backgroundColor: '#EBEBEB',
  },

  logoText: {
    paddingLeft: Metrics.ratio(10),
    marginRight: Metrics.ratio(10),
  },
  itemContainer: {
    marginRight: Metrics.ratio(20),
    backgroundColor: '#F1F1F2',
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    borderRadius: Metrics.ratio(100),
    paddingHorizontal: Metrics.ratio(10),
    paddingVertical: Metrics.ratio(2),
  },
  subContainer: {
    alignSelf: 'flex-end',
    flexDirection: 'row',
    //  justifyContent: 'space-between',
    marginBottom: Metrics.ratio(10),
  },
  time: {
    fontFamily: Fonts.type.medium,
    fontSize: Metrics.ratio(12),
    color: '#626883',
    paddingLeft: Metrics.ratio(5),
  },
  divider: {
    width: '100%',
    height: 1,
    backgroundColor: '#E5E6EB',
  },
  dot: {
    marginLeft: Metrics.ratio(10),
    width: 3,
    height: 3,
    backgroundColor: '#626883',
  },
  sliderContainer: {
    marginTop: Metrics.ratio(20),
    flexDirection: 'row',
  },
  negativeMargin: {marginTop: -2},
});
