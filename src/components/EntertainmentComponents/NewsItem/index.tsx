import React from 'react';
import {View} from 'react-native';
import {Colors, Fonts, AppStyles, Metrics} from '../../../theme';
import {ButtonView, Text} from '../../';
import {IEntertainmentContentResponse, INewsResponse} from '../../../types';
import {IMPRINT_SCORES, NEWS} from '../../../constants/StringConstants';
import {
  LAUGHTER,
  LIFE,
  LOVE,
  PURPOSE,
  RESPECT,
  SAFETY,
  SHARE_ICON,
} from '../../../constants/AssetSVGConstants';
import {styles} from './styles';
import util from '../../../util';
import BiasSlider from '../../BiasSlider';

interface NewsItemProps {
  item: INewsResponse;
  askRudiPress: (item: INewsResponse) => void;
  readMorePress: (item: INewsResponse) => void;
  onSharePress: (item: IEntertainmentContentResponse) => void;
}

const NewsItem: React.FC<NewsItemProps> = ({
  item,
  askRudiPress,
  readMorePress,
  onSharePress,
}) => {
  const renderSVG = (valueType: string) => {
    switch (valueType) {
      case IMPRINT_SCORES.LAUGHTER.toLocaleUpperCase():
        return (
          <View style={styles.imagePadding}>
            <LAUGHTER width={26} height={26} />
          </View>
        );
      case IMPRINT_SCORES.LIFE.toLocaleUpperCase():
        return (
          <View style={styles.imagePadding}>
            <LIFE width={26} height={26} />
          </View>
        );
      case IMPRINT_SCORES.LOVE.toLocaleUpperCase():
        return (
          <View style={styles.imagePadding}>
            <LOVE width={26} height={26} />
          </View>
        );
      case IMPRINT_SCORES.PURPOSE.toLocaleUpperCase():
        return (
          <View style={styles.imagePadding}>
            <PURPOSE width={26} height={26} />
          </View>
        );
      case IMPRINT_SCORES.RESPECT.toLocaleUpperCase():
        return (
          <View style={styles.imagePadding}>
            <RESPECT width={26} height={26} />
          </View>
        );
      case IMPRINT_SCORES.SAFETY.toLocaleUpperCase():
        return (
          <View style={styles.imagePadding}>
            <SAFETY width={26} height={26} />
          </View>
        );
      default:
        return <></>;
    }
  };
  return (
    <View style={styles.container}>
      <View style={styles.subContainer}>
        <View style={styles.itemContainer}>
          {renderSVG(item.valueType)}
          <Text
            color={Colors.black}
            size={'xxSmall'}
            type="medium"
            style={AppStyles.mLeft5}>
            {util.capitalizeFirstLetter(item.valueType.toLocaleLowerCase())}
          </Text>
        </View>

        <ButtonView style={styles.shareIcon} onPress={() => onSharePress(item)}>
          <SHARE_ICON />
        </ButtonView>
      </View>
      <View style={styles.header}>
        <Text
          color={Colors.black}
          size={'buttonText'}
          type={'semi_bold'}
          style={styles.title}>
          {item.title}
        </Text>
      </View>
      <Text
        color={Colors.text.modalText}
        size={'xSmall'}
        numberOfLines={3}
        style={AppStyles.marginVerticalBase}>
        {item.body}
      </Text>
      <View style={styles.divider} />

      <View
        style={{
          alignItems: 'center',
          flexDirection: 'row',
          marginTop: Metrics.ratio(10),
        }}>
        <Text color="#000" size={'small'} type="semi_bold">
          {item.sourceName}
        </Text>
        <View style={styles.dot} />
        <Text style={styles.time}>
          {util.getUpdatedTimeDifference(item?.createdAt ?? '')}
        </Text>
      </View>
      <View style={styles.sliderContainer}>
        <Text
          color="#000"
          size={'xxxSmall'}
          type="semi_bold"
          style={[AppStyles.mRight10, styles.negativeMargin]}>
          {NEWS.BIAS}
        </Text>
        <BiasSlider position={item.allsidesBiasRatings} />
      </View>
      <View style={styles.bottomContainer}>
        <ButtonView style={styles.button} onPress={() => askRudiPress(item)}>
          <Text
            color={Colors.black}
            size={Fonts.size.xxSmall}
            style={styles.buttonText}>
            {NEWS.ASk_RUDI}
          </Text>
        </ButtonView>
        <ButtonView
          style={[styles.button, styles.buttonLeft]}
          onPress={() => readMorePress(item)}>
          <Text
            color={Colors.white}
            size={Fonts.size.xxSmall}
            style={styles.buttonText}>
            {NEWS.READ_MORE}
          </Text>
        </ButtonView>
      </View>
    </View>
  );
};

export default NewsItem;
