import {View} from 'react-native';
import {AppStyles, Colors, Fonts} from '../../../theme';
import {DOWN_ARROW} from '../../../constants/AssetSVGConstants';
import styles from './styles';
import {<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>View, Loader, Text} from '../../../components';
import {shareImprintRequest} from '../../../actions/EntertainmentActions';
import {connect} from 'react-redux';
import {useMemo, useState} from 'react';
import {ISharePayload, UserState} from '../../../types';
import {
  CREATE_IMPRINT,
  ENTERTAINMENTS,
  TOAST_MESSAGES,
  TOAST_TYPE,
} from '../../../constants/StringConstants';
import _ from 'lodash';
import {BottomSheetModal, BottomSheetModalProvider} from '@gorhom/bottom-sheet';
import OutsidePressHandler from 'react-native-outside-press';
import UserSimpleDetails from '../UserSimpleDetails';
import {ScrollView} from 'react-native-gesture-handler';
import {useNavigation} from '@react-navigation/native';
import {showToastMsg} from '../../Alert';

interface sharedProps {
  shareImprintRequest: (
    payload: ISharePayload,
    callback: (res: any) => void,
  ) => void;
  user: UserState;
  shareModalRef: any;
  title: string;
  description: string;
  type: string;
  isBack?: boolean;
}

const ShareModal: React.FC<sharedProps> = ({
  shareImprintRequest,
  shareModalRef,
  user,
  title,
  description,
  type,
  isBack,
}) => {
  const [toggleView, setToggleView] = useState(false);
  const [isGlobal, setIsGlobal] = useState(false);
  const [loading, setLoading] = useState(false);
  const navigation = useNavigation();

  const snapPoints = useMemo(() => ['25%', '60'], []);

  const onSharePress = () => {
    setLoading(true);
    const payload: ISharePayload = {
      isGlobal: isGlobal,
      headline: title,
      description: description,
      type: type,
    };
    shareImprintRequest(payload, (res: any) => {
      setLoading(false);
      resetState();
      showToastMsg(
        TOAST_MESSAGES.SHARE_IMPRINT,
        TOAST_TYPE.SUCCESS,
        2000,
        'top',
      );
      isBack ? navigation.goBack() : null;
    });
  };

  const renderToggleView = () => {
    return (
      <OutsidePressHandler
        style={styles.AttachmentContainer}
        onOutsidePress={() => ({})}>
        <ButtonView
          onPress={() => setToggleView(false)}
          style={styles.overlay}
        />
        <ButtonView
          onPress={() => {
            setIsGlobal(false);
            setToggleView(false);
          }}>
          <Text style={styles.mHorizontal} color={Colors.black}>
            {CREATE_IMPRINT.PERSONAL_TRIBE}
          </Text>
        </ButtonView>
        <ButtonView
          style={styles.downLoadButton}
          onPress={() => {
            setIsGlobal(true);
            setToggleView(false);
          }}>
          <Text style={AppStyles.mLeft5} color={Colors.black}>
            {CREATE_IMPRINT.GLOBAL_TRIBE}
          </Text>
        </ButtonView>
      </OutsidePressHandler>
    );
  };
  const resetState = () => {
    shareModalRef.current?.close();
    setToggleView(false);
    setIsGlobal(false);
  };

  return (
    <BottomSheetModalProvider>
      <BottomSheetModal
        style={AppStyles.flex}
        handleIndicatorStyle={styles.handleStyle}
        backgroundStyle={styles.bottomSheet}
        enablePanDownToClose={true}
        ref={shareModalRef}
        index={1}
        snapPoints={snapPoints}
        onDismiss={() => {
          resetState();
          isBack ? navigation.goBack() : null;
        }}>
        <View style={AppStyles.flex}>
          <ScrollView
            contentContainerStyle={styles.scrollView}
            scrollEventThrottle={16}>
            <Text
              color={Colors.black3}
              size={Fonts.size.normal}
              type={'semi_bold'}
              textAlign="center">
              {ENTERTAINMENTS.SHARE_IMPRINT}
            </Text>
            <Text
              color={Colors.text.modalText}
              size={'xSmall'}
              textAlign="center"
              style={[AppStyles.marginHorizontalsmall, AppStyles.mTop10]}>
              {ENTERTAINMENTS.SHARE_CONFIRM}
            </Text>

            <View style={styles.shareCard}>
              <UserSimpleDetails
                avatarUrl={user.userInfo.avatarUrl}
                displayName={user.userInfo.displayName}
                valueType={type}
                isGlobal={isGlobal}
              />

              <Text color={Colors.black} size={'buttonText'} type={'semi_bold'}>
                {title}
              </Text>
              <Text
                color={Colors.text.modalText}
                size={'xSmall'}
                style={AppStyles.marginVerticalBase}>
                {description}
              </Text>
              <ButtonView
                onPress={() => setToggleView(!toggleView)}
                style={styles.toggleView}>
                <Text color={Colors.BlackButton} size={Fonts.size.xSmall}>
                  {isGlobal
                    ? CREATE_IMPRINT.GLOBAL_TRIBE
                    : CREATE_IMPRINT.PERSONAL_TRIBE}
                </Text>
                <DOWN_ARROW />
              </ButtonView>
            </View>
            <View style={styles.buttonContainer}>
              <AppButton
                buttonStye={styles.cancelButton}
                text={CREATE_IMPRINT.CANCEL}
                onPress={() => resetState()}
              />
              <AppButton
                buttonStye={[styles.publishButton]}
                text={CREATE_IMPRINT.PUBLISH}
                textColor={Colors.white}
                onPress={() => onSharePress()}
              />
              {toggleView && renderToggleView()}
            </View>
            <Loader loading={loading} />
          </ScrollView>
        </View>
      </BottomSheetModal>
    </BottomSheetModalProvider>
  );
};

const mapStateToProps = (state: any) => ({user: state.user});

export default connect(mapStateToProps, {
  shareImprintRequest,
})(ShareModal);
