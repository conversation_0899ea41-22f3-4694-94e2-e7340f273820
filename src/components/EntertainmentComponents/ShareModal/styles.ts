import {Platform, StyleSheet} from 'react-native';
import {Colors, Metrics, Fonts} from '../../../theme';

// @flow

export default StyleSheet.create({
  cancelButton: {
    width: Metrics.ratio(160),
    backgroundColor: Colors.gray2,
    borderRadius: Metrics.ratio(6),
    marginHorizontal: Metrics.ratio(0),
  },
  publishButton: {
    marginHorizontal: Metrics.ratio(0),
    width: Metrics.ratio(160),
  },
  handleStyle: {
    backgroundColor: Colors.handle,
    height: Metrics.ratio(6),
    borderRadius: Metrics.ratio(8),
    width: Metrics.ratio(45),
  },
  bottomSheet: {
    elevation: 10,
    shadowColor: Colors.black,
    shadowOffset: {
      width: 0,
      height: Metrics.ratio(12) as number,
    },
    shadowOpacity: 0.58,
    shadowRadius: 16.0,
  },
  downLoadButton: {
    flexDirection: 'row',
    marginVertical: Metrics.ratio(20),
  },
  mHorizontal: {marginHorizontal: Metrics.ratio(10)},
  AttachmentContainer: {
    backgroundColor: Colors.gray2,
    top: Metrics.ratio(-50),
    position: 'absolute',
    zIndex: 999999,
    height: Metrics.ratio(100),
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: Metrics.ratio(8),
    width: Metrics.ratio(162),
    right: Metrics.ratio(50),
  },
  overlay: {
    position: 'absolute',
    height: Metrics.ratio(50),
    width: Metrics.ratio(60),
  },
  shareCard: {
    borderRadius: Metrics.ratio(24),
    borderColor: Colors.gray,
    borderWidth: 1,
    margin: Metrics.ratio(24),
    padding: Metrics.ratio(24),
  },
  toggleView: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    alignSelf: 'flex-end',
    backgroundColor: '#EEEEF2',
    paddingHorizontal: Metrics.ratio(12),
    paddingVertical: Metrics.ratio(4),
    borderRadius: Metrics.ratio(6),
  },
  buttonContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-evenly',
  },
  scrollView: {
    flexGrow: 1,
    paddingBottom: 40,
  },
});
