import React from 'react';
import {View} from 'react-native';
import {Colors, Fonts, AppStyles, Metrics} from '../../../theme';
import {ButtonView, Text} from '../../';
import {IQuizContentResponse, QUIZ_STATUS} from '../../../types';
import {
  ENTERTAINMENTS,
  IMPRINT_SCORES,
} from '../../../constants/StringConstants';
import {
  GREEN_CHECK,
  LAUGHTER,
  LIFE,
  LOVE,
  PURPOSE,
  RESPECT,
  SAFETY,
} from '../../../constants/AssetSVGConstants';
import {styles} from './styles';
import util from '../../../util';

interface QuizItemProps {
  item: IQuizContentResponse;
  onSharePress: (item: IQuizContentResponse) => void;
  onQuizPress: (item: IQuizContentResponse) => void;
}

const QuizItem: React.FC<QuizItemProps> = ({
  item,
  onSharePress,
  onQuizPress,
}) => {
  const renderSVG = (valueType: string) => {
    switch (valueType) {
      case IMPRINT_SCORES.LAUGHTER.toLocaleUpperCase():
        return (
          <View style={styles.imagePadding}>
            <LAUGHTER width={26} height={26} />
          </View>
        );
      case IMPRINT_SCORES.LIFE.toLocaleUpperCase():
        return (
          <View style={styles.imagePadding}>
            <LIFE width={26} height={26} />
          </View>
        );
      case IMPRINT_SCORES.LOVE.toLocaleUpperCase():
        return (
          <View style={styles.imagePadding}>
            <LOVE width={26} height={26} />
          </View>
        );
      case IMPRINT_SCORES.PURPOSE.toLocaleUpperCase():
        return (
          <View style={styles.imagePadding}>
            <PURPOSE width={26} height={26} />
          </View>
        );
      case IMPRINT_SCORES.RESPECT.toLocaleUpperCase():
        return (
          <View style={styles.imagePadding}>
            <RESPECT width={26} height={26} />
          </View>
        );
      case IMPRINT_SCORES.SAFETY.toLocaleUpperCase():
        return (
          <View style={styles.imagePadding}>
            <SAFETY width={26} height={26} />
          </View>
        );
      default:
        return <></>;
    }
  };
  return (
    <View style={styles.container}>
      <View style={styles.itemContainer}>
        <View style={AppStyles.flexRow}>
          <Text
            color={Colors.lightBlue}
            size={Fonts.size.xxxSmall}
            style={styles.quizType}>
            {util.capitalizeFirstLetter(item.type.toLocaleLowerCase())}
            {ENTERTAINMENTS.QUIZ}
          </Text>
          {item.status === QUIZ_STATUS.COMPLETED && (
            <View style={styles.Status}>
              <GREEN_CHECK />

              <Text
                color={Colors.green1}
                size={Fonts.size.xxxSmall}
                style={AppStyles.mLeft5}>
                {util.capitalizeFirstLetter(item.status.toLocaleLowerCase())}
              </Text>
            </View>
          )}
        </View>
        <View style={AppStyles.flexRow}>{renderSVG(item.category)}</View>
      </View>
      <View style={styles.header}>
        <Text color={Colors.black} size={'buttonText'} type={'semi_bold'}>
          {item.description.tagline}
        </Text>
      </View>
      <Text
        color={Colors.text.modalText}
        size={'xSmall'}
        alignSelf="flex-start">
        {item.totalQuestions}
        {ENTERTAINMENTS.QUESTIONS}
      </Text>
      <View style={styles.bottomButton}>
        {item.status === QUIZ_STATUS.COMPLETED && (
          <ButtonView
            style={[styles.button, AppStyles.mRight10]}
            onPress={() => onSharePress(item)}>
            <Text
              color={Colors.black}
              size={Fonts.size.xxxSmall}
              style={styles.buttonText}>
              {ENTERTAINMENTS.SHARE}
            </Text>
          </ButtonView>
        )}
        <ButtonView style={styles.button} onPress={() => onQuizPress(item)}>
          <Text
            color={Colors.black}
            size={Fonts.size.xxxSmall}
            style={styles.buttonText}>
            {item.status !== QUIZ_STATUS.COMPLETED
              ? ENTERTAINMENTS.START
              : ENTERTAINMENTS.RE_ATTEMPT}
          </Text>
        </ButtonView>
      </View>
    </View>
  );
};

export default QuizItem;
