import {StyleSheet} from 'react-native';
import {Metrics, Colors} from '../../../theme';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.white,
    marginHorizontal: Metrics.ratio(24),
    marginVertical: Metrics.ratio(12),
    padding: Metrics.ratio(12),
    borderRadius: Metrics.ratio(12),
  },
  header: {
    alignItems: 'center',
    flexDirection: 'row',
  },

  button: {
    borderRadius: Metrics.ratio(6),
    backgroundColor: Colors.gray2,
    paddingVertical: Metrics.ratio(4),
    paddingHorizontal: Metrics.ratio(8),
  },
  buttonText: {
    marginHorizontal: Metrics.ratio(10),
    marginVertical: Metrics.ratio(4),
  },
  imagePadding: {
    paddingRight: Metrics.ratio(8),
  },
  itemContainer: {
    flex: 1,
    flexDirection: 'row',
    overflow: 'visible',
    justifyContent: 'space-between',
  },
  quizType: {
    backgroundColor: Colors.lightBlue1,
    borderWidth: 1,
    borderColor: Colors.lightBlue1,
    alignSelf: 'flex-start',
    paddingHorizontal: Metrics.ratio(8),
    paddingVertical: Metrics.ratio(3),
    borderRadius: 10,
    overflow: 'hidden',
  },
  Status: {
    marginLeft: Metrics.ratio(10),
    backgroundColor: Colors.lightGreen,
    borderWidth: 1,
    borderColor: Colors.lightGreen,
    alignSelf: 'flex-start',
    paddingHorizontal: Metrics.ratio(8),
    paddingVertical: Metrics.ratio(3),

    borderRadius: 10,
    overflow: 'hidden',
    flexDirection: 'row',
    alignItems: 'center',
  },
  bottomButton: {flexDirection: 'row', alignSelf: 'flex-end'},
});
