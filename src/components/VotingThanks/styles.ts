import {StyleSheet} from 'react-native';
import {Fonts, Metrics} from '../../theme';

export default StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: '#00000099',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    width: Metrics.screenWidth * 0.85,
    backgroundColor: '#fff',
    borderRadius: 20,
    padding: 20,
    paddingTop: 30,
    alignItems: 'center',
    position: 'relative',
  },
  triangleWrapper: {
    position: 'absolute',
    top: -12,
  },
  svgWrapper: {
    marginBottom: 16,
  },
  title: {
    fontSize: Fonts.size.medium,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
    marginVertical: 10,
  },
  highlight: {
    marginTop: 10,
    fontSize: Fonts.size.medium,
    color: '#FFA500',
  },
  description: {
    fontSize: Fonts.size.small,
    textAlign: 'center',
    lineHeight: 20,
  },
  closeBtn: {
    position: 'absolute',
    top: 10,
    right: 10,
    zIndex: 1,
    padding: 8,
  },
  closeText: {
    fontSize: 20,
    color: '#999',
  },
});
