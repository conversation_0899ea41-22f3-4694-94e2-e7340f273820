import React from 'react';
import {Modal, View, TouchableOpacity, Text} from 'react-native';
import {TICK_GREEN} from '../../constants/AssetSVGConstants';
import styles from './styles';
import {VOTING} from '../../constants/StringConstants';

interface VotingThanksModalProps {
  visible: boolean;
  onClose: () => void;
}

export const VotingThanksModal: React.FC<VotingThanksModalProps> = ({
  visible,
  onClose,
}) => {
  return (
    <Modal visible={visible} transparent animationType="fade">
      <View style={styles.overlay}>
        <View style={styles.modalContainer}>
          <TouchableOpacity style={styles.closeBtn} onPress={onClose}>
            <Text style={styles.closeText}>✕</Text>
          </TouchableOpacity>
          <TICK_GREEN />
          <Text style={styles.title}>
            {VOTING.THANKS}
            {'\n'}
            <Text style={styles.highlight}>{VOTING.VALUE}</Text>
          </Text>

          <Text style={styles.description}>{VOTING.DESCRIPTION}</Text>
        </View>
      </View>
    </Modal>
  );
};
