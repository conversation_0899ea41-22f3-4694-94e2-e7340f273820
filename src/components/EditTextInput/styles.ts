import {StyleSheet} from 'react-native';
import {Colors, Metrics, Fonts} from '../../theme';

export default StyleSheet.create({
  text: {
    fontFamily: Fonts.type.medium,
    fontSize: Metrics.ratio(16),
    color: Colors.text.homeTitleColor,
    marginTop: Metrics.ratio(24),
  },
  image: {
    height: Metrics.ratio(20),
    width: Metrics.ratio(20),
    marginHorizontal: Metrics.ratio(10),
    borderRadius: Metrics.ratio(100),
  },
  mainContainer: {
    height: Metrics.ratio(140),
    borderColor: Colors.gray4,
    marginTop: Metrics.ratio(2),
    borderWidth: Metrics.ratio(1),
    borderRadius: Metrics.ratio(16),
    zIndex: Metrics.ratio(1000),
  },
  textImageContainer: {
    paddingVertical: Metrics.ratio(10),
    flexDirection: 'row',
  },
  textContainer: {
    padding: Metrics.ratio(10),
    flexDirection: 'row',
  },
  symbol: {
    flexDirection: 'row',
    marginVertical: Metrics.ratio(8),
    paddingVertical: Metrics.ratio(5),
    paddingHorizontal: Metrics.ratio(10),
    marginHorizontal: Metrics.ratio(5),
    borderRadius: Metrics.ratio(6),
    backgroundColor: Colors.symbolBackground,
  },
  logoTextContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  logoText: {
    flex: 1,
    paddingLeft: Metrics.ratio(10),
    marginRight: Metrics.ratio(10),
  },
  cross: {
    height: Metrics.ratio(25),
    width: Metrics.ratio(40),
    justifyContent: 'center',
    alignItems: 'center',
  },
  whatIValueContainer: {
    borderWidth: Metrics.ratio(1),
    borderRadius: Metrics.ratio(16),
    padding: Metrics.ratio(12),
    borderColor: Colors.gray4,
    marginTop: Metrics.ratio(12),
    paddingTop: Metrics.ratio(12),
  },
  dropDownContainer: {
    marginTop: Metrics.ratio(10),
  },
  dropDownInnerContainer: {
    top: 0,
    position: 'relative',
  },
  dropDown: {
    backgroundColor: Colors.white,
  },
  phoneInputContainer: {
    height: Metrics.ratio(60),
    paddingVertical: 0,
    backgroundColor: Colors.white,
    borderRadius: Metrics.ratio(16),
    paddingRight: Metrics.ratio(15),
    borderColor: Colors.gray4,
    borderWidth: Metrics.ratio(1),
  },
  phoneTextInput: {
    padding: 0,
  },
});
