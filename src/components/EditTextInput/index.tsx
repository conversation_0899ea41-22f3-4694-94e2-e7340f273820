import React, {useEffect, useRef, useState} from 'react';
import {
  Image,
  Keyboard,
  Platform,
  Pressable,
  Text,
  TextInput,
  TextInputProps,
  View,
  ViewStyle,
} from 'react-native';
import {Colors, Metrics, Fonts, AppStyles} from '../../theme';
import {
  IAboutTitleSubtitle,
  Relation,
  SuggestedData,
  UserInfo,
} from '../../types';
import styles from './styles';
import {FlatList} from 'react-native-gesture-handler';
import util from '../../util';
import DatePicker from 'react-native-date-picker';
import {
  ABOUT,
  EDIT_TEXT_INPUT,
  TYPE_ONE_EDIT,
} from '../../constants/StringConstants';
import {CROSS_ICON} from '../../constants/AssetSVGConstants';
import DropDownPicker from 'react-native-dropdown-picker';
import PhoneInput, {isValidNumber} from 'react-native-phone-number-input';
import {parsePhoneNumberFromString} from 'libphonenumber-js';
import {DATE_FORMAT2, DATE_FORMAT6} from '../../constants';

interface InputFieldProps extends TextInputProps {
  enabled?: boolean;
  multiline?: boolean;
  style?: ViewStyle;
  data: IAboutTitleSubtitle;
  finalData?: any;
  suggestedData?: SuggestedData[];
  emptyInstitute?: () => void;
  setFinalData: (data: any) => void;
  setShowFlatlist?: (data: boolean) => void;
  setInstitute?: (data: any) => void;
  setSearchedUsers?: (data: any) => void;
  showFlatlist?: boolean;
  users?: UserInfo[];
  emptyUsers?: () => void;
  setRelation?: (data: Relation) => void;
  relationEnum?: Relation[];
  symbolData?: any[];
}

const EditInputField: React.FC<InputFieldProps> = ({
  enabled = true,
  multiline = false,
  style,
  data,
  finalData,
  suggestedData,
  setFinalData,
  emptyInstitute,
  setShowFlatlist,
  setInstitute,
  setSearchedUsers,
  emptyUsers,
  setRelation,
  showFlatlist,
  users,
  relationEnum,
  symbolData,
  disabled,

  ...rest
}) => {
  const [value, setValue] = useState(data?.subtitle);
  const [dob, setDob] = useState<Date>(new Date());
  const [open, setOpen] = useState(false);
  const [showValue, setShowValue] = useState(false);
  const [relationFlatList, setRelationFlatList] = useState(false);
  const [symbolDataCopy, setSymbolDataCopy] = useState<any[]>([]);
  const [symbolFlatList, setSymbolFlatList] = useState(false);
  const [dropDownValue, setdDropDownValue] = useState('');
  const [items, setItems] = useState([
    {label: 'MALE', value: 'male'},
    {label: 'FEMALE', value: 'female'},
  ]);
  const [openDropDown, setOpenDropDown] = useState(false);
  const phoneInput = useRef<PhoneInput>(null);
  const [formattedValue, setFormattedValue] = useState('');
  const [phoneValue, setPhoneValue] = useState('');
  const [valid, setValid] = useState(true);
  const [countryCode, setCountryCode] = useState<any>('');

  useEffect(() => {
    if (data?.title === EDIT_TEXT_INPUT.GENDER && dropDownValue !== '') {
      const updatedData = finalData
        ? finalData.map((item: IAboutTitleSubtitle) => {
            if (item.title === data?.title) {
              return {
                ...item,
                subtitle: dropDownValue,
              };
            }
            return item;
          })
        : [];
      setFinalData(updatedData);
    }
  }, [dropDownValue]);

  // All commented code in file maybe use in future if clients want to revert it
  // useEffect(() => {
  //   if (data?.title === EDIT_TEXT_INPUT.PHONE) {
  //     const updatedData = finalData
  //       ? finalData.map((item: IAboutTitleSubtitle) => {
  //           if (item.title === data?.title) {
  //             return {
  //               ...item,
  //               subtitle: valid ? formattedValue : ABOUT.INVALID,
  //             };
  //           }
  //           return item;
  //         })
  //       : [];
  //     setFinalData(updatedData);
  //   }
  // }, [phoneValue]);

  useEffect(() => {
    setSymbolDataCopy(symbolData ?? []);
  }, []);

  useEffect(() => {
    if (data?.title === EDIT_TEXT_INPUT.GENDER) {
      setdDropDownValue(data?.subtitle);
    }
    // if (data?.title === EDIT_TEXT_INPUT.PHONE) {
    //   const phoneNumbWithoutCode = data?.subtitle.split('/');
    //   const phoneWithCode = data?.subtitle?.replace('/', '');
    //   const phoneNumber = parsePhoneNumberFromString(phoneWithCode);
    //   setPhoneValue(phoneNumbWithoutCode[1]);
    //   if (phoneNumber) {
    //     const countryCode = phoneNumber.country;
    //     setCountryCode(countryCode);
    //   }
    // }
    else {
      setValue(data?.subtitle);
    }
  }, [data?.subtitle]);

  const handleChangeText = (text: string) => {
    if (
      data?.title === EDIT_TEXT_INPUT.INSTITUTE ||
      data?.title === EDIT_TEXT_INPUT.COMPANY ||
      data?.title === EDIT_TEXT_INPUT.NAME
    ) {
      setShowFlatlist?.(true);
    }

    // if (data?.title === EDIT_TEXT_INPUT.PHONE) {
    //   return;
    // }

    if (data?.title === TYPE_ONE_EDIT.ABOUT_YOU) {
      if (text.length > 200) {
        return;
      }
    }

    if (data?.title === EDIT_TEXT_INPUT.WHAT_I_VALUE) {
      let status = symbolDataCopy?.find(item => item.title === text);

      if (!status) {
        return;
      }
      let finalText = '';
      const updatedData = finalData
        ? finalData.map((item: IAboutTitleSubtitle) => {
            const val = `${text.toLowerCase()}`;
            if (item?.title === data?.title) {
              finalText = !item.subtitle?.includes(val)
                ? item.subtitle === null
                  ? val
                  : item.subtitle + ',' + val
                : item.subtitle;
              if (finalText?.startsWith(',')) {
                finalText = finalText.substring(1);
              }
              return {
                ...item,
                subtitle: finalText,
              };
            }
            return item;
          })
        : [];
      let copy = symbolDataCopy;
      copy?.map(item => {
        if (item.title === text) {
          item.selected = true;
          setValue(finalText);
        }
      });
      setSymbolDataCopy(copy);
      setFinalData(updatedData);
    } else {
      setValue(text);
      const updatedData = finalData
        ? finalData.map((item: IAboutTitleSubtitle) => {
            if (item.title === data?.title) {
              return {
                ...item,
                subtitle: text,
              };
            }
            return item;
          })
        : [];
      setFinalData(updatedData);
    }
  };

  useEffect(() => {
    if (showValue) {
      const updatedData = finalData
        ? finalData.map((item: IAboutTitleSubtitle) => {
            if (item.title === data?.title) {
              return {
                ...item,
                subtitle: dob,
              };
            }
            return item;
          })
        : [];
      setFinalData(updatedData);
    }
  }, [showValue, dob]);

  const isDate = (value: any): boolean => {
    const dateObject = new Date(value);
    return !isNaN(dateObject.getTime());
  };

  const renderDate = () => {
    if (data?.title?.includes(EDIT_TEXT_INPUT.SINCE)) {
      setOpen(true);
    }
  };

  const onCrossPress = (item: string) => {
    let updatedSubtitle = '';
    const updatedData = finalData
      ? finalData.map((data: IAboutTitleSubtitle) => {
          if (data?.title === EDIT_TEXT_INPUT.WHAT_I_VALUE) {
            updatedSubtitle = data?.subtitle.replace(
              new RegExp(`,?${item.toLowerCase()}`, 'gi'),
              '',
            );
            if (updatedSubtitle.startsWith(',')) {
              updatedSubtitle = updatedSubtitle.substring(1);
            }
            return {
              ...data,
              subtitle: updatedSubtitle,
            };
          }
          return data;
        })
      : [];
    let copy = symbolDataCopy;
    copy?.map(cop => {
      if (cop.title === item) {
        cop.selected = false;
      }
    });
    setSymbolDataCopy(copy);
    setValue(updatedSubtitle);
    setFinalData(updatedData);
  };

  const openWhatIValue = (value: any) => {
    if (
      isDate(value) ||
      data?.title?.includes(EDIT_TEXT_INPUT.DATE) ||
      data?.title?.includes(EDIT_TEXT_INPUT.SINCE)
    ) {
      renderDate();
    }
    setSymbolFlatList(!symbolFlatList);
  };

  useEffect(() => {
    Keyboard.dismiss();
  }, [users?.length]);

  return (
    <View>
      {data?.title && <Text style={styles.text}>{data.title}</Text>}
      {data?.title === EDIT_TEXT_INPUT.WHAT_I_VALUE ||
      isDate(value) ||
      data?.title?.includes(EDIT_TEXT_INPUT.DATE) ||
      data?.title?.includes(EDIT_TEXT_INPUT.SINCE) ? (
        <Pressable
          style={styles.whatIValueContainer}
          onPress={() => openWhatIValue(value)}>
          <Text
            style={[
              {
                fontFamily: Fonts.type.regular,
              },
              data?.title?.includes(EDIT_TEXT_INPUT.DATE) ||
              data?.title?.includes(EDIT_TEXT_INPUT.SINCE)
                ? value === '' || value === null
                  ? {color: Colors.text.publishTextInActiveColor}
                  : {color: Colors.text.modalText}
                : {color: Colors.text.modalText},
            ]}>
            {data?.title?.includes(EDIT_TEXT_INPUT.DATE) ||
            data?.title?.includes(EDIT_TEXT_INPUT.SINCE)
              ? isDate(value)
                ? util.getFormattedDateTime(new Date(value), DATE_FORMAT2)
                : EDIT_TEXT_INPUT.SELECT_DATE
              : value}
          </Text>
        </Pressable>
      ) : data?.title === EDIT_TEXT_INPUT.GENDER ? (
        <View style={styles.dropDownContainer}>
          <DropDownPicker
            listMode="SCROLLVIEW"
            open={openDropDown}
            value={dropDownValue}
            items={items}
            setOpen={setOpenDropDown}
            setValue={setdDropDownValue}
            setItems={setItems}
            dropDownContainerStyle={styles.dropDownInnerContainer}
            style={styles.dropDown}
          />
        </View>
      ) : (
        // : data.title === EDIT_TEXT_INPUT.PHONE ? (
        //   <View
        //     style={{
        //       paddingTop: 10,
        //     }}>
        //     <PhoneInput
        //       key={countryCode}
        //       ref={phoneInput}
        //       value={phoneValue}
        //       defaultCode={countryCode !== '' ? countryCode : 'US'}
        //       textInputStyle={styles.phoneTextInput}
        //       layout="first"
        //       onChangeText={text => {
        //         const checkValid = phoneInput.current?.isValidNumber(text);
        //         setValid(checkValid ? checkValid : false);
        //         setPhoneValue(text);
        //       }}
        //       onChangeFormattedText={text => {
        //         setFormattedValue(text);
        //       }}
        //       containerStyle={styles.phoneInputContainer}
        //     />
        //   </View>
        // ) : (
        <Pressable
          onPress={() =>
            Platform.OS === 'android' &&
            data?.title === EDIT_TEXT_INPUT.RELATION
              ? setRelationFlatList?.(true)
              : undefined
          }>
          <TextInput
            style={[
              {
                borderWidth: Metrics.ratio(1),
                borderRadius: Metrics.ratio(16),
                padding: Metrics.ratio(12),
                borderColor: Colors.gray4,
                backgroundColor: enabled ? Colors.white : Colors.gray4,
                fontFamily: Fonts.type.regular,
                color: enabled
                  ? Colors.text.modalText
                  : Colors.text.publishTextInActiveColor,
                marginTop: Metrics.ratio(12),
                paddingTop: Metrics.ratio(12),
              },
              style,
            ]}
            placeholder={
              !value ? `${EDIT_TEXT_INPUT.ENTER} ${data?.title}` : ''
            }
            editable={
              data?.title !== EDIT_TEXT_INPUT.RELATION &&
              data?.title !== 'Username'
            }
            onPressIn={() => {
              if (data?.title === EDIT_TEXT_INPUT.RELATION) {
                setRelationFlatList?.(true);
              }
            }}
            multiline={
              data?.title === TYPE_ONE_EDIT.WHAT_I_STAND_FOR ||
              data?.title === TYPE_ONE_EDIT.ABOUT_YOU
            }
            value={
              value === '' || value === null
                ? ''
                : isDate(value)
                ? util.getFormattedDateTime(new Date(value), DATE_FORMAT2)
                : value
            }
            onFocus={() => {
              renderDate(),
                data?.title === EDIT_TEXT_INPUT.RELATION
                  ? setRelationFlatList?.(true)
                  : setRelationFlatList(false),
                data?.title === EDIT_TEXT_INPUT.WHAT_I_VALUE
                  ? setSymbolFlatList(true)
                  : setSymbolFlatList(false);
            }}
            onChangeText={handleChangeText}
            {...rest}
          />
        </Pressable>
      )}

      {(data?.title === EDIT_TEXT_INPUT.INSTITUTE ||
        data?.title === EDIT_TEXT_INPUT.COMPANY) &&
      (suggestedData?.length ?? 0) > 0 ? (
        <View style={styles.mainContainer}>
          <FlatList
            data={suggestedData}
            renderItem={({item}: {item: any}) => (
              <Pressable
                style={styles.textImageContainer}
                onPress={() => {
                  handleChangeText(item.name),
                    setInstitute?.(item),
                    setShowFlatlist?.(false),
                    emptyInstitute?.();
                }}>
                <Image source={{uri: item.logo}} style={styles.image} />
                <Text>{item.name}</Text>
              </Pressable>
            )}
          />
        </View>
      ) : data?.title === EDIT_TEXT_INPUT.NAME && (users?.length ?? 0) > 0 ? (
        <View style={styles.mainContainer}>
          <FlatList
            data={users}
            renderItem={({item}: {item: any}) => (
              <Pressable
                style={styles.textContainer}
                onPress={() => {
                  handleChangeText(item.displayName), emptyUsers?.();
                  setSearchedUsers?.(item);
                  setShowFlatlist?.(false);
                }}>
                <Text>{item.displayName}</Text>
              </Pressable>
            )}
          />
        </View>
      ) : data?.title === EDIT_TEXT_INPUT.RELATION &&
        relationFlatList &&
        (relationEnum?.length ?? 0) > 0 ? (
        <View style={styles.mainContainer}>
          <FlatList
            data={relationEnum}
            renderItem={({item}: {item: any}) => (
              <Pressable
                style={styles.textContainer}
                onPress={() => {
                  handleChangeText(item.relation);
                  setRelation?.(item);
                  setRelationFlatList(false);
                }}>
                <Text>{item.relation}</Text>
              </Pressable>
            )}
          />
        </View>
      ) : data?.title === EDIT_TEXT_INPUT.WHAT_I_VALUE &&
        symbolFlatList &&
        (symbolDataCopy?.length ?? 0) > 0 ? (
        <View style={styles.mainContainer}>
          <FlatList
            style={AppStyles.marginVerticalBase}
            data={symbolDataCopy}
            renderItem={({item}: {item: any}) => (
              <Pressable
                style={[
                  styles.symbol,
                  item.selected
                    ? {backgroundColor: Colors.lightGray}
                    : {backgroundColor: 'white'},
                ]}
                onPress={() => {
                  handleChangeText(item.title);
                }}>
                <View style={styles.logoTextContainer}>
                  {item.image}
                  <Text style={styles.logoText}>{item.title}</Text>
                  {item.selected && (
                    <Pressable
                      style={styles.cross}
                      onPress={() => onCrossPress(item.title)}>
                      <CROSS_ICON width={10} height={10} />
                    </Pressable>
                  )}
                </View>
              </Pressable>
            )}
          />
        </View>
      ) : null}
      <DatePicker
        maximumDate={new Date()}
        modal
        locale="en"
        mode="date"
        open={open}
        date={dob}
        onConfirm={(date: React.SetStateAction<Date>) => {
          setShowValue(true);
          setOpen(false);
          setDob(date);
        }}
        onCancel={() => {
          setOpen(false);
        }}
      />
    </View>
  );
};

export default EditInputField;
