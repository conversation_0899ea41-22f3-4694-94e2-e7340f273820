import React from 'react';
import {Pressable, View} from 'react-native';
import {
  CROSS_WHITE,
  THREE_DOTS_WHITE,
} from '../../../constants/AssetSVGConstants';
import styles from './styles';
import {ITimeLine, UserState} from '../../../types';

interface TopBarProps {
  onClosePress: () => void;
  onMorePress: () => void;
  user: UserState;
  imprintItem: ITimeLine;
}

const TopBar: React.FC<TopBarProps> = ({
  onClosePress,
  onMorePress,
  user,
  imprintItem,
}) => {
  return (
    <View style={styles.container}>
      <Pressable onPress={() => onClosePress()}>
        <CROSS_WHITE />
      </Pressable>
      {user.data.userId !== imprintItem.user.id &&
        !imprintItem.followerRequestSent && (
          <Pressable onPress={() => onMorePress()}>
            {imprintItem.id !== null && <THREE_DOTS_WHITE />}
          </Pressable>
        )}
    </View>
  );
};

export default TopBar;
