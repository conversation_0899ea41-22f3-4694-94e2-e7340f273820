import {StyleSheet} from 'react-native';
import {Colors, Metrics, Fonts, AppStyles} from '../../../theme';

export default StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'space-between',
    marginBottom: Metrics.ratio(15),
    marginTop: Metrics.ratio(60),
  },
  overlayText: {
    color: Colors.white,
    fontFamily: Fonts.type.regular,
    fontSize: Metrics.ratio(28),
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  playButton: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: Metrics.ratio(80),
    top: Metrics.ratio(50),
  },
  absoluteFillObject: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  pdfImage: {
    flex: 1,
    backgroundColor: Colors.green,
  },
  flexOne: {
    flex: 1,
  },
  documentImage: {
    flex: 1,
    width: '30%',
    height: '30%',
    top: Metrics.ratio(20),
    alignSelf: 'center',
  },
  modal: {
    alignItems: 'center',
    justifyContent: 'center',
    width: Metrics.screenWidth,
    height: Metrics.screenHeight,
    backgroundColor: '#000000AB',
  },
  pdf: {
    height: Metrics.screenHeight * 0.8,
    width: Metrics.screenWidth - 40,
    borderRadius: Metrics.ratio(6),
  },
  loadingStyle: {
    alignSelf: 'center',
    position: 'absolute',
    top: '50%',
  },
  audioPlayerContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    bottom: Metrics.ratio(100),
  },
});
