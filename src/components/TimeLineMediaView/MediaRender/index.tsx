import React, {useEffect, useRef, useState} from 'react';
import {
  Pressable,
  View,
  TouchableOpacity,
  Modal,
  Image,
  ActivityIndicator,
  Alert,
  Linking,
} from 'react-native';
import {ImprintMedia} from '../../../types';
import FastImage from 'react-native-fast-image';
import {HOME, IMPRINT_MEDIA} from '../../../constants/StringConstants';
import {PLAY_BUTTON} from '../../../constants/AssetSVGConstants';
import styles from './styles';
import Video from 'react-native-video';
import Handle from '../Handle';
import FileViewer from '../../FileViewer';
import AudioPlayer from '../../CreateImprint/AudioPlayer';
import AudioRecorderPlayer from 'react-native-audio-recorder-player';
import {Colors} from '../../../theme';

const audioRecorderPlayer = new AudioRecorderPlayer();

interface RenderMediaProps {
  item: ImprintMedia;
  onPressLeft: () => void;
  onPressRight: () => void;
  showHandle: boolean;
}

const RenderMedia: React.FC<RenderMediaProps> = ({
  item,
  onPressLeft,
  onPressRight,
  showHandle,
}) => {
  const mediaType = item.type;
  const videoRef = useRef<Video>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [pdfURL, setPdfURL] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);

  const [currentPosition, setCurrentPosition] = useState(0);
  const [totalDuration, setTotalDuration] = useState(0);
  const [waveData, setWaveData] = useState(
    Array(200).fill({
      value: Math.random() * 400 + 10,
      color: Colors.BlackButton,
    }),
  );

  const showPlayButton = () => {
    return (
      <Pressable style={styles.playButton} onPress={() => playVideo()}>
        {!isPlaying ? <PLAY_BUTTON /> : <></>}
      </Pressable>
    );
  };

  useEffect(() => {
    setIsPlaying(false);
  }, [item]);

  const playVideo = () => {
    setIsPlaying(prevState => !prevState && item.type === IMPRINT_MEDIA.VIDEO);
  };

  const onLoad = () => {
    videoRef?.current?.seek(1);
    setLoading(false);
  };

  const onEnd = () => {
    setIsPlaying(false);
    videoRef?.current?.seek(0);
  };

  const onStartPlay = async (item: string | undefined) => {
    setIsPlaying(true);
    await audioRecorderPlayer.startPlayer(item);
    audioRecorderPlayer.setVolume(1.0);
    audioRecorderPlayer.addPlayBackListener(e => {
      setTotalDuration(e.duration);
      setCurrentPosition(e.currentPosition);
      setWaveData(prevData => {
        return prevData.map(bar => {
          const randomHeight = Math.random() * 30 + 10;
          return {
            ...bar,
            value: randomHeight,
            color: '#4CAF50',
          };
        });
      });
      if (e.isFinished) {
        setWaveData(
          Array(200).fill({
            value: Math.random() * 40 + 10,
            color: Colors.whit1,
          }),
        );
        onStopPlay();
        setCurrentPosition(0);
      }
      return;
    });
  };

  const onStopPlay = () => {
    setIsPlaying(false);
    audioRecorderPlayer.stopPlayer();
    setWaveData(
      Array(200).fill({value: Math.random() * 40 + 10, color: Colors.whit1}),
    );
    setCurrentPosition(0);
  };

  const onArticlesPress = (article: ImprintMedia) => {
    Alert.alert(HOME.DISCLAIMER, HOME.DISCLAIMER_TEXT, [
      {
        text: 'Cancel',
        style: 'cancel',
      },
      {
        text: 'Redirect',
        onPress: () => Linking.openURL(article.url ?? ''),
      },
    ]);
  };

  const getMedia = () => {
    if (mediaType === IMPRINT_MEDIA.IMAGE) {
      return <FastImage source={{uri: item.url}} style={styles.flexOne} />;
    }
    if (mediaType === IMPRINT_MEDIA.VIDEO) {
      return (
        <View style={styles.flexOne}>
          {!isPlaying ? (
            <View style={styles.flexOne}>
              <FastImage source={{uri: item.thumbUrl}} style={styles.flexOne} />
            </View>
          ) : (
            <>
              <Video
                resizeMode="contain"
                onLoadStart={() => setLoading(true)}
                onEnd={() => onEnd()}
                paused={false}
                volume={2.0}
                source={{
                  uri: item.url,
                }}
                style={styles.flexOne}
                onLoad={() => onLoad()}
              />
              {loading && (
                <ActivityIndicator
                  size="large"
                  color={'#000'}
                  style={styles.loadingStyle}
                />
              )}
            </>
          )}
        </View>
      );
    }
    if (mediaType === IMPRINT_MEDIA.DOCUMENT) {
      return (
        <Pressable
          style={styles.flexOne}
          onPress={() => setPdfURL(item.url || '')}>
          <Image
            source={require('../../../assets/icons/attachment.png')}
            defaultSource={require('../../../assets/icons/attachment.png')}
            style={styles.documentImage}
            resizeMode="contain"
          />
        </Pressable>
      );
    }
    if (mediaType === IMPRINT_MEDIA.ARTICLE) {
      return (
        <Pressable style={styles.flexOne} onPress={() => onArticlesPress(item)}>
          <Image
            source={require('../../../assets/icons/attachment.png')}
            defaultSource={require('../../../assets/icons/attachment.png')}
            style={styles.documentImage}
            resizeMode="contain"
          />
        </Pressable>
      );
    }
    if (mediaType === IMPRINT_MEDIA.AUDIO) {
      return (
        <View style={styles.audioPlayerContainer}>
          <AudioPlayer
            onStartPlay={() => onStartPlay(item?.url)}
            waveData={waveData}
            recordedFilePath={item.path}
            isPlying={isPlaying}
            onStopPlay={onStopPlay}
            totalDuration={totalDuration}
            currentPosition={currentPosition}
          />
        </View>
      );
    }
    return null;
  };

  const renderPDF = (pdfURL: string) => {
    return (
      <Modal
        animationType="fade"
        transparent={true}
        visible={pdfURL !== ''}
        onRequestClose={() => {
          setPdfURL('');
        }}
        onDismiss={() => setPdfURL('')}>
        <TouchableOpacity style={styles.modal} onPressOut={() => setPdfURL('')}>
          <View style={styles.pdf}>
            <FileViewer pdfURL={pdfURL} />
          </View>
        </TouchableOpacity>
      </Modal>
    );
  };

  const onHandlePress = (fun: () => void) => {
    audioRecorderPlayer.stopPlayer();
    fun();
  };

  return (
    <View style={styles.container}>
      {getMedia()}
      {showHandle ? (
        <Handle
          onPressLeft={() => onHandlePress(onPressLeft)}
          onPressRight={() => onHandlePress(onPressRight)}
        />
      ) : (
        <></>
      )}
      {mediaType === IMPRINT_MEDIA.VIDEO ? showPlayButton() : <></>}
      {pdfURL !== '' && renderPDF(pdfURL)}
    </View>
  );
};

export default RenderMedia;
