import React from 'react';
import {Pressable, View, ViewStyle} from 'react-native';
import {LEFT_HANDLE, RIGHT_HANDLE} from '../../../constants/AssetSVGConstants';
import styles from './styles';

interface HandleProps {
  overrideStyle?: ViewStyle;
  onPressLeft: () => void;
  onPressRight: () => void;
}

const Handle: React.FC<HandleProps> = ({
  onPressLeft,
  onPressRight,
  overrideStyle,
}) => {
  return (
    <View>
      <Pressable
        onPress={onPressLeft}
        style={[styles.handleLeft, overrideStyle]}>
        <LEFT_HANDLE />
      </Pressable>
      <Pressable
        onPress={onPressRight}
        style={[styles.handleRight, overrideStyle]}>
        <RIGHT_HANDLE />
      </Pressable>
    </View>
  );
};

export default Handle;
