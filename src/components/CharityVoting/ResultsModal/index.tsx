import React from 'react';
import {View, Modal, ScrollView, TouchableOpacity} from 'react-native';
import {Text, AppButton} from '../../index';
import {Colors, Fonts} from '../../../theme';
import {CHARITY_VOTING} from '../../../constants/StringConstants';
import {styles} from './styles';
import * as Progress from 'react-native-progress';
import {size} from 'lodash';
import {LOVE_GLOBAL} from '../../../constants/AssetSVGConstants';

interface CharityResult {
  id: string;
  name: string;
  votes: number;
  percentage: number;
  rank: number;
}

interface CharityResultsModalProps {
  visible: boolean;
  results: CharityResult[];
  onClose: () => void;
}

const CharityResultsModal: React.FC<CharityResultsModalProps> = ({
  visible,
  results,
  onClose,
}) => {
  const getRankColor = (rank: number) => {
    switch (rank) {
      case 1:
        return 'orange'; // Gold/Winner color
    }
  };

  const renderCharityResult = (result: CharityResult) => {
    const progressColor = getRankColor(result.rank);
    const progress = result.percentage / 100;

    return (
      <View key={result.id} style={styles.resultItem}>
        <View style={styles.percentageContainer}>
          <Text
            size={Fonts.size.xxxSmall}
            type="semi_bold"
            color={Colors.text.titleColor}>
            {result.name}
          </Text>
          <Text
            size="medium"
            type="medium"
            color={Colors.text.titleColor}
            style={styles.percentageText}>
            {result.percentage.toFixed(1)}%
          </Text>
        </View>

        <View>
          <Progress.Bar
            progress={progress}
            width={null}
            height={8}
            borderRadius={4}
            color={'#F8A337'}
            unfilledColor={Colors.lightGray1}
            borderColor="transparent"
            useNativeDriver
            style={{
              backgroundColor: '#EEEEEE',
            }}
          />
        </View>
      </View>
    );
  };

  // Sort results by rank
  const sortedResults = [...results].sort((a, b) => a.rank - b.rank);

  return (
    <Modal visible={visible} transparent animationType="fade">
      <View style={styles.overlay}>
        <View style={styles.modalContainer}>
          <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <Text style={styles.closeText}>✕</Text>
          </TouchableOpacity>
          <View style={{alignSelf: 'center'}}>
            <LOVE_GLOBAL height={40} width={40} />
          </View>

          <Text
            size="large"
            type="semi_bold"
            color={Colors.text.titleColor}
            style={styles.title}>
            {CHARITY_VOTING.RESULTS_MODAL_TITLE}
          </Text>

          <Text
            size="small"
            type="medium"
            color={Colors.text.gray}
            style={styles.subtitle}>
            {CHARITY_VOTING.RESULTS_MODAL_SUBTITLE}
          </Text>

          <ScrollView
            style={styles.resultsList}
            showsVerticalScrollIndicator={false}>
            {sortedResults.map(renderCharityResult)}
          </ScrollView>
          <Text
            style={{color: Colors.text.gray}}
            size={'xxxSmall'}
            textAlign={'center'}>
            Want to{' '}
            <Text
              size={Fonts.size.xxxSmall}
              style={{
                fontWeight: 'bold',
                color: Colors.black,
              }}>
              vote again ?{' '}
            </Text>
            Log out and log back in to drive attention to the charity you care
            about the most!
          </Text>

          {/* <AppButton
            text={CHARITY_VOTING.CLOSE}
            buttonStye={styles.closeButtonStyle}
            textColor={Colors.white}
            onPress={onClose}
            size="medium"
            type="semi_bold"
          /> */}
        </View>
      </View>
    </Modal>
  );
};

export default CharityResultsModal;
