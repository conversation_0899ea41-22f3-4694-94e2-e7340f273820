import React from 'react';
import {View} from 'react-native';
import {Text, ButtonView, AppButton} from '../../index';
import {Colors} from '../../../theme';
import {CHARITY_VOTING} from '../../../constants/StringConstants';
import {styles} from './styles';

interface CharityAnnouncementCardProps {
  currentWinner: string;
  onReadMorePress: () => void;
  onVoteNowPress: () => void;
  isVotingDay: boolean;
}

const CharityAnnouncementCard: React.FC<CharityAnnouncementCardProps> = ({
  currentWinner,
  onReadMorePress,
  onVoteNowPress,
  isVotingDay,
}) => {
  if (!isVotingDay) {
    return null;
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text
          size="medium"
          type="semi_bold"
          color={Colors.text.titleColor}
          style={styles.title}>
          {CHARITY_VOTING.ANNOUNCEMENT_TITLE}
        </Text>
      </View>

      <View style={styles.content}>
        <Text
          size="small"
          type="medium"
          color={Colors.text.gray}
          style={styles.currentWinnerLabel}>
          {CHARITY_VOTING.CURRENT_WINNER}
        </Text>
        <Text
          size="medium"
          type="semi_bold"
          color={Colors.text.titleColor}
          style={styles.winnerName}>
          {currentWinner}
        </Text>
      </View>

      <View style={styles.buttonContainer}>
        <ButtonView style={styles.readMoreButton} onPress={onReadMorePress}>
          <Text
            size="small"
            type="medium"
            color={Colors.text.titleColor}
            style={styles.readMoreText}>
            {CHARITY_VOTING.READ_MORE}
          </Text>
        </ButtonView>

        <AppButton
          text={CHARITY_VOTING.VOTE_NOW}
          buttonStye={styles.voteButton}
          textColor={Colors.white}
          onPress={onVoteNowPress}
          size="small"
          type="semi_bold"
        />
      </View>
    </View>
  );
};

export default CharityAnnouncementCard;
