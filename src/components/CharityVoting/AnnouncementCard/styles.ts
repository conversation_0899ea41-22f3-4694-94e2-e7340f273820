import {StyleSheet} from 'react-native';
import {Colors, Metrics} from '../../../theme';

export const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.white,
    marginHorizontal: Metrics.ratio(20),
    marginBottom: Metrics.ratio(20),
    borderRadius: Metrics.ratio(28),
    padding: Metrics.ratio(20),
    shadowColor: Colors.black,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  header: {
    marginBottom: Metrics.ratio(12),
  },
  title: {
    textAlign: 'center',
  },
  content: {
    alignItems: 'center',
    marginBottom: Metrics.ratio(16),
  },
  currentWinnerLabel: {
    marginBottom: Metrics.ratio(4),
  },
  winnerName: {
    textAlign: 'center',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  readMoreButton: {
    flex: 1,
    marginRight: Metrics.ratio(8),
    paddingVertical: Metrics.ratio(12),
    paddingHorizontal: Metrics.ratio(16),
    borderRadius: Metrics.ratio(6),
    borderWidth: 1,
    borderColor: Colors.gray2,
    backgroundColor: Colors.white,
    alignItems: 'center',
    justifyContent: 'center',
  },
  readMoreText: {
    textAlign: 'center',
  },
  voteButton: {
    flex: 1,
    marginLeft: Metrics.ratio(8),
    marginHorizontal: 0,
    paddingVertical: Metrics.ratio(12),
    height: Metrics.ratio(44),
  },
});
