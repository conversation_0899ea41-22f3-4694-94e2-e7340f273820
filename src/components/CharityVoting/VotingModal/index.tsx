import React, {useState} from 'react';
import {<PERSON>, Modal, ScrollView, TouchableOpacity, Image} from 'react-native';
import {Text, ButtonView, AppButton, Avatar} from '../../index';
import {Colors, Fonts} from '../../../theme';
import {CHARITY_VOTING} from '../../../constants/StringConstants';
import {styles} from './styles';
import {PLACEHOLDER_IMAGE} from '../../../constants';
import {LOVE_GLOBAL} from '../../../constants/AssetSVGConstants';

interface Charity {
  id: string;
  name: string;
  description: string;
}

interface CharityVotingModalProps {
  visible: boolean;
  charities: Charity[];
  onClose: () => void;
  onSubmitVote: (charityId: string) => void;
}

const CharityVotingModal: React.FC<CharityVotingModalProps> = ({
  visible,
  charities,
  onClose,
  onSubmitVote,
}) => {
  const [selectedCharityId, setSelectedCharityId] = useState<string | null>(
    null,
  );

  const handleSubmit = () => {
    if (selectedCharityId) {
      onSubmitVote(selectedCharityId);
      setSelectedCharityId(null);
    }
  };

  const handleClose = () => {
    setSelectedCharityId(null);
    onClose();
  };

  const renderCharityOption = (charity: Charity) => {
    const isSelected = selectedCharityId === charity.id;

    return (
      <TouchableOpacity
        key={charity.id}
        style={[styles.charityOption, isSelected && styles.selectedOption]}
        onPress={() => setSelectedCharityId(charity.id)}>
        <View style={styles.charityInfo}>
          <Image
            src={PLACEHOLDER_IMAGE}
            style={{
              width: 32,
              height: 32,
              borderRadius: 16,
            }}
          />
          <View>
            <Text
              size="medium"
              type="semi_bold"
              color={Colors.text.titleColor}
              style={styles.charityName}>
              {charity.name}
            </Text>
            <Text
              //  numberOfLines={1}
              size="small"
              type="medium"
              color={Colors.text.gray}
              style={styles.charityDescription}>
              {charity.description}
            </Text>
          </View>
        </View>
        <View style={styles.radioContainer}>
          <View
            style={[styles.radioButton, isSelected && styles.radioSelected]}>
            {isSelected && <View style={styles.radioInner} />}
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <Modal visible={visible} transparent animationType="fade">
      <View style={styles.overlay}>
        <View style={styles.modalContainer}>
          <TouchableOpacity style={styles.closeButton} onPress={handleClose}>
            <Text style={styles.closeText}>✕</Text>
          </TouchableOpacity>
          <View style={{alignSelf: 'center'}}>
            <LOVE_GLOBAL height={40} width={40} />
          </View>
          <Text
            size="large"
            type="semi_bold"
            color={Colors.text.titleColor}
            style={styles.title}>
            {CHARITY_VOTING.VOTING_MODAL_TITLE}
          </Text>
          <Text
            size={Fonts.size.xxxSmall}
            color={Colors.text.black}
            style={styles.subtitle2}>
            {CHARITY_VOTING.VOTING_MODAL_SUBTITLE_2}
          </Text>
          <Text
            size={Fonts.size.xxxSmall}
            type="bold"
            color={Colors.text.black}
            style={styles.subtitle}>
            {CHARITY_VOTING.VOTING_MODAL_SUBTITLE}
          </Text>

          <ScrollView
            style={styles.charitiesList}
            showsVerticalScrollIndicator={false}>
            {charities.map(renderCharityOption)}
          </ScrollView>

          <View style={styles.buttonContainer}>
            <ButtonView style={styles.cancelButton} onPress={handleClose}>
              <Text
                size="small"
                type="medium"
                color={Colors.text.gray}
                style={styles.cancelText}>
                {CHARITY_VOTING.CANCEL}
              </Text>
            </ButtonView>

            <AppButton
              text={CHARITY_VOTING.SUBMIT_VOTE}
              buttonStye={[
                styles.submitButton,
                !selectedCharityId && styles.disabledButton,
              ]}
              textColor={selectedCharityId ? Colors.white : Colors.text.gray}
              onPress={handleSubmit}
              size="small"
              type="semi_bold"
              disabled={!selectedCharityId}
            />
          </View>
        </View>
      </View>
    </Modal>
  );
};

export default CharityVotingModal;
