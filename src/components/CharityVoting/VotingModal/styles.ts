import {StyleSheet} from 'react-native';
import {Colors, Fonts, Metrics} from '../../../theme';

export const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    width: Metrics.screenWidth * 0.85,
    maxHeight: Metrics.screenHeight * 0.8,
    backgroundColor: Colors.white,
    borderRadius: 20,
    padding: 20,
    paddingTop: 30,
    position: 'relative',
  },
  closeButton: {
    position: 'absolute',
    top: Metrics.ratio(15),
    right: Metrics.ratio(15),
    zIndex: 1,
    padding: Metrics.ratio(8),
  },
  closeText: {
    fontSize: Metrics.ratio(18),
    color: Colors.text.gray,
    fontWeight: 'bold',
  },
  title: {
    fontSize: Metrics.ratio(18),
    fontFamily: Fonts.type.bold,
    textAlign: 'center',
    marginTop: Metrics.ratio(10),
    marginBottom: Metrics.ratio(8),
  },
  subtitle: {
    textAlign: 'center',
    marginVertical: Metrics.ratio(10),
  },
  subtitle2: {
    textAlign: 'center',
    // marginBottom: Metrics.ratio(20),
  },
  charitiesList: {
    maxHeight: Metrics.screenHeight * 0.4,
    marginBottom: Metrics.ratio(20),
  },
  charityOption: {
    justifyContent: 'center',
    flexDirection: 'row',
    alignItems: 'center',
    padding: Metrics.ratio(16),
    marginBottom: Metrics.ratio(12),
    borderRadius: Metrics.ratio(8),
    borderWidth: 1,
    borderColor: Colors.gray2,
    backgroundColor: Colors.white,
  },
  selectedOption: {
    borderColor: Colors.black,
    backgroundColor: Colors.lightGray1,
  },
  radioContainer: {
    marginRight: Metrics.ratio(12),
    marginTop: Metrics.ratio(2),
  },
  radioButton: {
    width: Metrics.ratio(20),
    height: Metrics.ratio(20),
    borderRadius: Metrics.ratio(10),
    borderWidth: 2,
    borderColor: Colors.gray2,
    alignItems: 'center',
    justifyContent: 'center',
  },
  radioSelected: {
    borderColor: Colors.black,
  },
  radioInner: {
    width: Metrics.ratio(10),
    height: Metrics.ratio(10),
    borderRadius: Metrics.ratio(5),
    backgroundColor: Colors.black,
  },
  charityInfo: {
    // backgroundColor: 'red',
    alignItems: 'center',
    flexDirection: 'row',
    flex: 1,
  },
  charityName: {
    fontFamily: Fonts.type.semi_bold,
    marginLeft: Metrics.ratio(12),
    marginBottom: Metrics.ratio(4),
    fontSize: Metrics.ratio(12),
    color: Colors.black,
  },
  charityDescription: {
    marginLeft: Metrics.ratio(12),

    fontSize: Metrics.ratio(10),
    maxWidth: Metrics.screenWidth * 0.5,
    lineHeight: Metrics.ratio(18),
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  cancelButton: {
    flex: 1,
    marginRight: Metrics.ratio(8),
    paddingVertical: Metrics.ratio(12),
    paddingHorizontal: Metrics.ratio(16),
    borderRadius: Metrics.ratio(6),
    borderWidth: 1,
    borderColor: Colors.gray2,
    backgroundColor: Colors.white,
    alignItems: 'center',
    justifyContent: 'center',
  },
  cancelText: {
    textAlign: 'center',
  },
  submitButton: {
    flex: 1,
    marginLeft: Metrics.ratio(8),
    marginHorizontal: 0,
    height: Metrics.ratio(44),
  },
  disabledButton: {
    backgroundColor: Colors.gray2,
  },
  avatarImage: {
    overflow: 'hidden',

    // alignSelf: 'center',
    //backgroundColor: 'red',
    height: Metrics.ratio(32),
    width: Metrics.ratio(32),
  },
  avatarContainer: {
    marginTop: Metrics.ratio(-30),
    // alignSelf: 'center',
    height: Metrics.ratio(25),
    width: Metrics.ratio(25),
    marginRight: Metrics.ratio(12),
  },
});
