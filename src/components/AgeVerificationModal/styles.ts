import {StyleSheet} from 'react-native';
import {Colors, Fonts, Metrics} from '../../theme';

export const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  container: {
    backgroundColor: Colors.white,
    borderRadius: Metrics.ratio(16),
    padding: Metrics.ratio(24),
    margin: Metrics.ratio(20),
    maxWidth: Metrics.ratio(380),
    width: '90%',
    alignItems: 'center',
  },
  title: {
    fontSize: Fonts.size.large,
    fontWeight: 'bold',
    color: Colors.black,
    textAlign: 'center',
    marginBottom: Metrics.ratio(16),
  },
  description: {
    fontSize: Fonts.size.medium,
    color: Colors.black,
    textAlign: 'center',
    marginBottom: Metrics.ratio(24),
    lineHeight: Metrics.ratio(22),
  },
  loadingContainer: {
    alignItems: 'center',
    paddingVertical: Metrics.ratio(20),
  },
  loadingText: {
    fontSize: Fonts.size.medium,
    color: Colors.black,
    marginTop: Metrics.ratio(12),
    textAlign: 'center',
  },
  actionsContainer: {
    width: '100%',
    gap: Metrics.ratio(12),
  },
  instructionText: {
    fontSize: Fonts.size.medium,
    color: Colors.black,
    textAlign: 'center',
    marginBottom: Metrics.ratio(16),
    fontWeight: '500',
    lineHeight: Metrics.ratio(22),
  },
  primaryButton: {
    backgroundColor: Colors.black,
    borderRadius: Metrics.ratio(8),
    paddingVertical: Metrics.ratio(14),
    paddingHorizontal: Metrics.ratio(24),
  },
  secondaryButton: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: Colors.black,
    borderRadius: Metrics.ratio(8),
    paddingVertical: Metrics.ratio(14),
    paddingHorizontal: Metrics.ratio(24),
  },
  noteText: {
    fontSize: Fonts.size.small,
    color: Colors.black,
    textAlign: 'center',
    marginTop: Metrics.ratio(16),
    fontStyle: 'italic',
    lineHeight: Metrics.ratio(18),
  },
  dobInputContainer: {
    backgroundColor: Colors.lightGray,
    borderRadius: Metrics.ratio(8),
    padding: Metrics.ratio(16),
    marginBottom: Metrics.ratio(16),
    width: '100%',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: Colors.gray,
  },
  dobInputText: {
    fontSize: Fonts.size.large,
    color: Colors.black,
    fontWeight: 'bold',
    marginBottom: Metrics.ratio(4),
  },
  dobInputLabel: {
    fontSize: Fonts.size.small,
    color: Colors.gray,
    textAlign: 'center',
  },
});
