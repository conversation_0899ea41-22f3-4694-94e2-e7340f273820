// @flow
import {StyleSheet} from 'react-native';
import {Metrics, Colors} from '../../theme';

export default StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: 'row',
    padding: Metrics.baseMargin,
    // backgroundColor: Colors.primary.backgroundColor,
  },
  button: {
    backgroundColor: Colors.background.publishInactiveColor,
    paddingHorizontal: Metrics.baseMargin,
    paddingVertical: Metrics.smallMargin,
    borderRadius: Metrics.ratio(6, 6),
  },
  selectedColor: {backgroundColor: Colors.background.publishActiveColor},
});
