import React from 'react';
import {ButtonView, Text} from '..';
import {Colors} from '../../theme';
import {CREATE_IMPRINT} from '../../constants/StringConstants';
import styles from './styles';

interface publishButton {
  textColor?: string;
  isPublish: boolean;
  onPress: () => void;
  buttonTitle?: string;
  size?:
    | 'small'
    | 'buttonText'
    | 'xxxxSmall'
    | 'xxxSmall'
    | 'xxSmall'
    | 'xSmall'
    | 'normal'
    | 'medium'
    | 'Large'
    | 'large'
    | 'xLarge'
    | 'xxLarge'
    | 'xxxLarge'
    | 'xxxxLarge';
}

const PublishButton: React.FC<publishButton> = ({
  textColor,
  isPublish = false,

  onPress,
  buttonTitle,
  size,
}) => {
  return (
    <ButtonView
      disabled={!isPublish}
      onPress={onPress}
      style={[styles.button, isPublish ? styles.selectedColor : {}]}>
      <Text
        color={
          textColor
            ? textColor
            : isPublish
            ? Colors.white
            : Colors.text.publishTextInActiveColor
        }
        size={size ?? 'buttonText'}
        type={textColor ? 'semi_bold' : undefined}>
        {buttonTitle ? buttonTitle : CREATE_IMPRINT.PUBLISH}
      </Text>
    </ButtonView>
  );
};

export default PublishButton;
