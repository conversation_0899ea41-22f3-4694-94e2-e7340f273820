import AsyncStorage from '@react-native-async-storage/async-storage';

function rejectWithMessage(error: any) {
  return Promise.reject(error.message);
}

export default (
  key: string,
  replacer: ((this: any, key: string, value: any) => any) | undefined,
  reviver: any,
) => ({
  async load() {
    try {
      return await new Promise(async (resolve, reject) => {
        try {
          const jsonState = await AsyncStorage.getItem(key);
          const state = JSON.parse(jsonState) || {};
          resolve(state);
        } catch (error) {
          reject(error);
        }
      });
    } catch (error) {
      return rejectWithMessage(error);
    }
  },

  async save(state: any) {
    try {
      await new Promise<void>((resolve, reject) => {
        const jsonState = JSON.stringify(state, replacer);
        AsyncStorage.setItem(key, jsonState)
          .then(() => {
            resolve();
          })
          .catch(error => {
            reject(error);
          });
      });
    } catch (error) {
      return rejectWithMessage(error);
    }
  },
});
