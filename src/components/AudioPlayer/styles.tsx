import {StyleSheet} from 'react-native';
import {Metrics, Colors} from '../../theme';

export const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  waveformContainer: {
    width: '100%',
    height: Metrics.ratio(50),
    marginVertical: Metrics.ratio(20),
    backgroundColor: '#F5F5F5',
    borderRadius: Metrics.ratio(8) as number,
    overflow: 'visible',
  },
  controls: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '90%',
  },

  handleStyle: {
    backgroundColor: Colors.handle,
    height: Metrics.ratio(6),
    borderRadius: Metrics.ratio(8),
    width: Metrics.ratio(45),
  },
  bottomSheet: {
    elevation: 10,
    shadowColor: Colors.black,
    shadowOffset: {
      width: 0,
      height: Metrics.ratio(12) as number,
    },
    shadowOpacity: 0.58,
    shadowRadius: 16.0,
  },
});
