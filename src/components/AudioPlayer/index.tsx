import React, {useState} from 'react';
import {ButtonView} from '..';
import {PAUSE, PLAY_ICON} from '../../constants/AssetSVGConstants';
import {styles} from './styles';
import {View} from 'react-native';
import Svg, {Rect} from 'react-native-svg';
// const BAR_SPACING = 1; // Space between bars

const BAR_WIDTH = 4;
const BAR_SPACING = 2;

const AudioPlayer = ({waveData, recordedFilePath, onPlay, onPause}) => {
  const [isPlaying, setIsPlaying] = useState(false);

  const handlePlayPause = () => {
    if (isPlaying) {
      onPause();
    } else {
      onPlay();
    }
    setIsPlaying(!isPlaying);
  };

  return (
    <View style={styles.container}>
      <View style={styles.waveformContainer}>
        <Svg height="50" width="100%">
          {waveData.map((bar, index) => (
            <Rect
              key={index}
              x={index * (BAR_WIDTH + BAR_SPACING)}
              y={50 - bar.value}
              width={BAR_WIDTH}
              height={bar.value}
              fill={bar.color}
              rx="2"
            />
          ))}
        </Svg>
      </View>
      <View style={styles.controls}>
        {recordedFilePath && (
          <ButtonView onPress={handlePlayPause}>
            {isPlaying ? <PAUSE /> : <PLAY_ICON />}
          </ButtonView>
        )}
      </View>
    </View>
  );
};

export default AudioPlayer;
