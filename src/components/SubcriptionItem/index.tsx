import React, {useEffect, useState} from 'react';
import {Platform, View} from 'react-native';
import {Colors, Fonts} from '../../theme';
import {AppButton, Text} from '../../components';
import {PLANS_HEADER} from '../../constants/AssetSVGConstants';
import {UserState} from '../../types';
import {
  getAvailablePurchases,
  SubscriptionAndroid,
  type Subscription,
} from 'react-native-iap';
import {ProPlans} from '../../constants';
import {styles} from './styles';
import {SUBSCRIPTION} from '../../constants/StringConstants';
import {isUserSubscribed, normalizePlanId} from '../../util';

interface ISubscriptionItemProps {
  item: Subscription | SubscriptionAndroid | (typeof ProPlans)[0];
  onPurchasedClick: (
    item: Subscription | SubscriptionAndroid | (typeof ProPlans)[0],
  ) => void;
  user: UserState;
}

const SubscriptionItem: React.FC<ISubscriptionItemProps> = ({
  item,
  onPurchasedClick,
  user,
}) => {
  const [activePlan, setActivePlan] = useState<string | null>(null);

  useEffect(() => {
    const fetchActivePlan = async () => {
      try {
        const purchases = await getAvailablePurchases();
        console.log('Available Purchases:', purchases.length);

        const matchedPurchases = purchases.filter(purchase =>
          ['Basic_plans', 'basic_plan_annual'].includes(purchase.productId),
        );

        if (matchedPurchases.length > 0) {
          // Sort by latest transaction date
          const latestPurchase = matchedPurchases.sort(
            (a, b) => b.transactionDate - a.transactionDate,
          )[0];

          setActivePlan(latestPurchase.productId);
        } else {
          setActivePlan(null); // No matching plan found
        }
      } catch (error) {
        console.error('Error fetching active plan:', error);
        setActivePlan(null);
      }
    };

    fetchActivePlan();
  }, []);

  const isProPlan = (item: any): item is (typeof ProPlans)[0] => {
    return 'charity' in item && 'price' in item && 'currency' in item;
  };

  const getButtonText = () => {
    const {billingType, subscriptionStatus, registrationSource} = user.userInfo;

    if (isProPlan(item)) {
      return 'Coming Soon';
    }

    if (registrationSource === null) {
      return 'Become a Member';
    }

    const isStripe =
      (registrationSource === 'STRIPE' && subscriptionStatus === 'active') ||
      subscriptionStatus === 'trialing' ||
      subscriptionStatus === 'cancellation_scheduled' ||
      subscriptionStatus === 'paused';

    const isApple = activePlan && registrationSource !== 'STRIPE';

    if (isProPlan(item)) return 'Coming Soon';

    if (isStripe) {
      if (billingType === 'month') {
        return item.productId === 'basic_plan_annual'
          ? 'Upgrade to Yearly'
          : 'View Portal';
      }

      if (billingType === 'year') {
        return item.productId === 'Basic_plans'
          ? 'Downgrade Monthly'
          : 'View Portal';
      }

      return 'Become a Member'; // fallback
    }

    // Apple (mobile) purchase
    if (isApple) {
      if (activePlan === 'Basic_plans') {
        return item.productId === 'basic_plan_annual'
          ? 'Upgrade to Yearly'
          : 'View Portal';
      } else if (activePlan === 'basic_plan_annual') {
        return item.productId === 'Basic_plans'
          ? 'Downgrade Monthly'
          : 'View Portal';
      }
    }

    // No plan
    return 'Become a Member';
  };

  const isButtonDisabled = () => {
    if (isProPlan(item)) {
      return true; // Disable ProPlans
    }

    return false;
  };

  const getPlanSuffix = (item: Subscription | SubscriptionAndroid): string => {
    if (Platform.OS === 'android') {
      const billingPeriod =
        item?.subscriptionOfferDetails?.[0]?.pricingPhases
          ?.pricingPhaseList?.[0]?.billingPeriod;
      if (billingPeriod?.includes('Y')) return '/year';
      if (billingPeriod?.includes('M')) return '/month';
    }

    if (Platform.OS === 'ios') {
      if (
        item?.productId?.includes('annual') ||
        item?.title?.toLowerCase().includes('year')
      ) {
        return '/year';
      }
    }

    return '/month';
  };

  return (
    <View style={styles.cardStyle}>
      <PLANS_HEADER />
      <View style={styles.cardHeader}>
        <Text color={Colors.black} type="bold" size={Fonts.size.small}>
          {Platform.OS === 'ios' ? item?.title : item?.name}
        </Text>
        <View style={styles.priceRow}>
          {isProPlan(item) ? (
            <Text color={Colors.BlackButton} size={Fonts.size.xxSmall}>
              {item.currency} {parseFloat(item.price.toString()).toFixed(2)}
            </Text>
          ) : Platform.OS === 'ios' ? (
            <Text color={Colors.BlackButton} size={Fonts.size.xxSmall}>
              {(item as any).localizedPrice || (item as any).price}
            </Text>
          ) : (
            <Text color={Colors.BlackButton} size={Fonts.size.xxSmall}>
              {(item as any).subscriptionOfferDetails?.[0]?.pricingPhases
                ?.pricingPhaseList?.[0]?.formattedPrice || 'N/A'}
            </Text>
          )}
          <Text
            type={'base'}
            size={'xxxSmall'}
            color={SUBSCRIPTION.MONTH_TEXT_COLOR}>
            {getPlanSuffix(item)}
            {/* {SUBSCRIPTION.MONTH_SUFFIX} */}
          </Text>
        </View>
      </View>
      <Text
        size={Fonts.size.xxxSmall}
        color={SUBSCRIPTION.DESCRIPTION_TEXT_COLOR}
        style={styles.descriptionText}>
        {item.description}
        {Platform.OS === 'ios' && item.title === SUBSCRIPTION.BASIC_PLAN_NAME
          ? `${SUBSCRIPTION.BASIC_PLAN_DESCRIPTION_SUFFIX}`
          : ''}
      </Text>

      {Platform.OS === 'ios' ? (
        <AppButton
          text={getButtonText()}
          size={Fonts.size.xSmall}
          onPress={() => onPurchasedClick(item)}
          disabled={isButtonDisabled()}
          textColor={Colors.white}
          buttonStye={[
            styles.button,
            isProPlan(item)
              ? {backgroundColor: Colors.gray} // ProPlans are disabled
              : activePlan === null || activePlan !== item.productId
              ? {backgroundColor: Colors.black} // Inactive plans
              : user.userInfo.registrationSource === 'STRIPE'
              ? {backgroundColor: Colors.black} // Active plans
              : {backgroundColor: Colors.gray}, // Active plans
          ]}
        />
      ) : (
        <AppButton
          text={
            item.name == SUBSCRIPTION.BASIC_PLAN_NAME
              ? !isUserSubscribed(user)
                ? SUBSCRIPTION.INVESTED_BUTTON_TEXT
                : 'View Plan'
              : SUBSCRIPTION.COMING_SOON_BUTTON_TEXT
          }
          size={Fonts.size.xSmall}
          onPress={() => onPurchasedClick(item)}
          disabled={item.name == SUBSCRIPTION.BASIC_PLAN_NAME ? false : true}
          textColor={Colors.white}
          buttonStye={[
            styles.button,
            item.name !== SUBSCRIPTION.BASIC_PLAN_NAME
              ? {backgroundColor: Colors.gray}
              : !isUserSubscribed(user)
              ? {backgroundColor: Colors.black}
              : {backgroundColor: Colors.gray},
          ]}
        />
      )}

      <View
        style={[
          styles.charityInfoContainer,
          {borderColor: SUBSCRIPTION.BORDER_COLOR},
        ]}>
        <View style={styles.charitySection}>
          <Text
            color={SUBSCRIPTION.HIGHLIGHT_COLOR}
            type="bold"
            size={Fonts.size.medium}>
            {item.title === SUBSCRIPTION.BASIC_PLAN_NAME
              ? SUBSCRIPTION.CHARITY_AMOUNT
              : isProPlan(item)
              ? '£' + item.charity
              : SUBSCRIPTION.CHARITY_AMOUNT}
          </Text>
          <Text
            color={Colors.black}
            size={Fonts.size.xxxSmall}
            type="semi_bold"
            style={styles.charityLabel}>
            {SUBSCRIPTION.MONTHLY_CHARITY_LABEL}
          </Text>
        </View>
        <View style={styles.divider} />
        <View style={styles.powerSection}>
          <Text
            color={SUBSCRIPTION.HIGHLIGHT_COLOR}
            type="bold"
            size={Fonts.size.medium}>
            {item.title === SUBSCRIPTION.BASIC_PLAN_NAME
              ? SUBSCRIPTION.POWER_MULTIPLIER
              : isProPlan(item)
              ? item.charity + 'x'
              : SUBSCRIPTION.POWER_MULTIPLIER}
          </Text>
          <Text
            color={Colors.black}
            size={Fonts.size.xxxSmall}
            type="semi_bold"
            style={styles.powerLabel}>
            {SUBSCRIPTION.LIKE_IMPRINT_POWER_LABEL}
          </Text>
        </View>
      </View>
      <Text
        color={SUBSCRIPTION.DESCRIPTION_TEXT_COLOR}
        size={Fonts.size.xxxSmall}
        style={styles.disclaimerText}>
        {SUBSCRIPTION.PROCESSING_FEE_DISCLAIMER}
      </Text>
    </View>
  );
};

export default SubscriptionItem;
