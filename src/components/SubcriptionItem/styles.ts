import {Platform, StyleSheet} from 'react-native';
import {Colors, Fonts, Metrics} from '../../theme';

export const styles = StyleSheet.create({
  cardStyle: {
    paddingVertical: Metrics.baseMargin,
    alignItems: 'center',
    shadowColor: Colors.itemColors.itemBackgroundColor,
    shadowOffset: {
      width: 0,
      height: 12,
    },
    shadowOpacity: 0.58,
    shadowRadius: 16.0,
    elevation: 24,
    borderRadius: Metrics.ratio(10),
    borderColor: Colors.itemColors.itemBackgroundColor,
    backgroundColor: Colors.white,
    height:
      Platform.OS == 'android'
        ? Metrics.screenHeight * 0.72
        : Metrics.screenHeight * 0.6,
    marginTop: Metrics.doubleBaseMargin,
    marginHorizontal: Metrics.smallMargin,
  },
  cardHeader: {
    marginTop: Metrics.smallMargin,
    justifyContent: 'space-between',
    flexDirection: 'row',
    alignItems: 'center',
    width: '100%',
    paddingHorizontal: Metrics.baseMargin,
    //  paddingVertical: Metrics.smallMargin,
  },
  checkItem: {
    alignItems: 'center',
    flexDirection: 'row',
    marginVertical: Metrics.ratio(10),
    marginHorizontal: Metrics.baseMargin,
  },
  checkItemText: {
    marginLeft: Metrics.ratio(20),
    width: Metrics.screenWidth * 0.6,
  },
  button: {
    marginTop: Metrics.doubleBaseMargin,
    width: '85%',
    backgroundColor: '#D2D5DB',
    borderWidth: 2,
    borderColor: '#D2D5DB',
  },

  priceContainer: {
    backgroundColor: 'red',
    flexDirection: 'row',
    alignItems: 'center',
  },
  // New styles moved from inline
  descriptionText: {
    width: '90%',
  },
  charityInfoContainer: {
    paddingVertical: Metrics.ratio(20),
    width: '90%',
    flexDirection: 'row',
    borderBottomWidth: 1,
    alignItems: 'center',
    justifyContent: 'space-around',
    borderColor: '#D2D5DB', // Will be overridden by SUBSCRIPTION constant
  },
  charitySection: {
    width: '40%',
    alignItems: 'center',
  },
  divider: {
    width: 1,
    height: 42,
    backgroundColor: '#D9D9D9', // Will be overridden by SUBSCRIPTION constant
  },
  powerSection: {
    width: '40%',
    alignItems: 'center',
  },
  charityLabel: {
    marginTop: 5, // Replaces AppStyles.mTop5
  },
  powerLabel: {
    marginTop: 5, // Replaces AppStyles.mTop5
  },
  disclaimerText: {
    paddingHorizontal: 20,
    marginTop: 10,
  },
  // Layout styles to replace AppStyles
  priceRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
});
