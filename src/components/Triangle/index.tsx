import React from 'react';
import {View, StyleSheet, Dimensions} from 'react-native';

const Triangle = () => {
  return <View style={styles.triangle} />;
};

export default Triangle;

const styles = StyleSheet.create({
  triangle: {
    alignSelf: 'center',
    transform: [{rotate: '180deg'}],
    bottom: -25,
    width: 0,
    height: 0,
    backgroundColor: 'transparent',
    borderStyle: 'solid',
    borderLeftWidth: 10,
    borderRightWidth: 10,
    borderBottomWidth: 15,
    borderLeftColor: 'transparent',
    borderRightColor: 'transparent',
    borderBottomColor: '#000',
  },
});
