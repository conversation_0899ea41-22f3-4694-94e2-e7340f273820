import {View} from 'react-native';
import styles from './styles';
import {IAboutCardHeader} from '../../types';
import AboutCardHeader from './AboutHeader';
import AboutTitleSubtitle from './AboutTitleSubtitle';
import AboutTitleSubtitleImage from './AboutTitleSubtitleImage';
import {ABOUT} from '../../constants/StringConstants';

interface AboutCardProps {
  data: IAboutCardHeader;
  onEditClicked?: (data: IAboutCardHeader) => void;
}

const AboutCard: React.FC<AboutCardProps> = ({data, onEditClicked}) => {
  const onEditClickedOption = () => {
    if (onEditClicked) {
      onEditClicked(data);
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.innerContainer}>
        <AboutCardHeader
          data={data}
          onEditClicked={onEditClicked ? onEditClickedOption : undefined}
        />
        {data.name === ABOUT.OVERVIEW ||
        data.name === ABOUT.PERSONAL_INFO ||
        data.name === ABOUT.CONTACT ? (
          <AboutTitleSubtitle
            data={data.details}
            onEditClicked={onEditClicked ? onEditClickedOption : undefined}
          />
        ) : (
          <AboutTitleSubtitleImage data={data.details} />
        )}
      </View>
    </View>
  );
};

export default AboutCard;
