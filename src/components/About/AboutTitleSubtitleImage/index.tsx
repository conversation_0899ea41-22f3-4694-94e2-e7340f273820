import {Image, Pressable, Text, View} from 'react-native';
import styles from './styles';
import {EDIT_ICON} from '../../../constants/AssetSVGConstants';

interface AboutTitleSubtitleProps {
  onEditPress?: (item: any) => () => void;
  data: any[];
}

const AboutTitleSubtitleImage: React.FC<AboutTitleSubtitleProps> = ({
  onEditPress,
  data,
}) => {
  return (
    <View style={styles.container}>
      {data?.map((item, index) => (
        <View key={index} style={styles.mainContainer}>
          <Image source={{uri: item.logo}} style={styles.image} />
          <View style={styles.nameContainer}>
            <Text style={styles.name} numberOfLines={2}>
              {item.name}
            </Text>
            <View style={styles.timeContainer}>
              <Text style={styles.class}>{item.date}</Text>
            </View>
          </View>
          {onEditPress && (
            <Pressable onPress={onEditPress(item)}>
              <EDIT_ICON />
            </Pressable>
          )}
        </View>
      ))}
    </View>
  );
};

export default AboutTitleSubtitleImage;
