import {StyleSheet} from 'react-native';
import {Colors, Metrics, Fonts} from '../../../theme';

export default StyleSheet.create({
  container: {
    flexDirection: 'column',
  },
  title: {
    fontFamily: Fonts.type.medium,
    fontSize: Metrics.ratio(14),
    color: Colors.text.homeTitleColor,
  },
  subtitle: {
    fontFamily: Fonts.type.regular,
    fontSize: Metrics.ratio(16),
    color: Colors.text.modalText,
  },
  mainContainer: {
    flexDirection: 'row',
    paddingVertical: Metrics.ratio(12),
    alignItems: 'center',
  },
  image: {
    width: Metrics.ratio(45),
    height: Metrics.ratio(45),
    borderRadius: Metrics.ratio(100),
  },
  nameContainer: {
    marginLeft: Metrics.ratio(12),
    flex: 1,
    marginRight: Metrics.ratio(30),
  },
  name: {
    fontFamily: Fonts.type.medium,
    fontSize: Metrics.ratio(14),
    color: Colors.text.homeTitleColor,
    paddingTop: Metrics.ratio(2),
  },
  class: {
    fontFamily: Fonts.type.medium,
    fontSize: Metrics.ratio(13),
    color: Colors.text.placeHolderTextColor,
    paddingTop: Metrics.ratio(2),
  },
  timeContainer: {
    paddingTop: Metrics.ratio(2),
    flexDirection: 'row',
  },
});
