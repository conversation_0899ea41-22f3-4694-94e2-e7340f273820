import {View, Text, Pressable} from 'react-native';
import styles from './styles';
import {IAboutCardHeader} from '../../../types';
import {EDIT_ICON} from '../../../constants/AssetSVGConstants';

interface AboutCardHeaderProps {
  data: IAboutCardHeader;
  onEditClicked?: () => void;
}

const AboutCardHeader: React.FC<AboutCardHeaderProps> = ({
  data,
  onEditClicked,
}) => {

  return (
    <View style={styles.container}>
      {data.leftImage}
      <Text style={styles.name}>{data.name}</Text>
      {onEditClicked ? (
        <Pressable onPress={onEditClicked}>
          <EDIT_ICON />
        </Pressable>
      ) : null}
    </View>
  );
};

export default AboutCardHeader;
