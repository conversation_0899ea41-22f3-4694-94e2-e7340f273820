import {StyleSheet} from 'react-native';
import {Colors, Metrics, Fonts, AppStyles} from '../../../theme';
import {getStatusBarHeight} from 'react-native-iphone-x-helper';

export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.home,
  },
  statusBar: {
    backgroundColor: Colors.white,
    height: getStatusBarHeight() + 30,
  },
  statusBarWithoutNotch: {
    backgroundColor: Colors.white,
    height: Metrics.ratio(30),
  },
  cardContainer: {
    borderRadius: Metrics.ratio(28),
    backgroundColor: Colors.white,
    marginHorizontal: Metrics.ratio(20),
    marginVertical: Metrics.ratio(20),
  },
  innerContainer: {
    marginHorizontal: Metrics.ratio(24),
    marginVertical: Metrics.ratio(24),
  },
});
