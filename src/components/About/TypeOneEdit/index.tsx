import {Alert, View} from 'react-native';
import {hasNotch} from 'react-native-device-info';
import {CustomNavbar, Loader} from '../..';
import styles from './styles';
import {useNavigation, useRoute} from '@react-navigation/native';
import PublishButton from '../../PublishButton';
import AboutCardHeader from '../AboutHeader';
import {
  IAboutCardHeader,
  IModerationResponse,
  IProfileModeration,
  Overview,
  UserState,
} from '../../../types';
import EditInputField from '../../EditTextInput';
import {useEffect, useState} from 'react';
import {connect} from 'react-redux';
import {
  updateProfileOverviewRequest,
  updateProfileAboutRequest,
  updateContanctRequest,
  profileModerationRequest,
} from '../../../actions/ProfileActions';
import {
  ABOUT,
  IMPRINT_SCORES,
  IMPRINT_SCORES_DISPLAY,
  TOAST_MESSAGES,
  TYPE_ONE_EDIT,
  TYPE_TWO_EDIT,
} from '../../../constants/StringConstants';
import {
  LAUGHTER,
  LIFE,
  LOVE,
  PURPOSE,
  RESPECT,
  SAFETY,
} from '../../../constants/AssetSVGConstants';
import {showToastMsg} from '../../Alert';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import {AppStyles} from '../../../theme';

interface TypeOneEditProps {
  updateProfileOverviewRequest: (
    payload: any,
    callback: (res: any) => void,
  ) => void;
  updateProfileAboutRequest: (
    payload: any,
    callback: (res: any) => void,
  ) => void;
  updateContanctRequest: (payload: any, callback: (res: any) => void) => void;
  profileModerationRequest: (
    payload: any,
    callback: (res: any) => void,
  ) => void;
  user: UserState;
}

const TypeOneEdit: React.FC<TypeOneEditProps> = ({
  updateProfileOverviewRequest,
  updateProfileAboutRequest,
  updateContanctRequest,
  profileModerationRequest,
  user,
}) => {
  const navigation = useNavigation();
  const route = useRoute();
  const {data} = route.params as {data: IAboutCardHeader};
  const [finalData, setFinalData] = useState<any>(data.details);
  const [originalData] = useState<any>(data.details); // Store the original data

  const [loading, setLoading] = useState<boolean>(false);
  const [validDisplayName, setValidDisplayName] = useState<boolean>(true);
  const [aboutContent, setAboutContent] = useState<boolean>(true);

  const [validUserName, setValidUserName] = useState<boolean>(true);
  const [validWhatIStandFor, setValidWhatIStandFor] = useState<boolean>(true);
  const [debounceTimers, setDebounceTimers] = useState<{
    displayName: NodeJS.Timeout | null;
    userName: NodeJS.Timeout | null;
    what_i_stand_for: NodeJS.Timeout | null;
  }>({
    displayName: null,
    userName: null,
    what_i_stand_for: null,
  });
  const DEBOUNCE_DELAY = 500;

  const hasChanges = () => {
    return JSON.stringify(originalData) !== JSON.stringify(finalData);
  };

  const handleDebouncedModeration = (
    key: 'displayName' | 'userName' | 'what_i_stand_for',
    text: string,
    setValidState: (value: boolean) => void,
  ) => {
    if (debounceTimers[key]) {
      clearTimeout(debounceTimers[key]!);
    }

    const timer = setTimeout(() => {
      const payLoad: IProfileModeration = {
        profileId: user.userInfo.userId,
        text: text,
      };

      profileModerationRequest(
        payLoad,
        (moderationRes: IModerationResponse) => {
          if (moderationRes) {
            setValidState(moderationRes.isValidContent);
            if (!moderationRes.isValidContent) {
              showToastMsg(`${TOAST_MESSAGES.REVIEW_CONTENT}`);
            }
          }
        },
      );
    }, DEBOUNCE_DELAY);

    setDebounceTimers(prevTimers => ({
      ...prevTimers,
      [key]: timer,
    }));
  };

  useEffect(() => {
    if (finalData[0]?.subtitle) {
      handleDebouncedModeration(
        'displayName',
        finalData[0].subtitle,
        setValidDisplayName,
      );
    }
  }, [finalData[0]?.subtitle]);

  useEffect(() => {
    if (finalData[1]?.subtitle) {
      handleDebouncedModeration(
        'userName',
        finalData[1].subtitle,
        setValidUserName,
      );
    }
  }, [finalData[1]?.subtitle]);

  useEffect(() => {
    if (finalData[2]?.subtitle) {
      handleDebouncedModeration(
        'what_i_stand_for',
        finalData[2].subtitle,
        setValidWhatIStandFor,
      );
    }
  }, [finalData[2]?.subtitle]);

  const onSaveClicked = () => {
    if (!validDisplayName) {
      showToastMsg(TOAST_MESSAGES.VALID_DISPLAYNAME_ERROR);
      return;
    }

    if (!validUserName) {
      showToastMsg(TOAST_MESSAGES.VALID_USERNAME_ERROR);
      return;
    }

    if (!validWhatIStandFor) {
      showToastMsg(TOAST_MESSAGES.WHAT_I_STAND_ERROR);
      return;
    }

    if (!aboutContent) {
      showToastMsg(TOAST_MESSAGES.ABOUT_YOU);
      return;
    }

    if (data.name === ABOUT.OVERVIEW) {
      if (finalData[3].subtitle === '') {
        showToastMsg(TOAST_MESSAGES.VALUE_MOST);
        return;
      }

      if (finalData[0].subtitle === '') {
        showToastMsg(TOAST_MESSAGES.PLEASE_ENTER_DISPLAY_NAME);
        return;
      }

      const payLoad: Overview = {
        displayName: finalData[0].subtitle,
        userName: finalData[1].subtitle,
        what_i_stand_for:
          finalData[2].subtitle === '' ? null : finalData[2].subtitle,
        what_i_value: finalData[3].subtitle,
      };

      setLoading(true);
      updateProfileOverviewRequest(payLoad, (res: any) => {
        if (res) {
          navigation.goBack();
        }
        setLoading(false);
      });
    } else if (data.name === ABOUT.PERSONAL_INFO) {
      const payLoad = {
        about: finalData[0].subtitle,
      };
      setLoading(true);
      updateProfileAboutRequest(payLoad, (res: any) => {
        if (res) {
          navigation.goBack();
        }
        setLoading(false);
      });
    } else if (data.name === ABOUT.CONTACT) {
      if (finalData[1].subtitle === '') {
        showToastMsg(TOAST_MESSAGES.SELECT_GENDER);
        return;
      }
      if (finalData[2].subtitle === '') {
        showToastMsg(TOAST_MESSAGES.ABOUT_YOU);
        return;
      }

      const payLoad = {
        dob: finalData[0].subtitle,
        gender: finalData[1].subtitle,
        about: finalData[2].subtitle,
      };
      updateContanctRequest(payLoad, (res: any) => {
        if (res) {
          navigation.goBack();
        }
      });
    }
  };

  const [debounceTimer, setDebounceTimer] = useState<NodeJS.Timeout | null>(
    null,
  );

  useEffect(() => {
    if (data.name === ABOUT.CONTACT && finalData[2]?.subtitle) {
      if (debounceTimer) {
        clearTimeout(debounceTimer);
      }

      const timer = setTimeout(() => {
        const payLoad: IProfileModeration = {
          profileId: user.userInfo.userId,
          text: finalData[2].subtitle,
        };

        profileModerationRequest(
          payLoad,
          (moderationRes: IModerationResponse) => {
            if (moderationRes) {
              setAboutContent(moderationRes.isValidContent);
              if (!moderationRes.isValidContent) {
                showToastMsg(TOAST_MESSAGES.VALID_ABOUT_CONTENT);
              }
            }
          },
        );
      }, DEBOUNCE_DELAY);

      setDebounceTimer(timer);

      return () => clearTimeout(timer);
    }
  }, [finalData[2]?.subtitle, aboutContent]);

  const constructSymbolsData = () => {
    if (data.name !== ABOUT.OVERVIEW) {
      return [];
    }

    const symbolData = [
      {
        title: IMPRINT_SCORES_DISPLAY.LOVE,
        image: <LOVE />,
        selected: data?.details?.[3]?.subtitle?.includes(IMPRINT_SCORES.LOVE),
      },
      {
        title: IMPRINT_SCORES_DISPLAY.LIFE,
        image: <LIFE />,
        selected: data?.details?.[3]?.subtitle?.includes(IMPRINT_SCORES.LIFE),
      },
      {
        title: IMPRINT_SCORES_DISPLAY.RESPECT,
        image: <RESPECT />,
        selected: data?.details?.[3]?.subtitle?.includes(
          IMPRINT_SCORES.RESPECT,
        ),
      },
      {
        title: IMPRINT_SCORES_DISPLAY.SAFETY,
        image: <SAFETY />,
        selected: data?.details?.[3]?.subtitle?.includes(IMPRINT_SCORES.SAFETY),
      },
      {
        title: IMPRINT_SCORES_DISPLAY.LAUGHTER,
        image: <LAUGHTER />,
        selected: data?.details?.[3]?.subtitle?.includes(
          IMPRINT_SCORES.LAUGHTER,
        ),
      },
      {
        title: IMPRINT_SCORES_DISPLAY.PURPOSE,
        image: <PURPOSE />,
        selected: data?.details?.[3]?.subtitle?.includes(
          IMPRINT_SCORES.PURPOSE,
        ),
      },
    ];
    return symbolData;
  };
  const handleBackNavigation = () => {
    if (hasChanges()) {
      Alert.alert(
        TYPE_ONE_EDIT.SAVE_CHANGES,
        TYPE_ONE_EDIT.SAVE_CHANGES_DETAIL,
        [
          {
            text: 'Discard',
            style: 'destructive',
            onPress: () => navigation.goBack(),
          },
          {
            text: 'Cancel',
            style: 'cancel',
          },
          {
            text: 'Save',
            onPress: onSaveClicked,
          },
        ],
      );
    } else {
      navigation.goBack();
    }
  };

  return (
    <View style={styles.container}>
      <View
        style={
          hasNotch() ? styles.statusBar : styles.statusBarWithoutNotch
        }></View>
      <CustomNavbar
        title={data.name}
        titleSize={16}
        //  alignTitleLeft={true}
        titleContainer={AppStyles.mRight25}
        leftBtnPress={handleBackNavigation}
        hasMultiRight={true}
        hasRight
        rightBtnImage={
          <PublishButton
            buttonTitle={TYPE_ONE_EDIT.SAVE}
            isPublish={true}
            onPress={() => {
              onSaveClicked();
            }}
          />
        }
        rightBtnPress2={() => alert('messagePress')}
      />
      <KeyboardAwareScrollView>
        <View style={styles.cardContainer}>
          <View style={styles.innerContainer}>
            <AboutCardHeader data={data} />
            {finalData.map((detailData: any, index: number) => (
              <EditInputField
                key={index}
                data={detailData}
                enabled={
                  detailData.title === TYPE_ONE_EDIT.USERNAME ||
                  detailData.title === TYPE_ONE_EDIT?.EMAIL
                    ? false
                    : true
                }
                setFinalData={setFinalData}
                finalData={finalData}
                symbolData={constructSymbolsData()}
              />
            ))}
          </View>
        </View>
      </KeyboardAwareScrollView>
      <Loader loading={loading} />
    </View>
  );
};

const mapStateToProps = (state: any) => ({
  user: state.user,
});

export default connect(mapStateToProps, {
  updateProfileOverviewRequest,
  updateProfileAboutRequest,
  profileModerationRequest,
  updateContanctRequest,
})(TypeOneEdit);
