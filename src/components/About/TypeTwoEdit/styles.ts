import {StyleSheet} from 'react-native';
import {Colors, Metrics, Fonts} from '../../../theme';
import {getStatusBarHeight} from 'react-native-iphone-x-helper';

export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.home,
  },
  statusBar: {
    backgroundColor: Colors.white,
    height: Metrics.ratio(getStatusBarHeight() + 30),
  },
  statusBarWithoutNotch: {
    backgroundColor: Colors.white,
    height: Metrics.ratio(30),
  },
  cardContainer: {
    borderRadius: Metrics.ratio(28),
    backgroundColor: Colors.white,
    marginHorizontal: Metrics.ratio(20),
    marginVertical: Metrics.ratio(20),
  },
  innerContainer: {
    marginHorizontal: Metrics.ratio(24),
    marginVertical: Metrics.ratio(24),
  },
  button: {
    marginTop: Metrics.ratio(12),
    backgroundColor: Colors.gray3,
    alignSelf: 'flex-start',
    paddingHorizontal: Metrics.ratio(10),
    paddingVertical: Metrics.ratio(10),
    borderRadius: Metrics.ratio(6),
  },
  buttonText: {
    fontFamily: Fonts.type.medium,
    fontSize: Metrics.ratio(14),
    color: Colors.text.homeTitleColor,
  },
  title: {
    marginTop: Metrics.ratio(24),
    fontFamily: Fonts.type.semi_bold,
    fontSize: Metrics.ratio(14),
    color: Colors.text.titleColor,
  },
  compomentContainer: {
    marginHorizontal: Metrics.ratio(24),
  },
  deleteButton: {
    width: Metrics.ratio(170),
    backgroundColor: Colors.gray2,
    borderRadius: Metrics.ratio(6),
    marginHorizontal: Metrics.ratio(8),
  },
  confirmButton: {
    marginHorizontal: Metrics.ratio(0),
    width: Metrics.ratio(170),
  },
  keyboardAware: {
    flexGrow: 1,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingTop: Metrics.ratio(20),
  },
});
