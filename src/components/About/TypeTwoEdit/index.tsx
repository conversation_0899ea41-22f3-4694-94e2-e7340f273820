import {Pressable, Text, View} from 'react-native';
import {hasNotch} from 'react-native-device-info';
import {AppButton, CustomNavbar} from '../..';
import styles from './styles';
import {useNavigation, useRoute} from '@react-navigation/native';
import PublishButton from '../../PublishButton';
import AboutCardHeader from '../AboutHeader';
import {
  CompleteProfile,
  IAboutCardHeader,
  Relation,
  SuggestedData,
  UserInfo,
} from '../../../types';
import AboutTitleSubtitleImage from '../AboutTitleSubtitleImage';
import MoreOptionsBottomModal from '../../../components/TimeLine/MoreOptionBottomModal';
import EditInputField from '../../EditTextInput';
import {useEffect, useRef, useState} from 'react';
import {Colors} from '../../../theme';
import {connect} from 'react-redux';
import {
  getInstituteRequest,
  emptyInstitute,
  addEducationRequest,
  updateEducationRequest,
  updateFamilyRequest,
  deleteEducationRequest,
  deleteFamilyRequest,
  addEmploymentRequest,
  updateEmploymentRequest,
  deleteEmploymentRequest,
  addFamilyRequest,
  userSearchRequest,
  emptyUsers,
  getRelationEnumRequest,
} from '../../../actions/ProfileActions';
import _ from 'lodash';
import {
  constructEducationData,
  generateAboutData,
} from '../../../helpers/ProfileHelper';
import {
  ABOUT,
  TOAST_MESSAGES,
  TYPE_TWO_EDIT,
} from '../../../constants/StringConstants';
import {showToastMsg} from '../../Alert';
import Toast from 'react-native-toast-message';

interface TypeTwoEditProps {
  relationShipEnum: Relation[];
  institutes: SuggestedData[];
  users: UserInfo[];
  completeProfile: CompleteProfile;
  getInstituteRequest: (payload: any, callback: (res: any) => void) => void;

  userSearchRequest: (payload: any, callback: (res: any) => void) => void;

  addEmploymentRequest: (payload: any, callback: (res: any) => void) => void;
  updateEmploymentRequest: (payload: any, callback: (res: any) => void) => void;
  deleteEmploymentRequest: (payload: any, callback: (res: any) => void) => void;
  deleteFamilyRequest: (payload: any, callback: (res: any) => void) => void;

  addEducationRequest: (payload: any, callback: (res: any) => void) => void;
  updateEducationRequest: (payload: any, callback: (res: any) => void) => void;
  updateFamilyRequest: (payload: any, callback: (res: any) => void) => void;

  deleteEducationRequest: (payload: any, callback: (res: any) => void) => void;

  addFamilyRequest: (payload: any, callback: (res: any) => void) => void;

  getRelationEnumRequest: (callback: (res: any) => void) => void;

  emptyInstitute: () => void;
  emptyUsers: () => void;
}

const TypeTwoEdit: React.FC<TypeTwoEditProps> = ({
  getInstituteRequest,
  emptyInstitute,
  addEducationRequest,
  updateEducationRequest,
  updateFamilyRequest,
  deleteEducationRequest,
  addEmploymentRequest,
  updateEmploymentRequest,
  deleteEmploymentRequest,
  addFamilyRequest,
  userSearchRequest,
  emptyUsers,
  getRelationEnumRequest,
  deleteFamilyRequest,
  completeProfile,
  institutes,
  users,
  relationShipEnum,
}) => {
  const navigation = useNavigation();
  const route = useRoute();
  const {data} = route.params as {
    data: IAboutCardHeader;
  };
  const [visible, setVisible] = useState(false);
  const [editName, setEditName] = useState<string>('');
  const [finalData, setFinalData] = useState<any>([]);
  const [showFlatlist, setShowFlatlist] = useState<boolean>(false);
  const debouncedRequestInstitute = useRef(
    _.debounce(getInstituteRequest, 1000),
  ).current;
  const debouncedRequestUsers = useRef(
    _.debounce(userSearchRequest, 1000),
  ).current;
  const [institute, setInstitute] = useState<any>({});
  const [searchedUsers, setSearchedUsers] = useState<any>({});
  const [relation, setRelation] = useState<any>({});
  const constructedData = generateAboutData(completeProfile).filter(
    (item: any) => item.name === data.name,
  )?.[0];

  const onEditClicked = (item: any) => {
    setEditName(item.name);
    setVisible(true);
  };

  useEffect(() => {
    getRelationEnumRequest((res: any) => {});
  }, []);

  useEffect(() => {
    if (visible) {
      const updatedData = constructEducationData(
        completeProfile,
        data,
        editName,
      );
      setFinalData(updatedData);
    }
  }, [completeProfile, data, editName, visible]);

  useEffect(() => {
    if (data.name === ABOUT.EDUCATION || data.name === ABOUT.JOB) {
      const payLoad = {
        name: finalData?.[1]?.subtitle || '',
      };

      if (payLoad.name !== '' && payLoad.name.length > 1 && showFlatlist) {
        debouncedRequestInstitute(payLoad, (res: SuggestedData) => {
          setShowFlatlist(true);
        });
      } else {
        emptyInstitute();
      }
    } else if (data.name === ABOUT.FAMILY) {
      const hasEditProfile = completeProfile?.family?.find(
        profile => profile.id === finalData[0]?.subtitle,
      );

      const payLoad = {
        name:
          hasEditProfile === undefined
            ? finalData?.[0]?.subtitle
            : finalData?.[2]?.subtitle,
      };

      if (payLoad.name !== '' && payLoad.name?.length > 1 && showFlatlist) {
        debouncedRequestUsers(payLoad, (res: UserInfo[]) => {
          setShowFlatlist(true);
        });
      } else {
        emptyUsers();
      }
    }
  }, [finalData]);

  const showAddModal = () => {
    setEditName('');
    setVisible(true);
  };

  const hideModal = () => {
    setVisible(!visible);
  };

  const onConfirmClick = () => {
    let payLoad: any = {};
    if (data.name === ABOUT.EDUCATION) {
      const selectedProfile = completeProfile.education.find(
        profile => profile.id === finalData[0].subtitle,
      );

      if (
        finalData[4].subtitle === '' ||
        finalData[2].subtitle === '' ||
        finalData[3].subtitle === ''
      ) {
        showToastMsg(TOAST_MESSAGES.PLEASE_FILL);
        return;
      }

      if (selectedProfile == undefined && Object.keys(institute).length < 1) {
        return;
      }

      payLoad = {
        id: finalData[0].subtitle,
        degree: finalData[4].subtitle,
        startDate: finalData[2].subtitle,
        endDate: finalData[3].subtitle,
        type: 'Academic',
        institute:
          institute && Object.keys(institute).length > 0
            ? institute
            : selectedProfile?.institute,
      };

      if (payLoad.id === '') {
        delete payLoad.id;
        addEducationRequest(payLoad, (res: any) => {
          if (res) {
            setInstitute({});
            hideModal();
          }
        });
      } else {
        updateEducationRequest(payLoad, (res: any) => {
          if (res) {
            setInstitute({});
            hideModal();
          }
        });
      }
    } else if (data.name === ABOUT.JOB) {
      const selectedProfile = completeProfile.employment.find(
        profile => profile.id === finalData[0].subtitle,
      );

      if (
        finalData[3].subtitle === '' ||
        finalData[2].subtitle === '' ||
        finalData[4].subtitle === ''
      ) {
        showToastMsg(TOAST_MESSAGES.PLEASE_FILL);
        return;
      }

      if (selectedProfile == undefined && Object.keys(institute).length < 1) {
        return;
      }

      payLoad = {
        id: finalData[0].subtitle,
        company:
          institute && Object.keys(institute).length > 0
            ? institute
            : selectedProfile?.company,
        startDate: finalData[3].subtitle,
        endDate: finalData[4].subtitle,
        position: finalData[2].subtitle,
      };

      if (payLoad.id === '') {
        delete payLoad.id;
        addEmploymentRequest(payLoad, (res: any) => {
          if (res) {
            setInstitute({});
            hideModal();
          }
        });
      } else {
        updateEmploymentRequest(payLoad, (res: any) => {
          if (res) {
            setInstitute({});
            hideModal();
          }
        });
      }
    } else if (data.name === ABOUT.FAMILY) {
      const selectedProfile = completeProfile.family.find(
        profile => profile.id === finalData[0].subtitle,
      );

      /* remove it for edit process not working
      if (
        searchedUsers.id == undefined ||
        relation.id == undefined ||
        finalData[2].subtitle === ''
      ) {
        showToastMsg(TOAST_MESSAGES.PLEASE_FILL, 'error', 2000, 'top');
        return;
      }
    }*/

      payLoad = {
        userId: searchedUsers.id,
        relationshipId: relation.id,
        relationshipSince: finalData[2].subtitle,
      };

      if (selectedProfile) {
        payLoad = {
          id: selectedProfile.id,
          userId: searchedUsers.id ? searchedUsers.id : selectedProfile.userId,
          relationshipId: relation.id
            ? relation.id
            : selectedProfile.relationshipId,
          relationshipSince: finalData[3].subtitle,
        };
        updateFamilyRequest(payLoad, (res: any) => {
          if (res) {
            setSearchedUsers({});
            hideModal();
          }
        });
      } else {
        addFamilyRequest(payLoad, (res: any) => {
          if (res) {
            setSearchedUsers({});
            hideModal();
            showToastMsg('Relation Added Successfully', 'success', 2000, 'top');
            navigation.goBack();
          }
        });
      }
    }
  };

  const onDeleteClick = () => {
    const payLoad = {
      id: finalData[0].subtitle,
    };

    if (editName === '') {
      hideModal();
      return;
    }
    if (data.name === ABOUT.EDUCATION) {
      deleteEducationRequest(payLoad, (res: any) => {
        if (res) {
          hideModal();
        }
      });
    } else if (data.name === ABOUT.JOB) {
      deleteEmploymentRequest(payLoad, (res: any) => {
        if (res) {
          hideModal();
        }
      });
    } else if (data.name === ABOUT.FAMILY) {
      deleteFamilyRequest(payLoad, (res: any) => {
        if (res) {
          hideModal();
        }
      });
    }
  };

  const renderModal = () => {
    return (
      <MoreOptionsBottomModal
        visible={visible}
        onClose={hideModal}
        onBackdropPress={hideModal}
        slide={false}
        avoidKeyboard>
        <View style={{paddingHorizontal: 30}}>
          {finalData.map((detailData: any, index: number) =>
            detailData?.title !== 'ID' ? (
              <EditInputField
                key={index}
                data={detailData}
                setFinalData={setFinalData}
                finalData={finalData}
                suggestedData={
                  finalData?.[1]?.subtitle !== '' && institutes
                    ? institutes
                    : undefined
                }
                emptyInstitute={emptyInstitute}
                emptyUsers={emptyUsers}
                showFlatlist={showFlatlist}
                setShowFlatlist={setShowFlatlist}
                setInstitute={setInstitute}
                users={users}
                setSearchedUsers={setSearchedUsers}
                relationEnum={relationShipEnum}
                setRelation={setRelation}
              />
            ) : (
              <></>
            ),
          )}
          <View style={styles.buttonContainer}>
            <AppButton
              buttonStye={styles.confirmButton}
              text={TYPE_TWO_EDIT.CONFIRM}
              textColor={Colors.white}
              onPress={() => onConfirmClick()}
            />
            <AppButton
              buttonStye={styles.deleteButton}
              text={
                editName === '' ? TYPE_TWO_EDIT.CANCEL : TYPE_TWO_EDIT.DELETE
              }
              onPress={() => onDeleteClick()}
            />
          </View>
        </View>
      </MoreOptionsBottomModal>
    );
  };

  const getButtonName = () => {
    if (data.name === ABOUT.EDUCATION) {
      return TYPE_TWO_EDIT.ADD_ACEDEMIC;
    } else if (data.name === ABOUT.JOB) {
      return TYPE_TWO_EDIT.ADD_WORKSPACE;
    } else {
      return TYPE_TWO_EDIT.ADD_FAMILY;
    }
  };

  const getText = () => {
    if (data.name === ABOUT.EDUCATION) {
      return TYPE_TWO_EDIT.ACADEMIC;
    } else if (data.name === ABOUT.JOB) {
      return TYPE_TWO_EDIT.JOB;
    } else {
      return TYPE_TWO_EDIT.FAMILY;
    }
  };

  return (
    <View style={styles.container}>
      <View
        style={
          hasNotch() ? styles.statusBar : styles.statusBarWithoutNotch
        }></View>
      <CustomNavbar
        title={data.name}
        titleSize={16}
        titleContainer={data.name === ABOUT.FAMILY ? {paddingRight: 50} : {}}
        leftBtnPress={() => navigation.goBack()}
        hasMultiRight={true}
        hasRight
        rightBtnImage={
          data.name === ABOUT.FAMILY ? undefined : (
            <PublishButton
              buttonTitle={TYPE_TWO_EDIT.SAVE}
              isPublish={true}
              onPress={() => {}}
            />
          )
        }
        rightBtnPress2={() => alert('messagePress')}
      />
      <View style={styles.cardContainer}>
        <View style={[styles.innerContainer, {width: '100%'}]}>
          <AboutCardHeader data={data} />
          <Text style={styles.title}>{getText()}</Text>
          <Pressable style={styles.button} onPress={() => showAddModal()}>
            <Text style={styles.buttonText}>{getButtonName()}</Text>
          </Pressable>
        </View>
        <View style={styles.compomentContainer}>
          <AboutTitleSubtitleImage
            onEditPress={
              data.name === ABOUT.EDUCATION ||
              data.name === ABOUT.JOB ||
              data.name === ABOUT.FAMILY
                ? (item: any) => () => onEditClicked(item)
                : undefined
            }
            data={constructedData?.details}
          />
        </View>
      </View>
      {renderModal()}
    </View>
  );
};

const mapStateToProps = (state: any) => ({
  institutes: state.profile.institute,
  users: state.profile.users,
  completeProfile: state.profile.completeProfile,
  relationShipEnum: state.profile.relationshipEnum,
});

export default connect(mapStateToProps, {
  getInstituteRequest,
  emptyInstitute,
  addEducationRequest,
  updateEducationRequest,
  updateFamilyRequest,
  deleteEducationRequest,
  addEmploymentRequest,
  updateEmploymentRequest,
  deleteEmploymentRequest,
  deleteFamilyRequest,
  addFamilyRequest,
  userSearchRequest,
  emptyUsers,
  getRelationEnumRequest,
})(TypeTwoEdit);
