import {Text, View} from 'react-native';
import styles from './styles';
import {IAboutTitleSubtitle} from '../../../types';
import {
  EDIT_TEXT_INPUT,
  IMPRINT_SCORES,
} from '../../../constants/StringConstants';
import {
  LAUGHTER,
  LIFE,
  LOVE,
  PURPOSE,
  RESPECT,
  SAFETY,
} from '../../../constants/AssetSVGConstants';
import {Metrics} from '../../../theme';
import util from '../../../util';
import {DATE_FORMAT2, DATE_FORMAT6} from '../../../constants';

interface AboutTitleSubtitleProps {
  data: IAboutTitleSubtitle[];
  onEditClicked?: () => void;
}

const AboutTitleSubtitle: React.FC<AboutTitleSubtitleProps> = ({
  data,
  onEditClicked,
}) => {
  const renderSVG = (subtitle: string) => {
    switch (subtitle) {
      case IMPRINT_SCORES.LAUGHTER:
        return (
          <View style={styles.imagePadding}>
            <LAUGHTER width={26} height={26} />
          </View>
        );
      case IMPRINT_SCORES.LIFE:
        return (
          <View style={styles.imagePadding}>
            <LIFE width={26} height={26} />
          </View>
        );
      case IMPRINT_SCORES.LOVE:
        return (
          <View style={styles.imagePadding}>
            <LOVE width={26} height={26} />
          </View>
        );
      case IMPRINT_SCORES.PURPOSE:
        return (
          <View style={styles.imagePadding}>
            <PURPOSE width={26} height={26} />
          </View>
        );
      case IMPRINT_SCORES.RESPECT:
        return (
          <View style={styles.imagePadding}>
            <RESPECT width={26} height={26} />
          </View>
        );
      case IMPRINT_SCORES.SAFETY:
        return (
          <View style={styles.imagePadding}>
            <SAFETY width={26} height={26} />
          </View>
        );
      default:
        return <></>;
    }
  };

  const renderViewWithLogoAndText = (item: IAboutTitleSubtitle) => {
    const splitData = item?.subtitle
      ?.split(',')
      .filter(subtitle => subtitle.trim());
    const firstThreeItems = splitData?.slice(0, 3);
    const otherRemaingItems = splitData?.slice(3);
    return (
      <View>
        {firstThreeItems?.length > 0 && (
          <View
            style={[styles.logoTextContainer, {paddingTop: Metrics.ratio(10)}]}>
            {firstThreeItems.map((subtitle, index) => (
              <View style={styles.symbol} key={`first-${index}`}>
                {renderSVG(subtitle.trim())}
                <Text style={styles.logoText}>{subtitle.trim()}</Text>
              </View>
            ))}
          </View>
        )}
        <View style={styles.logoTextContainer}>
          {otherRemaingItems?.map((subtitle, index) => (
            <View style={styles.symbol} key={index}>
              {renderSVG(subtitle)}
              <Text style={styles.logoText}>{subtitle}</Text>
            </View>
          ))}
        </View>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      {data.map((item, index) => {
        return (
          <View style={styles.mainContainer} key={index}>
            <Text style={styles.title}>{item?.title}</Text>
            {item?.title.includes('What I Value Most') ? (
              renderViewWithLogoAndText(item)
            ) : item?.title.includes('Date') ? (
              <Text style={styles.subtitle}>
                {item?.subtitle
                  ? util.getFormattedDateTime(
                      new Date(item.subtitle),
                      onEditClicked ? DATE_FORMAT2 : DATE_FORMAT6,
                    )
                  : ''}
              </Text>
            ) : (
              <Text style={styles.subtitle}>
                {item.title === EDIT_TEXT_INPUT.GENDER
                  ? item?.subtitle.toUpperCase()
                  : item?.subtitle}
              </Text>
            )}
          </View>
        );
      })}
    </View>
  );
};

export default AboutTitleSubtitle;
