import {StyleSheet} from 'react-native';
import {Colors, Metrics, Fonts} from '../../../theme';

export default StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    flexDirection: 'column',
    marginTop: Metrics.ratio(24),
  },
  title: {
    fontFamily: Fonts.type.medium,
    fontSize: Metrics.ratio(14),
    color: Colors.text.homeTitleColor,
  },
  subtitle: {
    fontFamily: Fonts.type.regular,
    fontSize: Metrics.ratio(16),
    color: Colors.text.modalText,
  },
  imagePadding: {
    paddingLeft: Metrics.ratio(6),
  },
  symbol: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: Metrics.ratio(10),
    paddingVertical: Metrics.ratio(5),
    marginRight: Metrics.ratio(10),
    borderRadius: Metrics.ratio(16),
    backgroundColor: '#EBEBEB',
  },
  logoTextContainer: {
    flexDirection: 'row',
    width: '100%',
  },
  logoText: {
    paddingLeft: Metrics.ratio(10),
    marginRight: Metrics.ratio(10),
  },
  mainContainer: {
    alignSelf: 'flex-start',
    marginBottom: Metrics.ratio(20),
  },
});
