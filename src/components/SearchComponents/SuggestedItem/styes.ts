// @flow
import {StyleSheet} from 'react-native';
import {Colors, Metrics, AppStyles} from '../../../theme';

export default StyleSheet.create({
  itemContainer: {
    borderRadius: Metrics.ratio(8),

    marginVertical: Metrics.smallMargin,
    backgroundColor: Colors.white,

    marginHorizontal: Metrics.smallMargin,
    borderColor: Colors.itemColors.itemBorderColor,
  },
  firstItem: {
    marginLeft: 0,
  },
  subContainer: {
    alignSelf: 'center',
    justifyContent: 'center',
  },
  extraMargin: {marginLeft: Metrics.ratio(50)},
  avatarContainer: {
    borderTopLeftRadius: 8,
    borderTopRightRadius: 8,
    borderBottomLeftRadius: 0,
    borderBottomRightRadius: 0,
    marginTop: Metrics.ratio(0),
    height: Metrics.ratio(120),
    width: Metrics.ratio(210),
  },
  avatar: {
    resizeMode: 'cover',
    height: Metrics.ratio(120),
    width: Metrics.ratio(210),
  },
  reactionContainer: {
    marginLeft: Metrics.ratio(5),
    paddingHorizontal: Metrics.ratio(10),
    flexDirection: 'row',

    borderRadius: Metrics.ratio(10),
    backgroundColor: '#EBEBEB',
    justifyContent: 'center',
    alignItems: 'center',
  },
  inviteButton: {
    alignSelf: 'center',
    width: Metrics.ratio(160),
    alignItems: 'center',
    backgroundColor: Colors.gray2,
    borderRadius: Metrics.ratio(5),
    marginVertical: Metrics.ratio(10),
    paddingVertical: Metrics.ratio(10),
  },
  textMarginTop: {
    marginVertical: Metrics.ratio(5),
    marginLeft: Metrics.ratio(10),
  },
  svgContainer: {
    flexDirection: 'row',
    marginVertical: Metrics.ratio(10),
    marginLeft: Metrics.ratio(5),
  },
});
