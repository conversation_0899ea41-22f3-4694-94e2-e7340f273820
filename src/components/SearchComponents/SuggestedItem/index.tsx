// @flow
import React from 'react';
import {View} from 'react-native';
import {Text, ButtonView, Avatar, AppButton} from '../../../components';
import styles from './styes';
import {Colors, AppStyles, Metrics, Fonts} from '../../../theme';
import {PLACEHOLDER_IMAGE, svgComponents} from '../../../constants';
import _ from 'lodash';
import {ISuggestedFriendsItem} from '../../../types';
import {SUGGESTED_FRIENDS} from '../../../constants/StringConstants';
import util from '../../../util';

interface SuggestedItemProps {
  item: ISuggestedFriendsItem;
  onItemPress: () => void;
  onFollowPress?: (item: ISuggestedFriendsItem) => void;
  index?: number;
}

const SearchFriendsItem: React.FC<SuggestedItemProps> = ({
  item,
  onItemPress,
  onFollowPress,
  index,
}) => {
  return (
    <View style={[styles.itemContainer, index === 0 && styles.firstItem]}>
      <ButtonView onPress={onItemPress}>
        <Avatar
          image={
            !_.isEmpty(item.avatarUrl) ? item.avatarUrl : PLACEHOLDER_IMAGE
          }
          style={styles.avatarContainer}
          imageStyle={styles.avatar}
        />
        <Text
          maxWidth={Metrics.screenWidth * 0.4}
          height={Metrics.screenHeight * 0.06}
          numberOfLines={2}
          type="medium"
          size="normal"
          textAlign="left"
          color={Colors.itemColors.titleColor}
          style={styles.textMarginTop}>
          {!_.isEmpty(item.displayName) ? item.displayName : 'No Display Name'}
        </Text>
      </ButtonView>

      <View style={styles.svgContainer}>
        {item.scores !== null &&
          Object?.entries(item?.scores)
            .sort(([, a], [, b]) => b - a)
            .slice(0, 2)
            .map(([key, value]) => {
              const SvgComponent = svgComponents[key.toLowerCase()];
              if (SvgComponent) {
                return (
                  <View key={key} style={styles.reactionContainer}>
                    <SvgComponent height={20} width={20} />
                    <Text size={'xxxSmall'} color={Colors.black}>
                      {util.capitalizeFirstLetter(key)}
                    </Text>
                  </View>
                );
              }
              return <></>;
            })}
      </View>
      <ButtonView
        style={[
          styles.inviteButton,
          item.followRequestSent
            ? {backgroundColor: Colors.gray}
            : {backgroundColor: Colors.black},
        ]}
        onPress={onFollowPress}
        disabled={item.followRequestSent}>
        <Text
          size={'xSmall'}
          color={item.followRequestSent ? Colors.BlackButton : Colors.white}>
          {item.followRequestSent
            ? SUGGESTED_FRIENDS.REQUESTED
            : SUGGESTED_FRIENDS.ADD_FRIENDS}
        </Text>
      </ButtonView>
    </View>
  );
};

export default SearchFriendsItem;
