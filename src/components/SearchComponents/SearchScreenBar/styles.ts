// @flow
import {StyleSheet} from 'react-native';
import {Colors, Metrics, AppStyles, Fonts} from '../../../theme';
import Util from '../../../util';

export default StyleSheet.create({
  mainContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: Metrics.baseMargin,
    marginTop: Metrics.doubleBaseMargin,
    marginBottom: Metrics.smallMargin,
  },
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.gray,
    marginHorizontal: Metrics.baseMargin,
    paddingHorizontal: Metrics.baseMargin,
    borderWidth: Metrics.ratio(1),
    borderColor: Colors.borderColor,
    borderRadius: Metrics.ratio(50),
  },
  searchWrapper: {
    alignSelf: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },

  textInput: {
    marginLeft: Metrics.ratio(10),
    fontSize: Fonts.size.xSmall,
    fontFamily: Fonts.type.base,
    ...AppStyles.flex,
    ...AppStyles.pRight10,
    height: Util.isPlatformAndroid() ? Metrics.ratio(45) : Metrics.ratio(45),

    ...AppStyles.pTop0,
    ...AppStyles.pBottom0,
    color: Colors.black,
  },
  conditionalContainer: {borderColor: Colors.black},
});
