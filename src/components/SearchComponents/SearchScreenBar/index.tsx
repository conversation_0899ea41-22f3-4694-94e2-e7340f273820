import React from 'react';
import {
  View,
  TextInput,
  ActivityIndicator,
  ViewStyle,
  TextInputProps,
} from 'react-native';
import {Colors, AppStyles, Metrics} from '../../../theme';
import styles from './styles';
import {
  BACK_ICON,
  CIRCLE_BLACK,
  SEARCH_BAR_ICON,
} from '../../../constants/AssetSVGConstants';
import ButtonView from '../../ButtonView';

interface SearchBarProps extends TextInputProps {
  containerStyle?: ViewStyle;
  placeholder?: string;
  onSearchText?: (text: string) => void;
  isSearching?: boolean;
  autoFocus?: boolean;
  value?: string;
  backBtnPress: () => void;
  clearSearch: () => void;
}

const SearchBarScreen: React.FC<SearchBarProps> = ({
  placeholder = 'Search',
  isSearching = false,
  onSearchText = () => {},
  autoFocus = false,
  containerStyle,
  value = '',
  backBtnPress,
  clearSearch,
  ...rest
}) => {
  return (
    <View style={styles.mainContainer}>
      <ButtonView onPress={backBtnPress}>
        <BACK_ICON />
      </ButtonView>
      <View
        style={[
          styles.container,
          containerStyle,
          !!value.length && styles.conditionalContainer,
        ]}>
        <View style={styles.searchWrapper}>
          <SEARCH_BAR_ICON />
          <TextInput
            autoFocus={autoFocus}
            placeholder={placeholder}
            placeholderTextColor={Colors.text.placeHolderTextColor}
            style={styles.textInput}
            returnKeyType="search"
            value={value}
            onChangeText={onSearchText}
            {...rest}
          />
          {/* {isSearching && (
            <ActivityIndicator
              size="small"
              color={Colors.white}
              style={AppStyles.mRight10}
            />
          )} */}
          {value.length > 0 && (
            <ButtonView
              style={AppStyles.padding10}
              onPress={() => {
                clearSearch();
              }}>
              <CIRCLE_BLACK />
            </ButtonView>
          )}
        </View>
      </View>
    </View>
  );
};

export default SearchBarScreen;
