import React, {useState} from 'react';
import {Modal, TextInput, Pressable, View, Text} from 'react-native';
import {Colors} from '../../theme';
import styles from './styles';
import {CROSS_GRAY} from '../../constants/AssetSVGConstants';
import {VOUCHER} from '../../constants/StringConstants';

interface InviteCodeProps {
  visible: boolean;
  onDone: (text: string) => void;
  onClose: () => void;
  onRequestClose?: () => void;
  placeHolder?: string;
  errorText: boolean;
  onTyping?: () => void;
  handleNoCode?: () => void;
}

const InviteCodeAlert: React.FC<InviteCodeProps> = ({
  visible,
  onDone,
  onClose,
  errorText,
  onTyping,
  handleNoCode,
}) => {
  const [text, setText] = useState('');

  const handleDone = () => {
    onDone(text);
  };

  const clearText = () => {
    setText('');
    onClose();
  };

  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={visible}
      onRequestClose={clearText}>
      <View
        style={[
          styles.modalContainer,
          {backgroundColor: VOUCHER.MODAL_BACKGROUND_COLOR},
        ]}>
        <View style={styles.popup}>
          <View style={styles.popupInnerContainer}>
            <Text style={styles.placeHolder}>{VOUCHER.MODAL_TITLE}</Text>

            <Pressable
              style={[
                styles.closeIconButton,
                {backgroundColor: VOUCHER.CLOSE_BUTTON_BACKGROUND},
              ]}
              onPress={clearText}>
              <CROSS_GRAY />
            </Pressable>
            <Text style={styles.title}>{VOUCHER.INSTRUCTION_TEXT}</Text>
            <TextInput
              multiline={true}
              numberOfLines={4}
              onChangeText={value => {
                setText(value);
                if (errorText && onTyping) {
                  onTyping();
                }
              }}
              value={text}
              placeholder={VOUCHER.EMPTY_PLACEHOLDER}
              placeholderTextColor={Colors.textGray}
              style={styles.input}
            />
            {errorText && (
              <Text style={styles.errorText}>{VOUCHER.ERROR_MESSAGE}</Text>
            )}
            <View style={styles.buttonContainer}>
              <Pressable
                onPress={handleDone}
                style={styles.doneButton}
                disabled={!text}>
                <Text
                  style={[
                    styles.verifyButtonText,
                    {color: VOUCHER.BUTTON_TEXT_COLOR_WHITE},
                  ]}>
                  {VOUCHER.VERIFY_BUTTON}
                </Text>
              </Pressable>
              <View style={styles.buttonSpacer} />
              <Pressable onPress={handleNoCode} style={styles.closeButton}>
                <Text style={styles.noCodeButtonText}>
                  {VOUCHER.NO_INVITE_CODE_BUTTON}
                </Text>
              </Pressable>
            </View>
          </View>
        </View>
      </View>
    </Modal>
  );
};

export default InviteCodeAlert;
