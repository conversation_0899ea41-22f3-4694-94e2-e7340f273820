import React from 'react';
import {
  View,
  Text,
  StyleProp,
  ViewStyle,
  TextStyle,
  Dimensions,
} from 'react-native';
import Toast, {
  BaseToast,
  ErrorToast,
  ToastConfig,
} from 'react-native-toast-message';
import Colors from '../../theme/Colors';
import {Fonts, Metrics} from '../../theme';
import {EXCLAMATION, TOAST_SUCCESS} from '../../constants/AssetSVGConstants';
import styles from './styles';

// Define the custom toast styles
const successToastStyle: StyleProp<ViewStyle> = {
  backgroundColor: Colors.success,
  borderColor: Colors.successBorder,
  borderWidth: 1,
  flexDirection: 'row',
  marginHorizontal: Metrics.ratio(20),
  alignItems: 'center',
  flex: 1,
};

const errorToastStyle: StyleProp<ViewStyle> = {
  backgroundColor: 'red',
  borderLeftColor: 'red',
};

const errorToastTextStyle: StyleProp<TextStyle> = {
  color: Colors.black,
};

const successToastTextStyle: StyleProp<TextStyle> = {
  color: Colors.black,
  textAlign: 'center',
  fontFamily: Fonts.type.semi_bold,
  fontSize: Metrics.ratio(12),
  paddingRight: Metrics.ratio(14),
  paddingBottom: Metrics.ratio(14),
  paddingTop: Metrics.ratio(14),
};

// Define the custom toast component
const TomatoToast = ({text1, props}: {text1: string; props: any}) => (
  <View style={styles.container}>
    <EXCLAMATION />
    <Text style={styles.text}>{text1}</Text>
  </View>
);

const SuccessToast = ({
  text1,
  text1NumberOfLines,
  text1Style,
  style,
  props,
}: {
  text1: string;
  props: any;
  text1NumberOfLines: number;
  style: any;
  text1Style: any;
}) => (
  <View style={style}>
    <View style={styles.successStyle}>
      <TOAST_SUCCESS />
    </View>
    <Text style={text1Style} numberOfLines={text1NumberOfLines}>
      {text1}
    </Text>
  </View>
);

const toastConfig: ToastConfig = {
  success: props => (
    <SuccessToast
      {...props}
      text1={props.text1 || ''} // Provide a default value for text1
      text1NumberOfLines={0}
      style={successToastStyle}
      text1Style={successToastTextStyle}
    />
  ),
  error: props => (
    <ErrorToast
      {...props}
      text1NumberOfLines={0}
      style={errorToastStyle}
      text1Style={errorToastTextStyle}
    />
  ),

  tomatoToast: ({text1 = '', props}: {text1?: string; props: any}) => (
    <TomatoToast text1={text1} props={props} />
  ),
};

export default toastConfig;
