// @flow
import {Dimensions, StyleSheet} from 'react-native';
import {Metrics, Colors, Fonts} from '../../theme';

export default StyleSheet.create({
  container: {
    flexDirection: 'row',
    flex: 1,
    backgroundColor: Colors.background.errorToast,
    width: Dimensions.get('window').width - 60,
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'center',
    minHeight: Metrics.ratio(40),
    borderRadius: Metrics.ratio(4),
  },
  text: {
    marginLeft: Metrics.ratio(10),
    paddingTop: Metrics.ratio(10),
    paddingBottom: Metrics.ratio(10),
    maxWidth: Metrics.screenWidth * 0.7,
    fontFamily: Fonts.type.regular,
    fontSize: Fonts.size.xSmall,
    color: Colors.black1,
    textAlign: 'left',
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'center',
  },
  successStyle: {
    paddingLeft: Metrics.ratio(14),
    paddingRight: Metrics.ratio(12),
  },
});
