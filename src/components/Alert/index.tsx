import {Alert, AlertButton} from 'react-native';
import Toast from 'react-native-toast-message';

export const showAlertMsg = (
  alertTitle: string,
  alertDesc?: string,
  buttons?: AlertButton[],
  cancelable?: boolean,
) => {
  cancelable = false;
  if (!buttons) {
    buttons = [
      {
        text: 'OK',
      },
    ];
  }
  Alert.alert(alertTitle, alertDesc, buttons, {cancelable});
};
type ToastPosition = 'top' | 'bottom' | 'center';

export const showToastMsg = (
  text1: string,
  type: string = 'tomatoToast',
  visibilityTime?: number,
  position?: string,
  bottomOffset: number = 200,
) => {
  Toast.show({
    type,
    text1,
    position: position ? position : 'bottom',
    autoHide: true,
    visibilityTime: visibilityTime || 1500,
    bottomOffset: bottomOffset,
    topOffset: 100,
  });
};
