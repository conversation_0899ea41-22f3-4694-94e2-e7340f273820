import {createBottomTabNavigator} from '@react-navigation/bottom-tabs';
import React from 'react';
import Routes from '../../constants/RouteConstants';
import TabBar from '../TabBar';
import Analytics from '../../containers/Analytic';
import Notification from '../../containers/Notification';
import Friend from '../../containers/Friend';
import HomeStack from '../../navigator/HomeStack';
import ProfileStack from '../../navigator/ProfileStack';
import NotificationStack from '../../navigator/NotificationStack';
import {CreateImprint} from '../../containers';
import CreateImprintStack from '../../navigator/createImprintStack';

const Tab = createBottomTabNavigator();

export function HomeTabs() {
  return (
    <Tab.Navigator
      initialRouteName={Routes.HOME_STACK}
      screenOptions={({route}) => ({
        tabBarHideOnKeyboard: true,
        tabBarShowLabel: false,
        headerShown: false,
      })}
      tabBar={props => <TabBar {...props} headerShown={false} />}>
      <Tab.Screen
        name={Routes.HOME_STACK}
        children={HomeStack}
        options={{
          title: Routes.HOME,
          // unmountOnBlur: true,
        }}
        listeners={({}) => ({
          tabPress: e => {},
        })}
      />
      <Tab.Screen
        name={Routes.ANALYTICS}
        component={Analytics}
        options={{
          title: Routes.ANALYTICS,
        }}
      />
      <Tab.Screen
        name={Routes.CREATE_IMPRINT_STACK}
        component={CreateImprintStack}
        options={{
          title: Routes.CREATE_IMPRINT,
        }}
        listeners={({}) => ({
          tabChange: e => {},
        })}
      />

      <Tab.Screen
        name={Routes.NOTIFICATION_STACK}
        children={NotificationStack}
        options={({route}) => ({
          title: Routes.NOTIFICATION,
        })}
      />
      <Tab.Screen
        name={Routes.PROFILE_STACK}
        children={ProfileStack}
        options={({route}) => ({
          title: Routes.PROFILE,
        })}
      />
    </Tab.Navigator>
  );
}
