import React, {useEffect} from 'react';
import {View, Pressable} from 'react-native';
import {styles} from './styles';
import {
  ANALYTICS,
  ANALYTICS_SELECTED,
  HOME,
  HOME_SELECTED,
  NOTIFICATION,
  NOTIFICATION_SELECTED,
  TAB_PROFILE,
  TAB_PROFILE_SELECTED,
} from '../../constants/AssetSVGConstants';
import * as RootNavigation from '../../services/RootNavigation';
import Routes from '../../constants/RouteConstants';
import {Text} from '../';
import {Colors} from '../../theme';
import {connect} from 'react-redux';
import {showToastMsg} from '../Alert';
import {TOAST_MESSAGES, TOAST_TYPE} from '../../constants/StringConstants';
import {
  allowedRoutesForFreeUsers,
  TOAST_VISIBILITY_TIMEOUT,
} from '../../constants';
import {isUserSubscribed} from '../../util';
import {userInfoRequest} from '../../actions/UserActions';
import {UserInfo} from '../../types';

const TabBar = ({
  state,
  descriptors,
  navigation,
  user,
  userInfoRequest,
}: any) => {
  const hideTabOnScroll = RootNavigation.getCurrentRoute()?.params?.showTab;
  const currentRoute = RootNavigation.getCurrentRoute()?.name;

  const [showTab, setShowTab] = React.useState(hideTabOnScroll);

  useEffect(() => {
    setTimeout(() => {
      setShowTab(showTabBar());
    }, 100);
  }, [RootNavigation.getCurrentRoute().name]);

  useEffect(() => {
    setShowTab(hideTabOnScroll);
  }, [hideTabOnScroll]);

  const NavigationIcon = ({route, isFocused}: any) => {
    switch (route) {
      case Routes.HOME:
        return isFocused ? <HOME_SELECTED /> : <HOME />;
      case Routes.ANALYTICS:
        return isFocused ? <ANALYTICS_SELECTED /> : <ANALYTICS />;
      case Routes.NOTIFICATION:
        return isFocused ? <NOTIFICATION_SELECTED /> : <NOTIFICATION />;
      case Routes.CREATE_IMPRINT:
        return isFocused ? (
          <View style={styles.createImprintSelected}>
            <Text size={'xxxLarge'} color={Colors.white} type="semi_bold">
              +
            </Text>
          </View>
        ) : (
          <View style={styles.createImprintUnSelected}>
            <Text size={'xxxLarge'} color={Colors.black} type="semi_bold">
              +
            </Text>
          </View>
        );
      case Routes.PROFILE:
        return isFocused ? <TAB_PROFILE_SELECTED /> : <TAB_PROFILE />;
      default:
        return null;
    }
  };

  const showTabBar = () => {
    switch (RootNavigation.getCurrentRoute().name) {
      case Routes.HOME:
      case Routes.ANALYTICS:
      case Routes.NOTIFICATION:
      // case Routes.CREATE_IMPRINT:
      case Routes.PROFILE:
      case Routes.HOME_TABS:
      case Routes.PROFILE_STACK:
        return true;
      default:
        return false;
    }
  };

  return (
    <View
      style={
        showTab === true || showTab === undefined
          ? styles.mainContainer
          : styles.heightZero
      }>
      {state.routes.map((route: any, index: number) => {
        const {options} = descriptors[route.key];
        const label =
          options.tabBarLabel !== undefined
            ? options.tabBarLabel
            : options.title !== undefined
            ? options.title
            : route.name;

        const isFocused = state.index === index;

        const onPress = () => {
          // ✅ Smart userInfo refresh - avoid overwriting recent local subscription updates
          const shouldRefreshUserInfo = () => {
            // Don't refresh if we have a recent local subscription update
            if (user.localSubscriptionUpdate) {
              const timeSinceUpdate =
                Date.now() - user.localSubscriptionUpdate.timestamp;
              // Skip refresh if local update is less than 2 minutes old
              if (timeSinceUpdate < 2 * 60 * 1000) {
                return false;
              }
            }
            return true;
          };

          if (shouldRefreshUserInfo()) {
            userInfoRequest(() => {});
          }

          if (
            !isUserSubscribed(user) &&
            !allowedRoutesForFreeUsers.includes(route.name)
          ) {
            navigation.navigate(Routes.SUBSCRIPTION);
            showToastMsg(
              TOAST_MESSAGES.SUBSCRIPTION_ERROR,
              TOAST_TYPE.DEFAULT,
              TOAST_VISIBILITY_TIMEOUT,
              'top',
            );
            return;
          }

          const event = navigation.emit({
            type: 'tabPress',
            target: route.key,
          });

          if (!isFocused && !event.defaultPrevented) {
            navigation.navigate(route.name);
            return;
          }
        };

        return (
          <View
            key={index}
            style={[
              styles.mainItemContainer,
              {borderRightWidth: label == 'notes' ? 3 : 0},
            ]}>
            <Pressable onPress={onPress}>
              {NavigationIcon({route: label, isFocused: isFocused})}
            </Pressable>
          </View>
        );
      })}
    </View>
  );
};
const mapStateToProps = (state: any) => ({
  user: state.user,
});
export default connect(mapStateToProps, {userInfoRequest})(TabBar);
