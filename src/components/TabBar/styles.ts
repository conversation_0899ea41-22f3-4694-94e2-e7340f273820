import {Dimensions, StyleSheet} from 'react-native';
import {width} from '../../theme/responsive';
import {Colors, Metrics} from '../../theme';

export const styles = StyleSheet.create({
  mainContainer: {
    flexDirection: 'row',
    position: 'absolute',
    bottom: Dimensions.get('window').height * 0.05,
    backgroundColor: 'rgba(16, 20, 24, 0.2)',
    borderRadius: 100,
    marginHorizontal: width * 0.05,
    paddingHorizontal: Metrics.ratio(15),
  },
  mainItemContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: Metrics.ratio(15),
    borderRadius: 1,
    borderColor: '#333B42',
  },
  buttonStyle: {
    justifyContent: 'center',
    alignItems: 'center',
    alignContent: 'center',
  },
  heightZero: {
    height: 0,
  },
  createImprintSelected: {
    marginHorizontal: 10,
    width: Metrics.ratio(53),
    height: Metrics.ratio(53),
    borderRadius: Metrics.ratio(100),
    backgroundColor: Colors.black,
    justifyContent: 'center',
    alignItems: 'center',
    shadowOffset: {
      width: 0,
      height: 12.857142448425293,
    },
    shadowOpacity: 0.33,
    shadowRadius: 25.714284896850586,
    elevation: 8,
  },
  createImprintUnSelected: {
    width: Metrics.ratio(53),
    height: Metrics.ratio(53),
    borderRadius: Metrics.ratio(100),
    backgroundColor: Colors.white,
    justifyContent: 'center',
    alignItems: 'center',
    shadowOffset: {
      width: 0,
      height: 12.857142448425293,
    },
    shadowOpacity: 0.33,
    shadowRadius: 25.714284896850586,
    elevation: 8,
  },
});
