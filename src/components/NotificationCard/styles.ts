import {StyleSheet} from 'react-native';
import {Colors, Fonts, Metrics} from '../../theme';

export const styles = StyleSheet.create({
  container: {
    marginHorizontal: Metrics.ratio(16),
    marginBottom: Metrics.ratio(20),
    paddingVertical: Metrics.ratio(10),
    borderRadius: Metrics.ratio(21),
  },
  imageContainer: {
    height: Metrics.ratio(68),
    width: Metrics.ratio(68),
    margin: Metrics.ratio(16),
    borderRadius: Metrics.ratio(100),
  },
  textContainer: {
    flex: 1,
    justifyContent: 'center',
    marginRight: Metrics.ratio(5),
  },
  title: {
    width: Metrics.screenWidth - 150,
    fontFamily: Fonts.type.bold,
    fontSize: Metrics.ratio(16),
  },
  description: {
    fontFamily: Fonts.type.regular,
    fontSize: Fonts.size.xSmall,
  },
  time: {
    fontFamily: Fonts.type.medium,
    fontSize: Metrics.ratio(12),
  },
  emojiContainer: {
    height: Metrics.ratio(38),
    width: Metrics.ratio(38),
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: Metrics.ratio(16),
  },
  confirmButton: {
    paddingHorizontal: Metrics.ratio(24),
    height: Metrics.ratio(40),
    backgroundColor: Colors.BlackButton,
    borderColor: Colors.black,
    borderWidth: Metrics.ratio(1),
    borderRadius: Metrics.ratio(6),
    marginHorizontal: Metrics.ratio(0),
  },
  deleteButton: {
    height: Metrics.ratio(40),
    marginLeft: Metrics.baseMargin,
    backgroundColor: Colors.gray2,
    paddingHorizontal: Metrics.ratio(24),
  },
  buttonContainer: {
    justifyContent: 'center',
    marginBottom: Metrics.ratio(10),
    flexDirection: 'row',
    marginTop: Metrics.ratio(10),
  },
  cardContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  userImageContainer: {
    width: Metrics.ratio(68),
    height: Metrics.ratio(68),
    borderRadius: Metrics.ratio(100),
    resizeMode: 'cover',
  },
  notificationRead: {
    backgroundColor: Colors.white,
  },
  notificationUnread: {
    backgroundColor: Colors.ChatColors.unreadItem,
  },
  boldText: {fontWeight: 'bold'},
  buttonContainerMore: {
    alignSelf: 'flex-start',
  },
});
