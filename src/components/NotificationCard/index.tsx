import {View, Text, Image, Pressable} from 'react-native';
import {styles} from './styles';
import {
  ImprintNotification,
  NotificationTypeEnum,
  ReactionType,
} from '../../types';
import {
  ANGRY,
  BOOKMARK_WHITE,
  FAILED_ICON,
  FLAG_RED,
  LIFE,
  OPEN_REQUEST_ICON,
  PURPOSE,
  REQUEST_DENIED_ICON,
  REQUESTED_ICON,
  RESPECT,
  SAD_REACTION,
  SAFETY,
  SHOUT_OUT,
  VERIFIED_ICON,
  SCARED_REACTION,
  LOVE_REACTION,
} from '../../constants/AssetSVGConstants';
import {
  COMMON,
  IMPRINT_TIMELINE,
  NOTIFICATION_TYPE,
} from '../../constants/StringConstants';
import {Colors} from '../../theme';
import AppButton from '../AppButton';
import util from '../../util';
import {BADGE_TYPES, SUPPORT_CENTER_LINK} from '../../constants';
import {navigationRef} from '../../services/RootNavigation';
import Routes from '../../constants/RouteConstants';

interface NotificationCardProps {
  onItemClicked: (item: ImprintNotification) => void;
  notification: ImprintNotification;
  onAccept?: (item: ImprintNotification) => void;
  onDecline?: (item: ImprintNotification) => void;
  onClickMore?: (item: ImprintNotification) => void;
}

const NotificationCard: React.FC<NotificationCardProps> = ({
  notification,
  onItemClicked,
  onAccept,
  onDecline,
  onClickMore,
}) => {
  const getEmoji = (reaction: string | string[]) => {
    switch (reaction) {
      case ReactionType.ANGRY:
        return <ANGRY width={38} height={38} />;
      case ReactionType.SAFETY:
        return <SAFETY width={38} height={38} />;
      case ReactionType.PURPOSE:
        return <PURPOSE width={38} height={38} />;
      case ReactionType.RESPECT:
        return <RESPECT width={38} height={38} />;
      case ReactionType.LOVE:
        return <LOVE_REACTION width={38} height={38} />;
      case ReactionType.SAD:
        return <SAD_REACTION width={38} height={38} />;
      case ReactionType.SCARED:
        return <SCARED_REACTION width={38} height={38} />;
      case ReactionType.SHOUT_OUT:
        return <SHOUT_OUT width={38} height={38} />;
      case IMPRINT_TIMELINE.BOOKMARK:
        return <BOOKMARK_WHITE width={38} height={38} />;
      case IMPRINT_TIMELINE.FLAG:
        return <FLAG_RED width={38} height={38} />;
      case NotificationTypeEnum.SPEAK_UP:
        return <SHOUT_OUT width={38} height={38} />;
      case NotificationTypeEnum.LIFE:
        return <LIFE width={38} height={38} />;
      default:
        return <></>;
    }
  };

  const currentType = notification.subNotifications[0].typeTemplate
    .type as NotificationTypeEnum;

  const getReactionName = () => {
    if (notification.subNotifications.length > 1) {
      return `${notification.subNotifications[0].user.userName} and ${
        notification.subNotifications.length - 1
      } Others`;
    } else {
      return notification.subNotifications[0].user.userName;
    }
  };

  const getVerification = (type: string) => {
    switch (notification.subNotifications[0].typeTemplate.type) {
      case NotificationTypeEnum.ARTICLE_VERIFICATION_SUCCESS:
        return (
          <View>
            <VERIFIED_ICON />
          </View>
        );
      case NotificationTypeEnum.ARTICLE_VERIFICATION_FAILURE:
        return (
          <View>
            <FAILED_ICON />
          </View>
        );
      case NotificationTypeEnum.FOLLOW_REQUEST:
        return (
          <View>
            <REQUESTED_ICON />
          </View>
        );
      case NotificationTypeEnum.ARTICLE_VERIFICATION_REJECTED:
        return (
          <View>
            <REQUEST_DENIED_ICON />
          </View>
        );
      default:
        return (
          <View>
            <OPEN_REQUEST_ICON />
          </View>
        );
    }
  };

  return (
    <Pressable
      onPress={() => onItemClicked(notification)}
      style={[
        styles.container,
        notification.isRead
          ? styles.notificationRead
          : styles.notificationUnread,
      ]}>
      <View style={styles.cardContainer}>
        <View style={styles.imageContainer}>
          <Image
            source={{uri: notification.subNotifications[0].user.avatarUrl}}
            style={styles.userImageContainer}
          />
        </View>
        <View style={styles.textContainer}>
          <Text style={styles.title} numberOfLines={2}>
            {getReactionName()}
          </Text>
          <Text style={styles.description}>
            {BADGE_TYPES.includes(
              notification.subNotifications[0].typeTemplate.type,
            ) ? (
              util.formatMessage(
                notification.subNotifications[0].typeTemplate.body,
              )
            ) : notification.subNotifications[0].typeTemplate.type ===
              NotificationTypeEnum.INVESTIGATION_REPORTED_USER_IS_A_RISK ? (
              <>
                {
                  notification.subNotifications[0].typeTemplate.body.split(
                    NOTIFICATION_TYPE.USER_NAME_SPLIT,
                  )[0]
                }
                <Text style={styles.boldText}>
                  {notification.metaData?.userName || ''}
                </Text>
                {
                  notification.subNotifications[0].typeTemplate.body.split(
                    NOTIFICATION_TYPE.USER_NAME_SPLIT,
                  )[1]
                }
              </>
            ) : notification.subNotifications[0].typeTemplate.type ===
              NotificationTypeEnum.NOK_REJECTED ? (
              (() => {
                const parts =
                  notification.subNotifications[0].typeTemplate.body.split(
                    NOTIFICATION_TYPE.HERE_SPLIT,
                  );
                return (
                  <>
                    {parts[0]}
                    <Text
                      style={styles.boldText}
                      onPress={() =>
                        navigationRef.current.navigate(Routes.REPORT_SCREEN, {
                          url: SUPPORT_CENTER_LINK,
                          title: notification.metaData?.title,
                        })
                      }>
                      {NOTIFICATION_TYPE.HERE}
                    </Text>
                    {parts[1]}
                  </>
                );
              })()
            ) : (
              notification.subNotifications[0].typeTemplate.body
            )}
          </Text>
          <Text style={styles.time}>
            {util.formatTime(notification.subNotifications[0].createdAt)}
          </Text>
          {[
            NotificationTypeEnum.IMPRINT_REJECTED_FOR_ALL_USERS,
            NotificationTypeEnum.USER_REPORTED_IMPRINT_REJECTED_FOR_UNDER_18_USERS,
            NotificationTypeEnum.INVESTIGATION_UPHELD_DECISION,
            NotificationTypeEnum.IMPRINT_REJECTED_FOR_UNDER_18_USERS,
            NotificationTypeEnum.USER_REPORTED_IMPRINT_DELETED,
            NotificationTypeEnum.MESSAGING_RESTRICTED_UNDER18,
            NotificationTypeEnum.MESSAGING_RESTRICTED_ALL,
            NotificationTypeEnum.USER_ACCOUNT_SUSPENDED_YEARLY,
            NotificationTypeEnum.SUBSCRIPTION_CANCELLED_USER_BANNED,
            NotificationTypeEnum.UPDATED_VALUE_BADGE_ASSIGNED,
            NotificationTypeEnum.IMPRINT_REJECTED_FOR_ALL_UNDER_18POLICY,
            NotificationTypeEnum.USER_ACCOUNT_SUSPENDED,
          ].includes(
            notification.subNotifications[0].typeTemplate
              .type as NotificationTypeEnum,
          ) ? (
            <View style={[styles.buttonContainer, styles.buttonContainerMore]}>
              <AppButton
                buttonStye={styles.confirmButton}
                text={NOTIFICATION_TYPE.View_More}
                onPress={() => onClickMore && onClickMore(notification)}
                textColor={Colors.white}
                type={'semi_bold'}
              />
            </View>
          ) : null}
        </View>
        <View style={styles.emojiContainer}>
          {getEmoji(
            BADGE_TYPES.includes(
              notification.subNotifications[0].typeTemplate.type,
            )
              ? util.extractBracketText(
                  notification.subNotifications[0].typeTemplate.body,
                )
              : notification.subNotifications[0].typeTemplate.type,
          )}
          {[
            NotificationTypeEnum.ARTICLE_VERIFICATION_SUCCESS,
            NotificationTypeEnum.ARTICLE_VERIFICATION_FAILURE,
            NotificationTypeEnum.ARTICLE_VERIFICATION_REJECTED,
            NotificationTypeEnum.ARTICLE_VERIFICATION_ACCEPTED,
          ].includes(
            notification.subNotifications[0].typeTemplate
              .type as NotificationTypeEnum,
          )
            ? getVerification(
                notification.subNotifications[0].typeTemplate.type,
              )
            : null}
        </View>
      </View>

      {[
        NotificationTypeEnum.FOLLOW_REQUEST,
        NotificationTypeEnum.ARTICLE_VERIFICATION_REQUESTED,
        NotificationTypeEnum.IMPRINT_MEDIA_MENTION,
        NotificationTypeEnum.IMPRINT_MENTION,
        NotificationTypeEnum.NOK_REJECTED,
      ].includes(
        notification.subNotifications[0].typeTemplate
          .type as NotificationTypeEnum,
      ) ? (
        <View style={styles.buttonContainer}>
          <AppButton
            buttonStye={styles.confirmButton}
            text={
              currentType === NotificationTypeEnum.NOK_REJECTED
                ? NOTIFICATION_TYPE.ENTER_NOW
                : currentType === NotificationTypeEnum.NOK_REMINDER
                ? NOTIFICATION_TYPE.SEND_REMINDER
                : COMMON.ACCEPT
            }
            onPress={() => onAccept && onAccept(notification)}
            textColor={Colors.white}
            type={'semi_bold'}
            size="xxSmall"
          />
          <AppButton
            buttonStye={styles.deleteButton}
            text={
              currentType === NotificationTypeEnum.NOK_REJECTED
                ? NOTIFICATION_TYPE.REMIND_LATER
                : COMMON.DECLINE
            }
            textColor={Colors.black}
            onPress={() => onDecline && onDecline(notification)}
            type={'semi_bold'}
            size="xxSmall"
          />
        </View>
      ) : null}
    </Pressable>
  );
};
export default NotificationCard;
