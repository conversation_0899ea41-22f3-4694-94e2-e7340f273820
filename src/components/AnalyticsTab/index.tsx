import {View} from 'react-native';
import {styles} from './styles';
import {Text} from '../';
import {ButtonView} from '..';
import {Colors} from '../../theme';
import {ANALYTICS, HOME} from '../../constants/StringConstants';

interface AnalyticsTabProps {
  onPersonalPress: () => void;
  onGlobalPress: () => void;
  onOverallPress?: () => void;
  isPersonalSelect: boolean;
  isGlobalSelect: boolean;
  isOverallSelect?: boolean;
}

const AnalyticTab: React.FC<AnalyticsTabProps> = ({
  onPersonalPress,
  onGlobalPress,
  onOverallPress,
  isPersonalSelect,
  isGlobalSelect,
  isOverallSelect,
}) => {
  const renderBar = () => {
    return <View style={styles.barStyle} />;
  };

  return (
    <View style={styles.container}>
      {onOverallPress && (
        <ButtonView onPress={onOverallPress} style={styles.tabChild}>
          <Text
            color={
              isOverallSelect
                ? Colors.tabsTextColor.activeTabColor
                : Colors.tabsTextColor.inActiveTabColor
            }
            size={'normal'}
            type={isOverallSelect ? 'semi_bold' : 'regular'}>
            {ANALYTICS.OVERALL}
          </Text>
          {isOverallSelect && renderBar()}
        </ButtonView>
      )}

      <ButtonView onPress={onGlobalPress} style={styles.tabChild}>
        <Text
          color={
            isGlobalSelect
              ? Colors.tabsTextColor.activeTabColor
              : Colors.tabsTextColor.inActiveTabColor
          }
          size={'normal'}
          type={isGlobalSelect ? 'semi_bold' : 'regular'}>
          {HOME.GLOBAL}
        </Text>
        {isGlobalSelect && renderBar()}
      </ButtonView>
      <ButtonView onPress={onPersonalPress} style={styles.tabChild}>
        <Text
          color={
            isPersonalSelect
              ? Colors.tabsTextColor.activeTabColor
              : Colors.tabsTextColor.inActiveTabColor
          }
          size={'normal'}
          type={isPersonalSelect ? 'semi_bold' : 'regular'}>
          {HOME.PERSONAL}
        </Text>
        {isPersonalSelect && renderBar()}
      </ButtonView>
    </View>
  );
};

export default AnalyticTab;
