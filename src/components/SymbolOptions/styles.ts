import {StyleSheet} from 'react-native';
import {Colors, Fonts, Metrics} from '../../theme';

export default StyleSheet.create({
  optionMainContainer: {
    backgroundColor: Colors.white,
  },
  optionInnerContainer: {
    backgroundColor: Colors.white,
    flexDirection: 'row',
  },
  optionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: Metrics.ratio(10),
    borderRadius: Metrics.ratio(4),
  },
  dot: {
    width: Metrics.ratio(10),
    height: Metrics.ratio(10),
    borderRadius: Metrics.ratio(100),
    marginHorizontal: Metrics.ratio(10),
  },
  optionText: {
    fontFamily: Fonts.type.regular,
    fontSize: Metrics.ratio(14),
    color: Colors.text.homeTitleColor,
    paddingVertical: Metrics.ratio(8),
    marginRight: Metrics.ratio(10),
  },
  scrollContainer: {
    marginTop: Metrics.ratio(24),
    marginBottom: Metrics.screenHeight * 0.42,
  },
});
