import {Pressable, Text, View} from 'react-native';
import styles from './styles';
import {Colors} from '../../theme';
import {getColor} from '../../helpers/AnalyticsHelper';
import {options} from '../../helpers/ProfileHelper';

interface ISymbolOptionsProps {
  optionSelected: string;
  onOptionClicked: (value: string) => void;
}

const SymbolOptions: React.FC<ISymbolOptionsProps> = ({
  optionSelected,
  onOptionClicked,
}) => {
  return (
    <View style={styles.optionMainContainer}>
      <View style={styles.optionInnerContainer}>
        {options.slice(0, 3).map((option, index) => (
          <Pressable
            key={index}
            onPress={() => onOptionClicked(option.value)}
            style={[
              {
                backgroundColor:
                  optionSelected === option.value
                    ? option.selectedColor
                    : Colors.white,
              },
              styles.optionButton,
            ]}>
            <View
              style={[
                {backgroundColor: getColor(option.value)},
                styles.dot,
              ]}></View>
            <Text style={styles.optionText}>{option.label}</Text>
          </Pressable>
        ))}
      </View>
      <View style={[{marginTop: 10}, styles.optionInnerContainer]}>
        {options.slice(3).map((option, index) => (
          <Pressable
            key={index}
            onPress={() => onOptionClicked(option.value)}
            style={[
              {
                backgroundColor:
                  optionSelected === option.value
                    ? option.selectedColor
                    : Colors.white,
              },
              styles.optionButton,
            ]}>
            <View
              style={[
                {backgroundColor: getColor(option.value)},
                styles.dot,
              ]}></View>
            <Text style={styles.optionText}>{option.label}</Text>
          </Pressable>
        ))}
      </View>
    </View>
  );
};

export default SymbolOptions;
