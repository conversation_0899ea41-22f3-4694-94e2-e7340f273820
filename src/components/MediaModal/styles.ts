import {StyleSheet} from 'react-native';
import {Metrics, Colors} from '../../theme';

export const styles = StyleSheet.create({
  handleStyle: {
    backgroundColor: Colors.handle,
    height: Metrics.ratio(6),
    borderRadius: Metrics.ratio(8),
    width: Metrics.ratio(45),
  },
  bottomSheet: {
    elevation: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 12,
    },
    shadowOpacity: 0.58,
    shadowRadius: 16.0,
  },
  libButton: {
    paddingHorizontal: Metrics.ratio(10),
    height: Metrics.ratio(50),
    borderRadius: Metrics.ratio(6),
    backgroundColor: Colors.itemColors.itemBackgroundColor,
    alignItems: 'center',
    marginHorizontal: Metrics.ratio(24),
    flexDirection: 'row',
  },
  photoButton: {
    height: Metrics.ratio(50),
    borderRadius: Metrics.ratio(6),
    backgroundColor: Colors.itemColors.itemBackgroundColor,
    alignItems: 'center',
    marginHorizontal: Metrics.ratio(24),
    flexDirection: 'row',
    marginVertical: Metrics.baseMargin,
    paddingHorizontal: Metrics.ratio(10),
  },
});
