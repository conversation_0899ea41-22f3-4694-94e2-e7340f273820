import React, {useMemo} from 'react';
import {BottomSheetModal, BottomSheetModalProvider} from '@gorhom/bottom-sheet';
import {AppStyles, Colors, Metrics} from '../../theme';
import {ButtonView, Text} from '..';
import {LIBRARY_ICON, CAMERA_BLACK} from '../../constants/AssetSVGConstants';
import {CREATE_IMPRINT} from '../../constants/StringConstants';
import {styles} from './styles';

interface BottomSheetModalProps {
  onLibraryPress: () => void;
  onPhotoPress: () => void;
  bottomSheetModalRef: any;
  onRecordVideo: () => void;
}

const MediaModal: React.FC<BottomSheetModalProps> = ({
  onPhotoPress,
  onLibraryPress,
  onRecordVideo,
  bottomSheetModalRef,
}) => {
  const snapPoints = useMemo(() => ['10%', '30%'], []);

  return (
    <BottomSheetModalProvider>
      <BottomSheetModal
        handleIndicatorStyle={styles.handleStyle}
        contentHeight={Metrics.screenHeight * 0.5}
        backgroundStyle={styles.bottomSheet}
        enablePanDownToClose={true}
        ref={bottomSheetModalRef}
        index={1}
        snapPoints={snapPoints}>
        <ButtonView onPress={onLibraryPress} style={styles.libButton}>
          <LIBRARY_ICON />
          <Text
            color={Colors.itemColors.subTitleColor}
            style={AppStyles.mLeft5}
            size={'medium'}
            type="medium">
            {CREATE_IMPRINT.CHOOSE_LIBRARY}
          </Text>
        </ButtonView>
        <ButtonView onPress={onPhotoPress} style={styles.photoButton}>
          <CAMERA_BLACK />
          <Text
            color={Colors.itemColors.subTitleColor}
            style={AppStyles.mLeft5}
            size={'medium'}
            type="medium">
            {CREATE_IMPRINT.TAKE_PHOTO}
          </Text>
        </ButtonView>
        <ButtonView
          onPress={onRecordVideo}
          style={[styles.photoButton, {marginVertical: 0}]}>
          <CAMERA_BLACK />
          <Text
            color={Colors.itemColors.subTitleColor}
            style={AppStyles.mLeft5}
            size={'medium'}
            type="medium">
            {CREATE_IMPRINT.RECORD_VIDEO}
          </Text>
        </ButtonView>
      </BottomSheetModal>
    </BottomSheetModalProvider>
  );
};

export default MediaModal;
