import {StyleSheet} from 'react-native';
import {Metrics, Colors} from '../../../theme';
import {Fonts} from '../../../theme';

export const styles = StyleSheet.create({
  container: {
    marginHorizontal: Metrics.ratio(16),
    marginTop: Metrics.ratio(10),
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconWrapper: {
    justifyContent: 'center',
    alignItems: 'center',
    height: Metrics.ratio(40),
    width: Metrics.ratio(40),
    borderRadius: Metrics.ratio(20),
    backgroundColor: Colors.gray,
  },
  icon: {
    height: Metrics.ratio(40),
    width: Metrics.ratio(40),
    borderRadius: Metrics.ratio(20),
  },
  iconPlaceholder: {
    color: Colors.gray,
  },
  input: {
    borderWidth: 1,
    width: Metrics.screenWidth * 0.75,
    paddingVertical: Metrics.baseMargin,
    borderColor: Colors.ChatColors.unreadItem,
    marginLeft: Metrics.ratio(14),
    paddingLeft: Metrics.ratio(10),
  },
  membersText: {
    marginVertical: Metrics.baseMargin,
  },
  membersContainer: {
    marginTop: Metrics.ratio(10),
  },
  memberWrapper: {
    alignItems: 'center',
    marginRight: Metrics.ratio(10),
    position: 'relative',
    justifyContent: 'center',
  },
  memberAvatar: {
    height: Metrics.ratio(50),
    width: Metrics.ratio(50),
    borderRadius: 25,
  },
  removeButton: {
    position: 'absolute',
    top: Metrics.ratio(-5),
    right: Metrics.ratio(-5),
    backgroundColor: Colors.white,
    borderRadius: Metrics.ratio(10),
    height: Metrics.ratio(20),
    width: Metrics.ratio(20),
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: Colors.white2,
  },
  removeText: {
    color: Colors.black,
    fontSize: Fonts.size.xxxSmall,
  },
  userName: {
    marginTop: Metrics.ratio(10),
    fontSize: Fonts.size.xxSmall,
  },
});
