import React from 'react';
import {Alert, Image, Pressable, Text, TextInput, View} from 'react-native';
import {GROUP_CAMERA} from '../../../constants/AssetSVGConstants';
import {Colors} from '../../../theme';
import {styles} from './styles';
import {CHAT_LIST} from '../../../constants/StringConstants';
import {ScrollView} from 'react-native-gesture-handler';

interface Member {
  id: string;
  avatarUrl: string;
}

interface GroupDetailsProps {
  icon: {path: string} | null;
  title: string;
  selectedItems: Member[];
  setTitle: (text: string) => void;
  setSelectedItems: (items: Member[]) => void;
  openCameraView: () => void;
  openGalleryView: () => void;
}

const GroupDetails: React.FC<GroupDetailsProps> = ({
  icon,
  title,
  selectedItems,
  setTitle,
  setSelectedItems,
  openCameraView,
  openGalleryView,
}) => {
  const removeItem = (id: string) => {
    setSelectedItems((prevItems: any[]) =>
      prevItems.filter(item => item.id !== id),
    );
  };

  const handleUploadPicture = () => {
    Alert.alert('Upload Picture Using', '', [
      {text: 'Camera', onPress: openCameraView},
      {text: 'Gallery', onPress: openGalleryView},
      {text: 'Cancel', onPress: () => {}},
    ]);
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Pressable onPress={handleUploadPicture} style={styles.iconWrapper}>
          {icon === null ? (
            <GROUP_CAMERA />
          ) : (
            <Image source={{uri: icon.path}} style={styles.icon} />
          )}
        </Pressable>
        <TextInput
          placeholder={CHAT_LIST.GROUP_Name}
          placeholderTextColor={Colors.text.placeHolderTextColor}
          style={styles.input}
          value={title}
          onChangeText={setTitle}
        />
      </View>
      <Text style={styles.membersText}>
        {selectedItems.length} {CHAT_LIST.MEMBERS}
      </Text>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        style={styles.membersContainer}
        contentContainerStyle={{flexGrow: 1}}>
        {selectedItems.map((item, index) => (
          <View key={index} style={styles.memberWrapper}>
            <Image source={{uri: item.avatarUrl}} style={styles.memberAvatar} />
            <Text style={styles.userName}>{item.userName}</Text>
            <Pressable
              onPress={() => removeItem(item.id)}
              style={styles.removeButton}>
              <Text style={styles.removeText}>×</Text>
            </Pressable>
          </View>
        ))}
      </ScrollView>
    </View>
  );
};

export default GroupDetails;
