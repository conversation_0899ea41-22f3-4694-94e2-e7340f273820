import {StyleSheet} from 'react-native';
import {Colors, Fonts, Metrics} from '../../../theme';

export default StyleSheet.create({
  qaContainer: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  button: {
    width: Metrics.ratio(80),
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonContainer: {
    borderRadius: Metrics.ratio(10),
    padding: Metrics.ratio(10),
    marginLeft: Metrics.ratio(-20),
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.black,
  },
});
