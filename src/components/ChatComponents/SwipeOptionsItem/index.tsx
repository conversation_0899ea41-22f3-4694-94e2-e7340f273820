import {Pressable, View} from 'react-native';
import {Text} from '../../../components';
import {AppStyles, Colors, Metrics} from '../../../theme';
import {WHITE_DOTS} from '../../../constants/AssetSVGConstants';
import {IRecentChatList} from '../../../types';
import styles from './styles';
import _ from 'lodash';
import {CHAT_LIST} from '../../../constants/StringConstants';

interface SwipeOptionsItemProps {
  item: IRecentChatList;
  onMoreOptionsPress: (item: IRecentChatList) => void;
}

const SwipeOptionsItem: React.FC<SwipeOptionsItemProps> = ({
  item,
  onMoreOptionsPress,
}) => {
  return (
    <View style={styles.qaContainer}>
      <View style={styles.button}>
        <Pressable
          onPress={() => {
            onMoreOptionsPress(item);
          }}
          style={styles.buttonContainer}>
          <Text style={AppStyles.mBottom10} color={Colors.white}>
            {CHAT_LIST.MORE}
          </Text>
          <WHITE_DOTS />
        </Pressable>
      </View>
    </View>
  );
};

export default SwipeOptionsItem;
