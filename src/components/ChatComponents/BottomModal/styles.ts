import {StyleSheet} from 'react-native';
import {Metrics, Colors} from '../../../theme';

export const styles = StyleSheet.create({
  handleStyle: {
    backgroundColor: Colors.handle,
    height: Metrics.ratio(6),
    borderRadius: Metrics.ratio(8),
    width: Metrics.ratio(45),
  },
  bottomSheet: {
    elevation: 10,
    shadowColor: Colors.black,
    shadowOffset: {
      width: 0,
      height: Metrics.ratio(12) as number,
    },
    shadowOpacity: 0.58,
    shadowRadius: 16.0,
  },

  Button: {
    marginVertical: Metrics.baseMargin,
    height: Metrics.ratio(50),
    borderRadius: Metrics.ratio(6),
    backgroundColor: Colors.itemColors.itemBackgroundColor,
    alignItems: 'center',
    marginHorizontal: Metrics.ratio(24),
    paddingHorizontal: Metrics.baseMargin,
    flexDirection: 'row',
  },
});
