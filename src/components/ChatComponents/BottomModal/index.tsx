import React, {useMemo} from 'react';
import {Platform} from 'react-native';
import {BottomSheetModal, BottomSheetModalProvider} from '@gorhom/bottom-sheet';
import {AppStyles, Colors, Metrics} from '../../../theme';
import {ButtonView, Text} from '../../';
import {CHAT_LIST} from '../../../constants/StringConstants';
import {styles} from './styles';

interface BottomModalProps {
  onChatPress: () => void;
  onGroupPress: () => void;

  bottomSheetModalRef: any;
}

const BottomModal: React.FC<BottomModalProps> = ({
  onChatPress,
  onGroupPress,

  bottomSheetModalRef,
}) => {
  const snapPoints = useMemo(
    () => ['10%', Platform.OS == 'android' ? '25%' : '20%'],
    [],
  );

  return (
    <BottomSheetModalProvider>
      <BottomSheetModal
        handleIndicatorStyle={styles.handleStyle}
        contentHeight={Metrics.screenHeight * 0.5}
        backgroundStyle={styles.bottomSheet}
        enablePanDownToClose={true}
        ref={bottomSheetModalRef}
        index={1}
        snapPoints={snapPoints}>
        <ButtonView onPress={onChatPress} style={styles.Button}>
          <Text
            color={Colors.itemColors.subTitleColor}
            style={AppStyles.mLeft5}
            size={'medium'}
            type="medium">
            {CHAT_LIST.NEW_CHAT}
          </Text>
        </ButtonView>
        <ButtonView onPress={onGroupPress} style={styles.Button}>
          <Text
            color={Colors.itemColors.subTitleColor}
            style={AppStyles.mLeft5}
            size={'medium'}
            type="medium">
            {CHAT_LIST.NEW_GROUP}
          </Text>
        </ButtonView>
      </BottomSheetModal>
    </BottomSheetModalProvider>
  );
};

export default BottomModal;
