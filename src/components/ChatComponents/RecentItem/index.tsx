import {Pressable, View} from 'react-native';
import {<PERSON><PERSON>, ButtonView, Text} from '../../../components';
import {AppStyles, Colors, Metrics} from '../../../theme';
import {PLACEHOLDER_IMAGE, TIME_FORMAT1} from '../../../constants';
import {DOT, FLAG} from '../../../constants/AssetSVGConstants';
import {IRecentChatList, UserState} from '../../../types';
import styles from './styles';
import util from '../../../util';
import _ from 'lodash';

interface RecentItemProps {
  item: IRecentChatList;
  currentUser: UserState;
  onItemPress: (item: IRecentChatList) => void;
}

const RecentItem: React.FC<RecentItemProps> = ({
  item,
  currentUser,
  onItemPress,
}) => {
  const index = _.findIndex(
    item.participantList,
    participant => participant.participantID === currentUser.userInfo.userId,
  );
  const updatedItem = _.cloneDeep(item);
  updatedItem.participantList.splice(index, 1);

  const renderSingleImage = (updatedItem: IRecentChatList) => {
    return (
      <Avatar
        image={
          updatedItem?.participantList[0].participant.avatarUrl ??
          PLACEHOLDER_IMAGE
        }
        style={styles.imageContainer}
        imageStyle={styles.image}
      />
    );
  };

  const renderMultipleImages = (updatedItem: IRecentChatList) => {
    return (
      <View style={styles.multiImageContainer}>
        {item.icon === null ? (
          item.participantList
            .slice(0, 2)
            .map((item, index) => (
              <Avatar
                key={index}
                image={item.participant.avatarUrl}
                style={styles.multiAvatar}
              />
            ))
        ) : (
          <Avatar
            image={updatedItem.icon ?? PLACEHOLDER_IMAGE}
            style={styles.imageContainer}
            imageStyle={styles.image}
          />
        )}
        {updatedItem.icon === null &&
          updatedItem.participantList.length > 2 && (
            <Text
              type="semi_bold"
              style={styles.count}
              color={Colors.black}
              textAlign="left">
              + {updatedItem.participantList.length - 2}
            </Text>
          )}
      </View>
    );
  };
  return (
    <Pressable
      style={[
        styles.userinfoContainer,
        item.unreadMessageCount > 0
          ? {backgroundColor: Colors.ChatColors.unreadItem}
          : {},
      ]}
      onPress={() => onItemPress(updatedItem)}>
      <View style={styles.itemContainer}>
        <View style={styles.image}>
          {updatedItem.participantList.length == 1 &&
            renderSingleImage(updatedItem)}
          {item.unreadMessageCount > 0 && (
            <View style={styles.singeUnreadCounter}>
              <DOT />
            </View>
          )}

          {updatedItem.participantList.length >= 2 &&
            renderMultipleImages(updatedItem)}
          {updatedItem.title === null &&
            item.unreadMessageCount > 0 &&
            item.participantList.length > 2 && (
              <View style={[styles.multipleUnreadCounter]}>
                <DOT />
              </View>
            )}
        </View>
        <View
          style={[
            AppStyles.alignStart,
            updatedItem.participantList.length > 1 ? AppStyles.mLeft25 : {},
          ]}>
          {updatedItem.participantList.length > 1 ? (
            <View style={styles.multipleText}>
              {updatedItem.title === null ? (
                updatedItem.participantList.slice(0, 2).map((item, index) => (
                  <Text
                    numberOfLines={1}
                    key={index}
                    color={Colors.itemColors.titleColor}
                    textAlign="center"
                    style={AppStyles.mLeft10}
                    type="bold"
                    size={'buttonText'}>
                    {item.participant.displayName}
                  </Text>
                ))
              ) : (
                <Text
                  numberOfLines={1}
                  key={index}
                  color={Colors.itemColors.titleColor}
                  textAlign="center"
                  type="bold"
                  size={'buttonText'}
                  style={styles.titleText}>
                  {item.title}
                </Text>
              )}
              {updatedItem.title === null &&
                updatedItem.participantList.length > 2 && (
                  <Text
                    color={Colors.itemColors.titleColor}
                    textAlign="center"
                    type="extra_bold"
                    size={'buttonText'}>
                    ...
                  </Text>
                )}
            </View>
          ) : (
            <Text
              numberOfLines={1}
              color={Colors.itemColors.titleColor}
              textAlign="left"
              style={[AppStyles.mLeft20, styles.extraWidth]}
              type="bold"
              size={'buttonText'}>
              {updatedItem &&
                updatedItem?.participantList[0]?.participant?.displayName}
            </Text>
          )}
          <Text
            numberOfLines={1}
            color={Colors.text.placeHolderTextColor}
            style={[
              styles.textMessage,
              item.title !== null && item.unreadMessageCount === 0
                ? styles.marginLeft
                : {},
            ]}
            type="base"
            size={'xSmall'}>
            {updatedItem &&
            (updatedItem.participantList.length > 1 ||
              (updatedItem.participantList.length === 1 &&
                !updatedItem.isBlocked))
              ? updatedItem.recentMessage?.content
              : ''}
          </Text>
        </View>
        {updatedItem.participantList.length === 1 ? (
          <ButtonView
            key={index}
            style={styles.flagContainerUnselected}
            onPress={() => {}}>
            {updatedItem.isFlagged !== null && <FLAG height={10} width={10} />}
          </ButtonView>
        ) : null}

        <Text
          color={Colors.BlackButton}
          size={'xxSmall'}
          style={[
            updatedItem.participantList.length > 2
              ? styles.time
              : styles.multiUsersTime,
          ]}
          textAlign="right"
          alignSelf="center">
          {util.getFormattedDateTime(item.createdAt, TIME_FORMAT1)}
        </Text>
      </View>
    </Pressable>
  );
};

export default RecentItem;
