import {StyleSheet} from 'react-native';
import {Colors, Fonts, Metrics} from '../../../theme';

export default StyleSheet.create({
  userinfoContainer: {
    backgroundColor: Colors.background.home,
    paddingVertical: Metrics.baseMargin,
    marginVertical: Metrics.smallMargin,
    marginHorizontal: Metrics.baseMargin,
    borderRadius: Metrics.ratio(10),
  },
  imageContainer: {
    marginTop: Metrics.ratio(0),
    height: Metrics.ratio(45),
    width: Metrics.ratio(45),
  },
  image: {
    height: Metrics.ratio(45),
    width: Metrics.ratio(45),
  },
  singeUnreadCounter: {
    position: 'absolute',
    bottom: Metrics.ratio(-2),
    right: Metrics.ratio(0),
    backgroundColor: Colors.ChatColors.unreadItem,
    borderRadius: Metrics.ratio(20),
  },
  multipleUnreadCounter: {
    position: 'absolute',
    bottom: Metrics.ratio(-20),
    right: Metrics.ratio(-30),
    backgroundColor: Colors.ChatColors.unreadItem,
    borderRadius: Metrics.ratio(20),
  },
  multiImageContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  multiAvatar: {
    marginLeft: Metrics.ratio(0),
    width: Metrics.ratio(45),
    height: Metrics.ratio(45),
    borderRadius: Metrics.ratio(25),
    marginRight: Metrics.ratio(-15),
  },
  count: {
    position: 'absolute',
    right: Metrics.ratio(0),
    top: Metrics.ratio(-5),
  },
  textMessage: {
    flex: 1,
    marginLeft: Metrics.ratio(20),
    width: Metrics.screenWidth * 0.5,
  },
  titleText: {marginLeft: Metrics.ratio(-8)},
  time: {
    flex: 1,
    alignItems: 'center',
  },
  itemContainer: {flexDirection: 'row', marginHorizontal: Metrics.ratio(10)},
  multipleText: {
    marginTop: Metrics.ratio(10),
    maxWidth: Metrics.ratio(230),
    flexDirection: 'row',
    alignItems: 'center',
  },
  multiUsersTime: {
    marginTop: Metrics.ratio(-20),
    flex: 1,
    alignItems: 'center',
  },
  flagContainerUnselected: {
    right: Metrics.ratio(50),
    width: Metrics.ratio(35),
    height: Metrics.ratio(35),
  },
  extraWidth: {width: Metrics.screenWidth * 0.5},
  marginLeft: {marginLeft: -5},
});
