import {StyleSheet} from 'react-native';
import {Colors, Metrics} from '../../../theme';

export default StyleSheet.create({
  itemContainer: {
    marginVertical: Metrics.smallMargin,
    backgroundColor: Colors.white,
    borderRadius: Metrics.ratio(20),
    alignItems: 'center',
    marginHorizontal: Metrics.baseMargin,
    padding: Metrics.ratio(24),
    borderColor: Colors.itemColors.itemBorderColor,
    //  borderWidth: Metrics.ratio(1),
    shadowColor: Colors.black,
  },
  subContainer: {
    alignSelf: 'center',
    justifyContent: 'center',
    flex: 8,
    alignItems: 'center',
    flexDirection: 'row',
  },
  imageContainer: {
    marginTop: Metrics.ratio(0),
    height: Metrics.ratio(45),
    width: Metrics.ratio(45),
  },
  image: {height: Metrics.ratio(45), width: Metrics.ratio(45)},
  selectedItemContainer: {flex: 2, alignItems: 'flex-end'},
  dotStyle: {
    borderRadius: Metrics.ratio(100),
    height: Metrics.ratio(22),
    width: Metrics.ratio(22),
    backgroundColor: Colors.borderColors.activeBorderColor,
    borderColor: Colors.borderColors.incorrectBorderColor,
  },
});
