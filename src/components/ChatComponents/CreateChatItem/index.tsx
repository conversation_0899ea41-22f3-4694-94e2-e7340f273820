// @flow
import React from 'react';
import {View} from 'react-native';
import {Text, ButtonView, Avatar} from '../../../components';
import styles from './styles';
import {Colors, AppStyles, Metrics} from '../../../theme';
import {TICK_ICON} from '../../../constants/AssetSVGConstants';
import {PLACEHOLDER_IMAGE} from '../../../constants';
import _ from 'lodash';
import {User} from '../../../types';

interface ItemProps {
  item: User;
  selectedItems: any[];
  onItemPress: () => void;
}

const Item: React.FC<ItemProps> = ({item, onItemPress, selectedItems}) => {
  const isSelected = selectedItems.some(selected => selected.id === item.id);
  return (
    <ButtonView style={styles.itemContainer} onPress={onItemPress}>
      <View style={styles.subContainer}>
        <Avatar
          image={
            !_.isEmpty(item.avatarUrl) ? item.avatarUrl : PLACEHOLDER_IMAGE
          }
          style={styles.imageContainer}
          imageStyle={styles.image}
        />
        <View>
          <Text
            numberOfLines={1}
            type="semi_bold"
            size="normal"
            color={Colors.itemColors.titleColor}
            style={[AppStyles.mLeftBase, {width: Metrics.screenWidth * 0.6}]}>
            {!_.isEmpty(item.displayName) ? item.displayName : ''}
          </Text>
          <Text
            numberOfLines={2}
            type="medium"
            size="xSmall"
            color={Colors.itemColors.subTitleColor}
            style={AppStyles.mLeftBase}>
            {item.userName}
          </Text>
        </View>
        <View style={styles.selectedItemContainer}>
          {!isSelected ? <View style={styles.dotStyle} /> : <TICK_ICON />}
        </View>
      </View>
    </ButtonView>
  );
};

export default Item;
