import React from 'react';
import {View} from 'react-native';
import {ButtonView, Text} from '../..';
import {styles} from './styles';
import {IRecentChatList} from '../../../types';
import {CHAT_LIST} from '../../../constants/StringConstants';
import {MODAL_DELETE} from '../../../constants/AssetSVGConstants';
import MoreOptionsBottomModal from '../../TimeLine/MoreOptionBottomModal';
import styes from '../../FriendsComponents/SuggestedFriendItem/styes';

interface MoreOptionsModalProps {
  selectedItem: IRecentChatList | undefined;
  showMoreModal: boolean;
  hideModal: () => void;
  onLeaveGroupPress: () => void;
  onDeleteChat: () => void;
  onBlockChat: () => void;
}

const MoreOptionsModal: React.FC<MoreOptionsModalProps> = ({
  showMoreModal,
  hideModal,
  selectedItem,
  onLeaveGroupPress,
  onDeleteChat,
  onBlockChat,
}) => {
  return (
    <MoreOptionsBottomModal
      visible={showMoreModal}
      onClose={() => hideModal()}
      onBackdropPress={() => hideModal()}>
      <View style={styles.modalParentContainer}>
        {selectedItem && selectedItem.participantList.length > 2 && (
          <>
            <View style={styles.modalOptionSeperator} />
            <View>
              <ButtonView
                style={styles.modalContainer}
                onPress={() => onLeaveGroupPress()}>
                <MODAL_DELETE />
                <View style={styles.modalTextContainer}>
                  <Text style={styles.modalTextOne}>
                    {CHAT_LIST.LEAVE_GROUP}
                  </Text>
                </View>
              </ButtonView>
            </View>
            <View style={styles.modalOptionSeperator} />
          </>
        )}

        {selectedItem && selectedItem.participantList.length === 2 && (
          <>
            <View>
              <ButtonView
                style={[styles.modalContainer, styes.extraMargin]}
                onPress={() => onDeleteChat()}>
                <MODAL_DELETE />
                <View style={styles.modalTextContainer}>
                  <Text style={styles.modalTextOne}>
                    {CHAT_LIST.DELETE_CHAT}
                  </Text>
                </View>
              </ButtonView>
              <View style={styles.modalOptionSeperator} />
            </View>
            <View>
              <ButtonView
                style={[styles.modalContainer, styes.extraMargin]}
                onPress={() => onBlockChat()}>
                <MODAL_DELETE />
                <View style={styles.modalTextContainer}>
                  <Text style={styles.modalTextOne}>
                    {selectedItem?.isBlocked
                      ? CHAT_LIST.UNBLOCK_CHAT
                      : CHAT_LIST.BLOCK_CHAT}
                  </Text>
                </View>
              </ButtonView>
              <View style={styles.modalOptionSeperator} />
            </View>
          </>
        )}
      </View>
    </MoreOptionsBottomModal>
  );
};

export default MoreOptionsModal;
