import {StyleSheet} from 'react-native';
import {Metrics, Colors} from '../../../theme';
import {Fonts} from '../../../theme';

export const styles = StyleSheet.create({
  modalParentContainer: {
    marginTop: Metrics.ratio(0),
  },
  modalContainer: {
    marginTop: Metrics.ratio(25),
    marginHorizontal: Metrics.ratio(40),
    flexDirection: 'row',
  },
  modalTextContainer: {
    paddingLeft: Metrics.ratio(20),
    flex: 1,
    justifyContent: 'center',
  },
  modalOptionSeperator: {
    marginTop: Metrics.ratio(10),
    backgroundColor: Colors.lightGray,
    height: 0.5,
    width: Metrics.screenWidth - 40,
    alignSelf: 'center',
  },
  modalTextOne: {
    fontFamily: Fonts.type.semi_bold,
    fontSize: Metrics.ratio(13),
    color: Colors.text.titleColor,
  },
});
