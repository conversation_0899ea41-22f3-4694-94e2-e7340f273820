import {View} from 'react-native';
import {styles} from './styles';
import {ButtonView, Text} from '../..';
import {CHAT_LIST, FRIENDS} from '../../../constants/StringConstants';
import {Colors} from '../../../theme';
import {FOLLOWERS} from '../../../types';

interface TabsProps {
  onChatPress: () => void;
  onGroupPress: () => void;
  chatList: boolean;
  singleChatList: any[];
  GroupList: any[];
}

const Tabs: React.FC<TabsProps> = ({
  onChatPress,
  onGroupPress,
  chatList,
  singleChatList,
  GroupList,
}) => {
  return (
    <View style={styles.container}>
      <ButtonView
        style={[
          styles.activeButton,
          {backgroundColor: chatList ? Colors.black1 : Colors.white},
        ]}
        onPress={onChatPress}>
        <Text
          size={'buttonText'}
          color={
            chatList ? Colors.white : Colors.tabsTextColor.inActiveFriendList
          }
          type="semi_bold">
          {`${CHAT_LIST.MESSAGES}(${
            singleChatList?.length === 0
              ? '00'
              : singleChatList?.length.toString().padStart(2, '0')
          })`}
        </Text>
      </ButtonView>
      <ButtonView
        style={[
          styles.requestButton,
          {backgroundColor: !chatList ? Colors.black1 : Colors.white},
        ]}
        onPress={onGroupPress}>
        <Text
          size={'buttonText'}
          color={
            !chatList ? Colors.white : Colors.tabsTextColor.inActiveFriendList
          }
          type="semi_bold">
          {`${CHAT_LIST.GROUP}(${
            GroupList?.length === 0
              ? '0'
              : GroupList?.length.toString().padStart(2, '0')
          })`}
        </Text>
      </ButtonView>
    </View>
  );
};

export default Tabs;
