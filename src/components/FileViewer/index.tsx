import React, {useEffect, useState} from 'react';
import {Platform} from 'react-native';
import WebView from 'react-native-webview';
import axios from 'axios';

const FileViewer = ({pdfURL}: any) => {
  const [fileType, setFileType] = useState<string>('null');

  useEffect(() => {
    const fetchFileType = async () => {
      try {
        const response = await axios.head(pdfURL);
        const contentType = response.headers['content-type'];

        if (contentType && contentType.includes('application/pdf')) {
          setFileType('pdf');
        } else {
          setFileType('other');
        }
      } catch (error) {
        console.error('Error fetching content type:', error);
      }
    };

    fetchFileType();
  }, [pdfURL]);

  let uri = pdfURL;

  if (fileType === 'pdf' && Platform.OS === 'android') {
    uri = `https://docs.google.com/gview?embedded=true&url=${pdfURL}`;
  } else if (Platform.OS === 'android') {
    uri = `https://view.officeapps.live.com/op/view.aspx?src=${pdfURL}`;
  }

  return (
    <WebView
      cacheMode="LOAD_NO_CACHE"
      scalesPageToFit={Platform.OS == 'android'}
      source={{
        uri: Platform.OS === 'ios' ? pdfURL : uri,
      }}
    />
  );
};

export default FileViewer;
