import React from 'react';
import {View} from 'react-native';
import {Text} from '../../../components';
import {Colors, Fonts, Metrics} from '../../../theme';
import util from '../../../util';
import {DATE_FORMAT1} from '../../../constants';
import {Marker} from 'react-native-maps';
import {styles} from './styles';
import {CHECK_IN_TAB} from '../../../constants/StringConstants';

interface MarkerItemsProps {
  data: {
    place: string;
    date: string;
  };
  coordinate: {
    latitude: number;
    longitude: number;
  };
}

const MARKERS: React.FC<MarkerItemsProps> = ({data, coordinate}) => {
  return (
    <Marker
      id={data.date}
      title={CHECK_IN_TAB.VISITED}
      coordinate={coordinate}
      description={`${util.getFormattedDateTime(data.date, DATE_FORMAT1)}`}>
      <View style={styles.container}>
        <Text
          alignSelf="flex-start"
          color={Colors.black}
          type="bold"
          size={'xSmall'}>
          {data.place}
        </Text>
      </View>
    </Marker>
  );
};

export default MARKERS;
