import {StyleSheet} from 'react-native';
import {Colors, Metrics} from '../../../theme';

export const styles = StyleSheet.create({
  container: {
    marginBottom: Metrics.ratio(24),
    justifyContent: 'space-between',
    flexDirection: 'row',
    marginHorizontal: Metrics.baseMargin,
  },
  activeButton: {
    backgroundColor: Colors.black1,
    borderRadius: Metrics.ratio(100),
    paddingHorizontal: Metrics.ratio(16),
    paddingVertical: Metrics.ratio(11),
    alignItems: 'center',
    justifyContent: 'center',
  },
  inactiveButton: {
    flexDirection: 'row',
    backgroundColor: Colors.white,
    marginLeft: Metrics.ratio(10),
    borderRadius: Metrics.ratio(100),
    paddingHorizontal: Metrics.ratio(16),
    paddingVertical: Metrics.ratio(11),
    alignItems: 'center',
    justifyContent: 'center',
  },
});
