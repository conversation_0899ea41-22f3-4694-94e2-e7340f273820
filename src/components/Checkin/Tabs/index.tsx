import {View} from 'react-native';
import {styles} from './styles';
import {ButtonView, Text} from '../..';
import {AppStyles, Colors} from '../../../theme';
import {CHECK_IN_TAB} from '../../../constants/StringConstants';
import {BLACK_PLUS} from '../../../constants/AssetSVGConstants';

interface TabProps {
  onAddPress: () => void;
  tab1Text?: string;
  tab2Text?: string;
}

const MapViewTabs: React.FC<TabProps> = ({
  onAddPress,
  tab1Text = CHECK_IN_TAB.YOUR_CHECK_IN,
  tab2Text = CHECK_IN_TAB.ADD,
}) => {
  return (
    <View style={styles.container}>
      <ButtonView style={styles.activeButton}>
        <Text size={'buttonText'} color={Colors.white} type="semi_bold">
          {tab1Text}
        </Text>
      </ButtonView>
      <ButtonView style={styles.inactiveButton} onPress={onAddPress}>
        <BLACK_PLUS color={Colors.black} />
        <Text
          size={'buttonText'}
          color={Colors.black}
          type="semi_bold"
          style={AppStyles.mLeft10}>
          {tab2Text}
        </Text>
      </ButtonView>
    </View>
  );
};

export default MapViewTabs;
