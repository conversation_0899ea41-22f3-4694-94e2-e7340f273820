import * as Progress from 'react-native-progress';
import {Colors, Metrics} from '../../../theme';
import {styles} from './styles';
import {ViewStyle} from 'react-native';

const MAX_STEPS: number = 3;

interface IProgressBar {
  steps: number;
  maxSteps?: number;
  width?: any;
  style?: ViewStyle;
  borderColor?: string;
  unfilledColor?: string;
}

const ProgressBar: React.FC<IProgressBar> = ({
  steps,
  maxSteps = MAX_STEPS,
  width = Metrics.screenWidth * 0.5,
  style = styles.container,
  borderColor = Colors.white,
  unfilledColor = Colors.itemColors.itemBackgroundColor,
}) => {
  let progress = null;
  if (maxSteps !== 10) {
    if (steps === 0 || steps === 1) {
      if (steps === 1) steps = 0;
      progress = (100 / maxSteps / 100) * steps + 0.2;
    } else {
      progress = (100 / maxSteps / 100) * steps;
    }
  } else {
    if (steps === 0) {
      progress = 0;
    } else {
      progress = steps / maxSteps;
    }
  }

  return (
    <Progress.Bar
      progress={progress}
      width={width}
      borderRadius={8}
      style={style}
      useNativeDriver
      color={Colors.black}
      unfilledColor={unfilledColor}
      borderColor={borderColor}
    />
  );
};

export default ProgressBar;
