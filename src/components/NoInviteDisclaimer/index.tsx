import React from 'react';
import {Modal, Pressable, View, Text} from 'react-native';
import {Fonts} from '../../theme';
import styles from './styles';
import {CROSS_GRAY} from '../../constants/AssetSVGConstants';
import {NO_INVITE_CODE} from '../../constants/StringConstants';

interface NoInviteDisclaimerProps {
  visible: boolean;
  onDone: () => void;
  onClose: () => void;
}

const NoInviteDisclaimerAlert: React.FC<NoInviteDisclaimerProps> = ({
  visible,
  onDone,
  onClose,
}) => {
  const handleDone = () => {
    onDone();
  };

  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={visible}
      onRequestClose={onClose}>
      <View style={styles.modalContainer}>
        <View style={styles.popup}>
          <View style={styles.popupInnerContainer}>
            <Text style={styles.placeHolder}>
              {NO_INVITE_CODE.NO_INVITE_CODE}
            </Text>

            <Pressable style={styles.crossContainer} onPress={onClose}>
              <CROSS_GRAY />
            </Pressable>
            <Text style={styles.title}>
              {NO_INVITE_CODE.INVITE_CODE_SUCCESS}
            </Text>
            <Pressable onPress={handleDone} style={styles.doneButton}>
              <Text style={styles.okText}>Ok</Text>
            </Pressable>
          </View>
        </View>
      </View>
    </Modal>
  );
};

export default NoInviteDisclaimerAlert;
