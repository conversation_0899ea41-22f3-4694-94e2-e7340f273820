// @flow
import {StyleSheet} from 'react-native';
import {Colors, Metrics, Fonts, AppStyles} from '../../theme';
import {DH, DW} from '../../theme/responsive';

export default StyleSheet.create({
  doneButton: {
    marginTop: 10,
    alignSelf: 'center',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 5,
    backgroundColor: 'black',
  },
  closeButton: {
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 5,
    borderWidth: 1,
    borderColor: 'black',
    backgroundColor: Colors.gray2,
  },
  popup: {
    top: '30%',
    width: '90%',
    justifyContent: 'center',
    alignSelf: 'center',
  },
  popupInnerContainer: {
    backgroundColor: 'white',
    padding: 20,
    borderRadius: 10,
    elevation: 5,
  },
  placeHolder: {
    fontFamily: Fonts.type.semi_bold,
    textAlign: 'center',
    fontSize: 14,
  },
  input: {
    borderWidth: 0.5,
    borderColor: 'black',
    marginTop: 10,
    paddingTop: 10,
    borderRadius: 5,
    padding: 10,
    fontFamily: Fonts.type.semi_bold,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 20,
  },
  title: {
    marginTop: 10,
    fontFamily: Fonts.type.medium,
    fontSize: 13,
    textAlign: 'center',
  },
  errorText: {
    fontSize: 12,
    color: Colors.red,
    textAlign: 'center',
    marginTop: 10,
  },
  modalContainer: {flex: 1, backgroundColor: 'rgba(0, 0, 0, 0.7)'},
  crossContainer: {
    alignSelf: 'flex-end',
    flex: 1,
    top: -25,
    right: -10,
    alignItems: 'flex-end',
  },
  okText: {
    color: 'white',
    fontFamily: Fonts.type.medium,
  },
});
