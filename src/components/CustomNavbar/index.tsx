import React from 'react';
import {View, SafeAreaView, ViewStyle} from 'react-native';
import {Text, ButtonView} from '../';
import styles from './styles';
import {Colors, AppStyles, Metrics} from '../../theme';
import {PLACEHOLDER_IMAGE} from '../../constants';
import {BACK_ICON, NAVBAR_LEF_ICON} from '../../constants/AssetSVGConstants';
import {HOME} from '../../constants/StringConstants';

interface Props {
  navigation?: any;
  profileImage?: string;
  hasLogo?: boolean;
  hasBack?: boolean;
  hasRight?: boolean;
  hasMultiRight?: boolean;
  title?: string;
  leftBtnImage?: any;
  leftBtnPress1?: () => void;
  leftBtnPress?: () => void;
  leftBtnText?: string;
  rightBtnImage?: any;
  rightBtnImage1?: any;
  rightBtnImage2?: any;
  rightBtnImage3?: any;

  rightBtnPress?: () => void;
  rightBtnPress1?: () => void;
  rightBtnPress2?: () => void;
  rightBtnPress3?: () => void;
  rightBtnText?: string;
  titleColor?: string;
  hasBorder?: boolean;
  style?: {};
  img1Style?: {};
  img2Style?: {};
  hasSearch?: boolean;
  onSearchText?: (text: string) => void;
  isSearching?: boolean;
  showStatus?: boolean;
  logoViewStyle?: {};
  imageName?: string;
  titleImage?: any;
  titleSize?: any;
  hasTitleWIthImage?: boolean;
  titleContainer?: ViewStyle;
  alignTitleLeft?: boolean;
}

const CustomNavbar: React.FC<Props> = ({
  hasTitleWIthImage = false,
  profileImage = PLACEHOLDER_IMAGE,
  hasLogo = false,
  hasBack = true,
  titleColor = Colors.text.titleColor,
  leftBtnImage,
  leftBtnPress1 = () => {},
  leftBtnPress = () => {},
  leftBtnText = '',
  rightBtnImage,
  rightBtnPress = () => {},
  rightBtnPress1 = () => {},
  rightBtnPress2 = () => {},
  rightBtnPress3 = () => {},
  rightBtnText = '',
  hasBorder = true,
  style = {},
  hasSearch = false,
  onSearchText = () => {},
  isSearching = false,
  showStatus = false,
  logoViewStyle = {},
  img1Style = {},
  img2Style = {},
  imageName = '',
  titleImage,
  title = '',
  titleSize = '',
  hasRight,
  rightBtnImage1,
  rightBtnImage2,
  rightBtnImage3,
  hasMultiRight,
  titleContainer,
  alignTitleLeft,
}) => {
  const renderLeft = () => {
    const renderBack = hasBack && !leftBtnText && !leftBtnImage;

    return (
      <ButtonView
        onPress={hasBack ? leftBtnPress : undefined}
        style={styles.btnWrapper}>
        {leftBtnText && (
          <Text size="normal" color={Colors.black} type="semi_bold">
            {leftBtnText}
          </Text>
        )}
        {renderBack && <BACK_ICON />}
        {leftBtnImage && leftBtnImage}
      </ButtonView>
    );
  };

  const renderRight = () => {
    return (
      <ButtonView onPress={rightBtnPress} style={[styles.rightBtnWrapper]}>
        {rightBtnText && (
          <Text
            type="medium"
            numberOfLines={1}
            size={'xSmall'}
            color={Colors.navbar.rightTitleColor}>
            {rightBtnText}
          </Text>
        )}
        {rightBtnImage && rightBtnImage}
      </ButtonView>
    );
  };

  const renderRightMultiple = (rightBtnImage1: any, rightBtnImage2: any) => {
    return (
      <View style={styles.rightContainer}>
        <ButtonView style={img1Style} onPress={rightBtnPress}>
          {rightBtnImage}
        </ButtonView>
        <ButtonView style={img1Style} onPress={rightBtnPress1}>
          {rightBtnImage1}
        </ButtonView>

        <ButtonView
          style={[img2Style, AppStyles.mLeft5]}
          onPress={rightBtnPress2}>
          {rightBtnImage2}
        </ButtonView>
        <ButtonView
          style={[img2Style, AppStyles.mLeft5]}
          onPress={rightBtnPress3}>
          {rightBtnImage3}
        </ButtonView>
      </View>
    );
  };

  const renderTitle = (titleImage?: any, title?: String, titleSize?: any) => {
    return (
      <View style={[styles.titleContainer, titleContainer]}>
        {titleImage ? (
          titleImage
        ) : (
          <View
            style={[
              alignTitleLeft
                ? styles.alignTitleLeft
                : styles.titleTextContainer,
            ]}>
            <Text
              alignSelf="center"
              maxWidth={Metrics.screenWidth / 2}
              color={titleColor || Colors.text.titleColor}
              numberOfLines={2}
              size={titleSize || 'normal'}
              type="bold">
              {title || ''}
            </Text>
            {/*showStatus && renderStatus(status)*/}
          </View>
        )}
      </View>
    );
  };

  const renderTitleWithImage = (
    titleImage = (<NAVBAR_LEF_ICON />) as any,
    title?: String,
    titleSize?: any,
  ) => {
    return (
      <View style={styles.titleImageContainer}>
        {titleImage}

        <Text
          style={AppStyles.mLeft5}
          color={titleColor || Colors.text.homeTitleColor}
          numberOfLines={1}
          size={titleSize || 'large'}
          type="bold">
          {title || HOME.NAVBAR_TITLE}
        </Text>
        {/*showStatus && renderStatus(status)*/}
      </View>
    );
  };

  return (
    <SafeAreaView
      style={[
        styles.container,
        style,
        hasBorder ? styles.borderBottom : {},
        hasSearch ? styles.searchHeader : {},
      ]}>
      <View
        style={{
          justifyContent: 'center',
          flexDirection: 'row',
          alignItems: 'center',
        }}>
        {hasBack && renderLeft()}
        {renderTitle(titleImage, title, titleSize)}
        {hasTitleWIthImage &&
          renderTitleWithImage(titleImage, title, titleSize)}

        {hasRight && renderRight()}
        {hasMultiRight && renderRightMultiple(rightBtnImage1, rightBtnImage2)}
        {/*{hasProfile && renderProfilePicture()}
        {hasRight  renderRightMultiple()} */}
      </View>
      {/*hasSearch && <View style={styles.centerInner}>{renderSearch()}</View>*/}
    </SafeAreaView>
  );
};

export default CustomNavbar;
