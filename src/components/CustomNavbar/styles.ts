// @flow
import {Platform, StyleSheet} from 'react-native';
import {Colors, Metrics} from '../../theme';

export default StyleSheet.create({
  container: {
    width: Metrics.screenWidth,
    backgroundColor: Colors.white,
    height:
      Platform.OS == 'android'
        ? Metrics.statusBarHeight + 50
        : Metrics.statusBarHeight,
    justifyContent: 'center',
    alignItems: 'center',
  },
  borderBottom: {},
  btnImage: {
    width: 25,
    height: 25,
    resizeMode: 'contain',
  },
  rightimage2: {
    width: 35,
    height: 35,
  },
  btnImage2: {
    width: Metrics.screenWidth * 0.35,
    height: Metrics.screenHeight * 0.08,
    marginLeft: 5,
    marginTop: 1,
  },
  btnWrapper: {
    zIndex: 999,
    position: 'absolute',
    left: 0,
    paddingLeft: Metrics.ratio(15),
    minWidth: 50,
    maxWidth: 80,
  },
  rightBtnWrapper: {
    marginRight: Metrics.ratio(15),
    position: 'absolute',
    right: 0,
    alignItems: 'center',

    minWidth: 80,
  },
  titleContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  titleTextContainer: {
    alignSelf: 'center',
    justifyContent: 'center',
    alignItems: 'center',
  },
  alignTitleLeft: {
    marginHorizontal: Metrics.doubleBaseMargin,
    alignSelf: 'flex-start',
    justifyContent: 'center',
    alignItems: 'center',
  },
  btnWrapperprofile: {
    padding: Metrics.smallMargin,
    justifyContent: 'center',
  },
  rightBtn: {
    alignItems: 'flex-end',
  },
  searchHeader: {
    height: Metrics.navBarHeight + 50,
  },
  titleImageContainer: {
    marginLeft: Metrics.ratio(20, 20),
    position: 'absolute',
    left: 0,
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
  },
  rightContainer: {
    marginRight: Metrics.ratio(24, 24),
    right: 0,
    position: 'absolute',
    zIndex: 999,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
});
