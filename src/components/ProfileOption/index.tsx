import React from 'react';
import {View, Text, Image, Pressable} from 'react-native';
import styles from './styles';
import {IProfileOptionObject} from '../../types';
import {PROFILE} from '../../constants/StringConstants';

interface ProfileOptionProps {
  data: IProfileOptionObject[];
  onPress: (data: IProfileOptionObject) => void;
}

const ProfileOption: React.FC<ProfileOptionProps> = ({data, onPress}) => {
  return (
    <View style={styles.mainContainer}>
      {data.map((item, index) => (
        <View key={index} style={{flexDirection: 'column'}}>
          <Pressable
            onPress={() => onPress(item)}
            key={index}
            style={[
              styles.container,
              {
                borderTopLeftRadius: index === 0 ? 21 : 0,
                borderTopRightRadius: index === 0 ? 21 : 0,
                borderBottomLeftRadius: index === data.length - 1 ? 21 : 0,
                borderBottomRightRadius: index === data.length - 1 ? 21 : 0,
              },
            ]}>
            {typeof item.leftImage === 'string' ? (
              <Image source={{uri: item.leftImage}} style={styles.leftImage} />
            ) : item.leftImage ? (
              <View
                style={[
                  item.name === PROFILE.ABOUT_IMPRINT
                    ? styles.leftImageForAbout
                    : styles.leftImage,
                ]}>
                {item.leftImage}
              </View>
            ) : null}
            <View style={styles.info}>
              <Text
                style={[
                  styles.name,
                  {textAlign: item.name === PROFILE.LOGOUT ? 'center' : 'left'},
                ]}>
                {item.name}
              </Text>
              {item.description === '' ? null : (
                <Text style={styles.description}>{item.description}</Text>
              )}
            </View>
            {item.rightImage === null ? (
              <></>
            ) : (
              <View style={styles.rightImage}>{item.rightImage}</View>
            )}
          </Pressable>
          {index === data.length - 1 ? null : (
            <View style={styles.seperator}></View>
          )}
        </View>
      ))}
    </View>
  );
};

export default ProfileOption;
