import {StyleSheet} from 'react-native';
import {Colors, Fonts, Metrics} from '../../theme';

export default StyleSheet.create({
  mainContainer: {
    marginTop: Metrics.ratio(20),
  },
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: Metrics.ratio(20),
    height: Metrics.ratio(69),
    borderRadius: Metrics.ratio(21),
    backgroundColor: Colors.white,
  },
  leftImage: {
    width: Metrics.ratio(50),
    height: Metrics.ratio(50),
    margin: Metrics.ratio(12),
    borderRadius: 100,
  },
  leftImageForAbout: {
    justifyContent: 'center',
    alignItems: 'center',
    width: Metrics.ratio(35),
    height: Metrics.ratio(35),
    margin: Metrics.ratio(20),
    borderRadius: Metrics.ratio(100),
    backgroundColor: Colors.lightGray1,
  },
  info: {
    flex: 1,
  },
  name: {
    fontFamily: Fonts.type.semi_bold,
    fontSize: Metrics.ratio(16),
    color: Colors.text.titleColor,
    textAlign: 'center',
  },
  description: {
    fontFamily: Fonts.type.medium,
    color: Colors.textLight,
    fontSize: Metrics.ratio(13),
    marginTop: Metrics.ratio(2),
  },
  rightImage: {
    marginRight: Metrics.ratio(14),
  },
  seperator: {
    backgroundColor: Colors.background.publishInactiveColor,
    height: 1,
    marginHorizontal: Metrics.ratio(20),
  },
});
