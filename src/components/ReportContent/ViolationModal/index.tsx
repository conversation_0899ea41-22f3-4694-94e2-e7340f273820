import {View, Pressable, Text, TextInput, Platform} from 'react-native';
import {AppStyles, Colors} from '../../../theme';
import {CROSS_ICON} from '../../../constants/AssetSVGConstants';
import styles from './styles';
import {AppButton} from '../../../components';
import {FlatList} from 'react-native-gesture-handler';
import {connect} from 'react-redux';
import {useEffect, useState} from 'react';
import {
  IViolationContent,
  IViolationResponse,
  REPORT_ENUMS,
} from '../../../types';
import {showToastMsg} from '../../Alert';
import {
  NEXT_OF_KIN,
  REPORT_CONTENT,
  TOAST_MESSAGES,
  TOAST_TYPE,
} from '../../../constants/StringConstants';
import _ from 'lodash';
import {getViolationPolicies} from '../../../actions/TimelineActions';

interface ViolationModalProps {
  ageGroup: string;
  crossClicked: () => void;
  handleSubmitClick: (selectedViolations: {policy: string}[]) => void;
  getViolationPolicies: (
    payload: any,
    callback: (res: IViolationResponse) => void,
  ) => void;
}

const ViolationModal: React.FC<ViolationModalProps> = ({
  crossClicked,
  handleSubmitClick,
  getViolationPolicies,
  ageGroup = 'under20',
}) => {
  const [showFlatList, setShowFlatList] = useState(false);

  const [selectedViolations, setSelectedViolations] = useState<
    IViolationContent[]
  >([]);
  const [inputText, setInputText] = useState('');
  const [violationList, setViolationList] = useState();

  const handleSelectViolation = (item: IViolationContent) => {
    let updatedViolations = [...selectedViolations];
    if (updatedViolations.length === 3) {
      showToastMsg(TOAST_MESSAGES.VIOLATION_ERROR, TOAST_TYPE.DEFAULT);
      return;
    }

    const isAlreadySelected = updatedViolations.some(v => v.code === item.code);
    if (isAlreadySelected) {
      updatedViolations = updatedViolations.filter(v => v.code !== item.code);
    } else {
      if (updatedViolations.length < 3) {
        updatedViolations.push(item);
      }
    }

    setSelectedViolations(updatedViolations);
    setInputText(updatedViolations.map(v => v.name).join(', '));
  };

  const onCrossPress = (title: string) => {
    const updatedViolations = selectedViolations.filter(
      item => item.name !== title,
    );
    setSelectedViolations(updatedViolations);
    setInputText(updatedViolations.map(v => v.name).join(', '));
  };

  const modifySubmitClick = () => {
    const formattedViolations = selectedViolations.map(v => ({policy: v.code}));
    handleSubmitClick(formattedViolations);
  };

  useEffect(() => {
    const payload = {
      type: REPORT_ENUMS.NEGATIVE,
    };
    getViolationPolicies(payload, (res: IViolationResponse) => {
      if (res) {
        if (Array.isArray(res?.categories)) {
          setViolationList(
            ageGroup === REPORT_ENUMS.UNDER18
              ? res.categories[1]?.policies
              : res.categories[0]?.policies,
          );
        }
      }
    });
  }, []);

  const renderListContainer = () => {
    return (
      <View style={styles.listContainer}>
        <FlatList
          style={AppStyles.marginVerticalBase}
          data={violationList}
          renderItem={({item}: {item: IViolationContent}) => {
            const isSelected = selectedViolations.some(
              v => v.code === item.code,
            );
            return (
              <Pressable
                style={[
                  styles.symbol,
                  isSelected ? styles.grayColor : styles.whiteColor,
                ]}
                onPress={() => handleSelectViolation(item)}>
                <View style={styles.itemContainer}>
                  <Text>{item.name.split(' ')[0]} </Text>
                  <View style={styles.logoTextContainer}>
                    <Text style={styles.itemText}>
                      {item.name.substring(item.name.indexOf(' ') + 1)}
                    </Text>

                    {selectedViolations.some(v => v.code === item.code) && (
                      <Pressable
                        style={styles.cross}
                        onPress={() => onCrossPress(item.name)}>
                        <CROSS_ICON width={10} height={10} />
                      </Pressable>
                    )}
                  </View>
                </View>
              </Pressable>
            );
          }}
        />
      </View>
    );
  };

  return (
    <View style={styles.mainContainer}>
      <View style={styles.container}>
        <View style={styles.titleContainer}>
          <Text style={styles.title}>{REPORT_CONTENT.VIOLATION_HEADING}</Text>

          <Pressable style={styles.cross} onPress={() => crossClicked()}>
            <CROSS_ICON width={12} height={12} />
          </Pressable>
        </View>
        <Text style={styles.subTitle}>
          {REPORT_CONTENT.VIOLATION_SUBHEADING}
        </Text>
        <Pressable
          onPress={() =>
            Platform.OS === 'android'
              ? setShowFlatList(!showFlatList)
              : undefined
          }>
          <TextInput
            style={styles.dropDown}
            placeholder={REPORT_CONTENT.PLACE_HOLDER}
            placeholderTextColor={Colors.black}
            multiline={true}
            value={inputText}
            onFocus={() => setShowFlatList(true)}
            onPressIn={() => setShowFlatList(!showFlatList)}
            editable={false}
          />
        </Pressable>
        {showFlatList && renderListContainer()}

        <View style={styles.buttonContainer}>
          <AppButton
            buttonStye={styles.retakeButton}
            text={'Cancel'}
            onPress={() => crossClicked()}
          />
          <AppButton
            disabled={selectedViolations.length === 0}
            buttonStye={[
              styles.nextButton,
              {
                backgroundColor:
                  selectedViolations.length === 0
                    ? Colors.lightGray
                    : Colors.BlackButton,
              },
            ]}
            text={NEXT_OF_KIN.SUBMIT}
            textColor={Colors.white}
            onPress={modifySubmitClick}
          />
        </View>
      </View>
    </View>
  );
};

const mapStateToProps = (state: any) => ({user: state.user});

export default connect(mapStateToProps, {getViolationPolicies})(ViolationModal);
