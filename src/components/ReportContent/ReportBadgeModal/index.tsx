import {View, Pressable, Text, TextInput, Platform} from 'react-native';
import {AppStyles, Colors} from '../../../theme';
import {CROSS_ICON} from '../../../constants/AssetSVGConstants';
import styles from './styles';
import {AppButton} from '../../../components';
import {FlatList} from 'react-native-gesture-handler';
import {connect} from 'react-redux';
import {useEffect, useState} from 'react';
import {
  IViolationContent,
  IViolationResponse,
  REPORT_ENUMS,
} from '../../../types';
import {showToastMsg} from '../../Alert';
import {
  NEXT_OF_KIN,
  REPORT_CONTENT,
  TOAST_MESSAGES,
  TOAST_TYPE,
} from '../../../constants/StringConstants';
import _ from 'lodash';
import {getViolationPolicies} from '../../../actions/TimelineActions';

interface ReportBadgeModalProps {
  crossClicked: () => void;
  handleSubmitClick: (selectedViolations: {policy: string}[]) => void;
  getViolationPolicies: (
    payload: any,
    callback: (res: IViolationResponse) => void,
  ) => void;
}

const ReportBadgeModal: React.FC<ReportBadgeModalProps> = ({
  crossClicked,
  handleSubmitClick,
  getViolationPolicies,
}) => {
  const [showFlatList, setShowFlatList] = useState(false);

  const [selectedViolations, setSelectedViolations] = useState<
    IViolationContent[]
  >([]);
  const [inputText, setInputText] = useState('');
  const [violationList, setViolationList] = useState();

  const handleSelectViolation = (item: IViolationContent) => {
    let updatedViolations = [...selectedViolations];
    if (updatedViolations.length === 2) {
      showToastMsg(TOAST_MESSAGES.BADGE_ERROR, TOAST_TYPE.DEFAULT);
      return;
    }

    const isAlreadySelected = updatedViolations.some(v => v.code === item.code);
    if (isAlreadySelected) {
      updatedViolations = updatedViolations.filter(v => v.code !== item.code);
    } else {
      if (updatedViolations.length < 2) {
        updatedViolations.push(item);
      }
    }

    setSelectedViolations(updatedViolations);
    setInputText(updatedViolations.map(v => v.name).join(', '));
  };

  const onCrossPress = (title: string) => {
    const updatedViolations = selectedViolations.filter(
      item => item.name !== title,
    );
    setSelectedViolations(updatedViolations);
    setInputText(updatedViolations.map(v => v.name).join(', '));
  };

  const modifySubmitClick = () => {
    const formattedViolations = selectedViolations.map(v => ({policy: v.code}));
    handleSubmitClick(formattedViolations);
  };

  useEffect(() => {
    const payload = {
      type: REPORT_ENUMS.POSITIVE,
    };
    getViolationPolicies(payload, (res: IViolationResponse) => {
      if (res) {
        if (Array.isArray(res?.categories)) {
          setViolationList(res.categories[0]?.policies);
        }
      }
    });
  }, []);

  return (
    <View style={styles.mainContainer}>
      <View style={styles.container}>
        <View style={styles.titleContainer}>
          <Text style={styles.title}>{REPORT_CONTENT.VALUE_BADGE_HEADING}</Text>

          <Pressable style={styles.cross} onPress={() => crossClicked()}>
            <CROSS_ICON width={12} height={12} />
          </Pressable>
        </View>
        <Text style={styles.subHeading}>
          {REPORT_CONTENT.VALUE_BADGE_SUB_HEADING}
        </Text>
        <Pressable
          onPress={() =>
            Platform.OS === 'android'
              ? setShowFlatList(!showFlatList)
              : undefined
          }>
          <TextInput
            style={styles.selectorContainer}
            placeholder={REPORT_CONTENT.BADGE_PLACEHOLDER}
            placeholderTextColor={Colors.black}
            multiline={true}
            value={inputText}
            onFocus={() => setShowFlatList(true)}
            editable={false}
            onPressIn={() => setShowFlatList(!showFlatList)}
          />
        </Pressable>
        {showFlatList && (
          <View style={styles.listContainer}>
            <FlatList
              style={AppStyles.marginVerticalBase}
              data={violationList}
              renderItem={({item}: {item: IViolationContent}) => {
                const isSelected = selectedViolations.some(
                  v => v.code === item.code,
                );
                return (
                  <Pressable
                    style={[
                      styles.symbol,
                      isSelected ? styles.grayColor : styles.white,
                    ]}
                    onPress={() => handleSelectViolation(item)}>
                    <View style={styles.logoTextContainer}>
                      <Text style={styles.logoText}>{item.name}</Text>
                      {selectedViolations.some(v => v.code === item.code) && (
                        <Pressable
                          style={styles.cross}
                          onPress={() => onCrossPress(item.name)}>
                          <CROSS_ICON width={10} height={10} />
                        </Pressable>
                      )}
                    </View>
                  </Pressable>
                );
              }}
            />
          </View>
        )}
        <View style={styles.buttonContainer}>
          <AppButton
            buttonStye={styles.retakeButton}
            text={'Cancel'}
            onPress={() => crossClicked()}
          />
          <AppButton
            disabled={selectedViolations.length === 0}
            buttonStye={[
              styles.nextButton,
              {
                backgroundColor:
                  selectedViolations.length === 0
                    ? Colors.lightGray
                    : Colors.BlackButton,
              },
            ]}
            text={NEXT_OF_KIN.SUBMIT}
            textColor={Colors.white}
            onPress={modifySubmitClick}
          />
        </View>
      </View>
    </View>
  );
};

const mapStateToProps = (state: any) => ({user: state.user});

export default connect(mapStateToProps, {getViolationPolicies})(
  ReportBadgeModal,
);
