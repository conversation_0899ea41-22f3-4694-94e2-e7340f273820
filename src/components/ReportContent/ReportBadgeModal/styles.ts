import {StyleSheet} from 'react-native';
import {Colors, Metrics, Fonts} from '../../../theme';

// @flow

export default StyleSheet.create({
  mainContainer: {
    height: Metrics.ratio(Metrics.screenHeight),
    width: Metrics.ratio(Metrics.screenWidth),
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  container: {
    flex: 1,
    width: Metrics.ratio(Metrics.screenWidth - 20),
    backgroundColor: 'white',
    position: 'absolute',
    borderRadius: Metrics.ratio(10),
  },
  titleContainer: {
    flexDirection: 'row',
    borderTopLeftRadius: Metrics.ratio(10),
    borderTopRightRadius: Metrics.ratio(10),
    backgroundColor: Colors.lightGray,
    height: Metrics.ratio(50),
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  title: {
    fontFamily: Fonts.type.medium,
    fontSize: Fonts.size.normal,
    color: Colors.black,
    paddingLeft: Metrics.ratio(20),
  },
  buttonContainer: {
    flex: 1,
    bottom: 0,
    marginVertical: Metrics.ratio(20),
    width: Metrics.ratio(Metrics.screenWidth - 60),
    flexDirection: 'row',
    alignSelf: 'center',
    justifyContent: 'space-evenly',
    alignItems: 'flex-end',
  },
  retakeButton: {
    width: Metrics.ratio(140),
    backgroundColor: Colors.gray,
    borderColor: Colors.black,
    borderWidth: Metrics.ratio(1),
    borderRadius: Metrics.ratio(6),
    marginHorizontal: Metrics.ratio(0),
  },
  nextButton: {
    marginHorizontal: Metrics.ratio(0),
    width: Metrics.ratio(140),
  },
  cross: {
    top: 0,
    alignItems: 'flex-end',
    //  flex: 1,
    paddingRight: 20,
  },
  symbol: {
    flexDirection: 'row',
    marginVertical: Metrics.ratio(8),
    paddingVertical: Metrics.ratio(5),
    paddingHorizontal: Metrics.ratio(10),
    marginHorizontal: Metrics.ratio(5),
    borderRadius: Metrics.ratio(6),
    backgroundColor: Colors.symbolBackground,
  },
  logoTextContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  logoText: {
    flex: 1,
    paddingLeft: Metrics.ratio(10),
    marginRight: Metrics.ratio(10),
  },
  selectorContainer: {
    borderWidth: Metrics.ratio(1),
    borderRadius: Metrics.ratio(16),
    padding: Metrics.ratio(12),
    borderColor: Colors.gray4,
    backgroundColor: Colors.white,
    fontFamily: Fonts.type.regular,
    color: Colors.text.modalText,
    marginTop: Metrics.ratio(12),
    paddingTop: Metrics.ratio(12),
    marginHorizontal: Metrics.ratio(20),
  },
  listContainer: {
    height: Metrics.ratio(140),
    borderColor: Colors.gray4,
    marginTop: Metrics.ratio(2),
    borderWidth: Metrics.ratio(1),
    borderRadius: Metrics.ratio(16),
    zIndex: Metrics.ratio(1000),
    marginHorizontal: Metrics.ratio(20),
  },
  grayColor: {backgroundColor: Colors.lightGray},
  white: {backgroundColor: 'white'},
  subHeading: {
    fontFamily: Fonts.type.semi_bold,
    alignSelf: 'center',
    margin: Metrics.ratio(20),
  },
});
