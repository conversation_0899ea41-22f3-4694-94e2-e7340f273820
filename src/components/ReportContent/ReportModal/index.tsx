import {View, Pressable, TouchableOpacity, Text} from 'react-native';
import {AppStyles, Colors} from '../../../theme';
import {
  CROSS_ICON,
  DETAILED,
  TICK_BLUE,
  TICK_ICON_NEXT,
} from '../../../constants/AssetSVGConstants';
import styles from './styles';
import {AppButton} from '../../../components';
import {connect} from 'react-redux';
import {useState} from 'react';
import {REPORT_CONTENT} from '../../../constants/StringConstants';
import _ from 'lodash';
import {REPORT_ENUMS} from '../../../types';

interface NextOfKinProps {
  reportType: string;
  crossClicked: () => void;
  handleSubmitClick: (ageGroup: string, option: string) => void;
  onlyForUser?: boolean;
}

const ReportModal: React.FC<NextOfKinProps> = ({
  reportType,
  crossClicked,
  handleSubmitClick,
  onlyForUser = false,
}) => {
  const [selectedOption, setSelectedOption] = useState(
    onlyForUser ? REPORT_ENUMS.PROFILE : '',
  );
  const [selectedAgeGroup, setSelectedAgeGroup] = useState('');
  const [mentalHealthConcerned, setMentalHealthConcerned] =
    useState<boolean>(false);

  const renderFirstOptionContainer = () => {
    return (
      <View style={styles.optionsContainer}>
        <TouchableOpacity
          style={styles.profileSelector}
          onPress={() => setSelectedOption(REPORT_ENUMS.PROFILE)}>
          {selectedOption === REPORT_ENUMS.PROFILE ? (
            <View style={AppStyles.mRight10}>
              <TICK_BLUE width={15} height={15} />
            </View>
          ) : (
            <View style={AppStyles.mRight10}>
              <TICK_ICON_NEXT width={15} height={15} />
            </View>
          )}
          <Text style={styles.optionsText}>{REPORT_CONTENT.USER}</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={AppStyles.flexRow}
          onPress={() => setSelectedOption(REPORT_ENUMS.POST)}>
          {selectedOption === REPORT_ENUMS.POST ? (
            <View style={AppStyles.mLeft30}>
              <TICK_BLUE width={15} height={15} />
            </View>
          ) : (
            <View style={AppStyles.mLeft30}>
              <TICK_ICON_NEXT width={15} height={15} />
            </View>
          )}
          <Text style={styles.optionsText}>{reportType}</Text>
        </TouchableOpacity>
      </View>
    );
  };

  const renderSecondOptionContainer = () => {
    return (
      <View style={[AppStyles.flexRow, styles.overrideMargin]}>
        <TouchableOpacity
          style={[styles.optionsContainer]}
          onPress={() => setSelectedAgeGroup(REPORT_ENUMS.UNDER18)}>
          {selectedAgeGroup === REPORT_ENUMS.UNDER18 ? (
            <TICK_BLUE width={15} height={15} />
          ) : (
            <TICK_ICON_NEXT width={15} height={15} />
          )}
          <Text style={styles.optionsText}>{REPORT_CONTENT.UNDER18}</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[AppStyles.flexRow, AppStyles.mLeft20]}
          onPress={() => setSelectedAgeGroup(REPORT_ENUMS.ALL)}>
          {selectedAgeGroup === REPORT_ENUMS.ALL ? (
            <TICK_BLUE width={15} height={15} />
          ) : (
            <TICK_ICON_NEXT width={15} height={15} />
          )}
          <Text style={styles.optionsText}>{REPORT_CONTENT.ALL}</Text>
        </TouchableOpacity>
      </View>
    );
  };
  const renderThirdOptionContainer = () => {
    return (
      <View style={[AppStyles.flexRow, AppStyles.mTop10, AppStyles.mRight30]}>
        <TouchableOpacity
          style={styles.optionsContainer}
          onPress={() => setMentalHealthConcerned(!mentalHealthConcerned)}>
          {mentalHealthConcerned ? (
            <TICK_BLUE width={15} height={15} />
          ) : (
            <TICK_ICON_NEXT width={15} height={15} />
          )}
          <Text style={styles.optionsText} numberOfLines={2}>
            {REPORT_CONTENT.MENTAL_HEALTH_CONCERNED}
          </Text>
        </TouchableOpacity>
      </View>
    );
  };

  return (
    <View style={styles.mainContainer}>
      <View style={styles.container}>
        <View style={styles.titleContainer}>
          <Text style={styles.title}>{REPORT_CONTENT.REPORT_CONTENT}</Text>

          <Pressable style={styles.cross} onPress={() => crossClicked()}>
            <CROSS_ICON width={12} height={12} />
          </Pressable>
        </View>
        {!onlyForUser && (
          <Text style={styles.subTitle}>
            {REPORT_CONTENT.REPORT_CONTENT_SUBTITLE}
          </Text>
        )}
        <View style={styles.nextSubtitle}>
          {!onlyForUser && (
            <>
              <Text style={styles.subtitleText}>
                {REPORT_CONTENT.REPORT_CONTENT_SUBTITLE_NEXT}
                <Text style={styles.red}>*</Text>
              </Text>
              {renderFirstOptionContainer()}
            </>
          )}
          <Text style={[styles.subtitleText, onlyForUser && AppStyles.mTop10]}>
            {REPORT_CONTENT.AGE_GROUP}
            <Text style={styles.red}>*</Text>
          </Text>

          {renderSecondOptionContainer()}
          {renderThirdOptionContainer()}
        </View>

        <View style={styles.footerContainer}>
          <DETAILED height={15} width={15} />
          <Text style={styles.footerText}>{REPORT_CONTENT.FOOTER_TEXT}</Text>
        </View>
        <View style={styles.buttonContainer}>
          <AppButton
            buttonStye={styles.retakeButton}
            text={'Cancel'}
            size={'xxSmall'}
            onPress={() => crossClicked()}
          />
          <AppButton
            disabled={_.isEmpty(selectedOption) || _.isEmpty(selectedAgeGroup)}
            buttonStye={[
              styles.nextButton,
              {
                backgroundColor:
                  _.isEmpty(selectedOption) || _.isEmpty(selectedAgeGroup)
                    ? Colors.lightGray
                    : Colors.BlackButton,
              },
            ]}
            text={REPORT_CONTENT.NEXT}
            size={'xxSmall'}
            textColor={Colors.white}
            onPress={() => handleSubmitClick(selectedAgeGroup, selectedOption)}
          />
        </View>
      </View>
    </View>
  );
};

const mapStateToProps = (state: any) => ({user: state.user});

export default connect(mapStateToProps, {})(ReportModal);
