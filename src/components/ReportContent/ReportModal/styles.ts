import {StyleSheet} from 'react-native';
import {Colors, Metrics, Fonts} from '../../../theme';

// @flow

export default StyleSheet.create({
  mainContainer: {
    height: Metrics.ratio(Metrics.screenHeight),
    width: Metrics.ratio(Metrics.screenWidth),
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  container: {
    alignSelf: 'center',
    width: Metrics.ratio(Metrics.screenWidth - 20),
    backgroundColor: 'white',
    position: 'absolute',
    borderRadius: Metrics.ratio(10),
  },
  titleContainer: {
    flexDirection: 'row',
    borderTopLeftRadius: Metrics.ratio(10),
    borderTopRightRadius: Metrics.ratio(10),
    backgroundColor: Colors.lightGray,
    height: Metrics.ratio(50),
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  title: {
    fontFamily: Fonts.type.medium,
    fontSize: Fonts.size.normal,
    color: Colors.black,
    paddingLeft: Metrics.ratio(20),
  },
  subTitle: {
    fontFamily: Fonts.type.semi_bold,
    margin: Metrics.ratio(20),
  },
  nextSubtitle: {
    marginLeft: Metrics.ratio(20),
  },
  buttonContainer: {
    // flex: 1,
    bottom: 0,
    marginVertical: Metrics.ratio(10),
    width: Metrics.ratio(Metrics.screenWidth - 60),
    flexDirection: 'row',
    alignSelf: 'center',
    justifyContent: 'space-evenly',
    alignItems: 'flex-end',
  },
  retakeButton: {
    width: Metrics.ratio(140),
    backgroundColor: Colors.gray,
    borderColor: Colors.black,
    borderWidth: Metrics.ratio(1),
    borderRadius: Metrics.ratio(6),
    marginHorizontal: Metrics.ratio(0),
  },
  nextButton: {
    marginHorizontal: Metrics.ratio(0),
    width: Metrics.ratio(140),
  },
  cross: {
    top: 0,
    alignItems: 'flex-end',
    flex: 1,
    paddingRight: 20,
  },

  safetyContainer: {
    flexDirection: 'row',
    paddingHorizontal: Metrics.ratio(20),
  },
  optionsContainer: {
    flexDirection: 'row',
    marginBottom: Metrics.ratio(10),
  },
  profileSelector: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  footerContainer: {
    marginHorizontal: Metrics.ratio(20),
    borderLeftWidth: 4, // Vertical line width
    borderLeftColor: 'orange', // Vertical line color
    marginVertical: Metrics.ratio(20),
    bottom: 0,
    flexDirection: 'row',
    backgroundColor: Colors.gray3,
    alignItems: 'center',
    paddingLeft: Metrics.ratio(10),
    paddingVertical: Metrics.ratio(10),
    paddingRight: Metrics.ratio(20),
  },
  footerText: {
    marginLeft: Metrics.ratio(20),
    fontSize: Fonts.size.xxSmall,
  },
  red: {
    color: Colors.red,
  },
  subtitleText: {
    marginBottom: Metrics.ratio(10),
    fontFamily: Fonts.type.semi_bold,
  },
  overrideMargin: {marginVertical: Metrics.ratio(10)},
  optionsText: {marginLeft: Metrics.ratio(5), fontSize: Fonts.size.xxSmall},
});
