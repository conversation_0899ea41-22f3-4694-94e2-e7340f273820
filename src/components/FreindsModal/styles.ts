import {StyleSheet} from 'react-native';
import {Colors, Metrics} from '../../theme';

export default StyleSheet.create({
  modalParentContainer: {
    marginTop: 0,
  },
  heading: {
    marginHorizontal: Metrics.ratio(20),
    marginTop: Metrics.baseMargin,
  },

  button: {
    backgroundColor: Colors.background.publishInactiveColor,
    paddingHorizontal: Metrics.baseMargin,
    paddingVertical: Metrics.smallMargin,
    borderRadius: Metrics.ratio(6, 6),
  },
  selectedColor: {backgroundColor: Colors.background.publishActiveColor},
  modal: {
    backgroundColor: Colors.gray,
    height: '100%',
    width: Metrics.screenWidth,
    alignSelf: 'center',
  },
  header: {
    marginHorizontal: Metrics.baseMargin,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  listContainer: {height: '80%'},
});
