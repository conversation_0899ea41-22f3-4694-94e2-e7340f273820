import React, {useEffect, useState} from 'react';
import {View} from 'react-native';
import {FRIENDS} from '../../constants/StringConstants';
import {CROSS_ROUND} from '../../constants/AssetSVGConstants';
import styles from './styles';
import {
  Text,
  ButtonView,
  SuggestedFriendItem,
  EmptyStateComponent,
  SearchBar,
} from '..';
import Modal from 'react-native-modal';
import {FlatList} from 'react-native-gesture-handler';
import {AppStyles, Colors, Metrics} from '../../theme';
import _ from 'lodash';
import {User, UserInfo} from '../../types';
import {SafeAreaView} from 'react-native-safe-area-context';

interface MoreOptionsBottomModalProps {
  visible: boolean;
  hideModal: () => void;
  friendsData: any;
  onSubmit: (selectedItems: UserInfo[]) => void;
}

const FriendsModal: React.FC<MoreOptionsBottomModalProps> = ({
  visible,
  hideModal,
  friendsData,
  onSubmit,
}) => {
  const [selectedItems, setSelectedItems] = useState<any[]>([]);
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [filteredFriends, setFilteredFriends] = useState(friendsData);

  const renderItem = ({item}: {item: User}) => {
    return (
      <SuggestedFriendItem
        item={item}
        onItemPress={() => handleItemClick(item)}
        selectedItems={selectedItems.map(selected => selected.id)}
      />
    );
  };
  useEffect(() => {
    setFilteredFriends(friendsData);
  }, [visible]);

  const handleSearch = (text: string) => {
    setSearchQuery(text);
    const OriginalFriendsData = _.cloneDeep(friendsData);

    if (OriginalFriendsData) {
      const searchData = OriginalFriendsData.filter(
        (item: {displayName: string}) =>
          item.displayName.toLowerCase().includes(text.toLowerCase()),
      );
      setFilteredFriends(searchData);
    }
  };
  const handleItemClick = (item: ItemProps['item']) => {
    const selectedIndex = selectedItems.findIndex(
      selected => selected.id === item.id,
    );
    let newSelectedItems = [...selectedItems];

    if (selectedIndex === -1) {
      newSelectedItems.push(item);
    } else {
      newSelectedItems.splice(selectedIndex, 1);
    }

    setSelectedItems(newSelectedItems);
  };
  const handleSubmit = () => {
    onSubmit(selectedItems);
    hideModal();
  };
  const onCancelPress = () => {
    hideModal();
  };

  return (
    <Modal isVisible={visible} onDismiss={hideModal} style={styles.modal}>
      <SafeAreaView style={styles.header}>
        <ButtonView onPress={onCancelPress}>
          <CROSS_ROUND />
        </ButtonView>
        <ButtonView
          onPress={handleSubmit}
          style={[styles.button, styles.selectedColor]}>
          <Text color={Colors.white} size={'buttonText'}>
            {FRIENDS.CONFIRM}
          </Text>
        </ButtonView>
      </SafeAreaView>
      <SearchBar
        autoFocus={false}
        containerStyle={AppStyles.mTop20}
        onSearchText={text => handleSearch(text)}
        value={searchQuery}
      />
      <View style={styles.listContainer}>
        {friendsData.length > 0 && (
          <Text
            color={Colors.black}
            type="semi_bold"
            size={'normal'}
            style={styles.heading}>
            {FRIENDS.PERSONAL_FRIENDS}
          </Text>
        )}

        <FlatList
          ListEmptyComponent={<EmptyStateComponent />}
          data={filteredFriends}
          renderItem={renderItem}
          showsVerticalScrollIndicator={false}
        />
      </View>
    </Modal>
  );
};

export default FriendsModal;
