import React, {useState} from 'react';
import {Pressable, Text, View, Image} from 'react-native';
import styles from './styles';
import {MINUS_ICON, PLUS_ICON} from '../../../constants/AssetSVGConstants';
import {ImprintMedia} from '../../../types';
import {easeInEaseOut} from '../../../util';
import {IMPRINT_TIMELINE} from '../../../constants/StringConstants';

interface IPostText {
  articleData: ImprintMedia[];
  title?: string;
  onPressArticle: (article: ImprintMedia) => void;
}

const RelatedArticle: React.FC<IPostText> = ({
  articleData,
  title,
  onPressArticle,
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const renderArticle = () => {
    return (
      <View style={styles.articleContainer}>
        {articleData.map((article, index) => (
          <View key={index} style={styles.container}>
            <Pressable
              onPress={() => onPressArticle(article)}
              style={{flex: 1}}>
              <Image
                source={{
                  uri: Image.resolveAssetSource(
                    require('../../../assets/icons/attachment.png'),
                  ).uri,
                }}
                defaultSource={require('../../../assets/icons/attachment.png')}
                style={styles.articleImage}
                resizeMode="center"
              />
            </Pressable>
            <View style={styles.textContainer}>
              <Text style={styles.articleTitle} numberOfLines={2}>
                {article.name}
              </Text>
              <Text style={styles.articleDescription} numberOfLines={2}>
                {article.description}
              </Text>
            </View>
          </View>
        ))}
      </View>
    );
  };

  const expandArticle = () => {
    //  easeInEaseOut(); comment due to causing issue in android
    setIsExpanded(!isExpanded);
  };

  return (
    <View style={styles.container}>
      <Pressable onPress={() => expandArticle()}>
        <View style={styles.innerContainer}>
          <Text style={styles.relatedArticleText}>
            {title ? title : IMPRINT_TIMELINE.RELATED_ARTICLES}
          </Text>
          <View style={styles.imageContainer}>
            {isExpanded ? <MINUS_ICON /> : <PLUS_ICON />}
          </View>
        </View>
      </Pressable>
      {isExpanded ? renderArticle() : null}
    </View>
  );
};

export default RelatedArticle;
