import {StyleSheet} from 'react-native';
import {Colors, Fonts, Metrics} from '../../../theme';

export default StyleSheet.create({
  container: {
    paddingHorizontal: Metrics.ratio(24),
    paddingBottom: Metrics.ratio(5),
    paddingTop: Metrics.ratio(18),
  },
  innerContainer: {
    flexDirection: 'row',
    height: Metrics.ratio(57),
    borderWidth: Metrics.ratio(1),
    borderColor: Colors.articleBorder,
    backgroundColor: Colors.background.article,
    borderRadius: Metrics.ratio(4),
    alignItems: 'center',
  },
  relatedArticleText: {
    fontFamily: Fonts.type.semi_bold,
    fontSize: Metrics.ratio(16),
    color: Colors.black,
    paddingLeft: Metrics.ratio(12),
  },
  imageContainer: {
    position: 'absolute',
    right: Metrics.ratio(16),
  },
  articleContainer: {
    paddingHorizontal: Metrics.ratio(12),
    backgroundColor: Colors.background.article,
  },
  articleTitle: {
    fontFamily: Fonts.type.semi_bold,
    fontSize: Metrics.ratio(14),
    color: Colors.text.titleColor,
    paddingHorizontal: Metrics.ratio(12),
    paddingTop: Metrics.ratio(12),
    paddingBottom: Metrics.ratio(5),
  },
  articleDescription: {
    fontFamily: Fonts.type.regular,
    fontSize: Metrics.ratio(14),
    color: Colors.textLight,
    paddingHorizontal: Metrics.ratio(12),
  },
  textContainer: {
    backgroundColor: Colors.white,
    paddingBottom: Metrics.ratio(10),
    borderBottomLeftRadius: Metrics.ratio(8),
    borderBottomRightRadius: Metrics.ratio(8),
    marginBottom: Metrics.ratio(24),
  },
  articleImage: {
    flex: 1,
    height: Metrics.ratio(200),
    backgroundColor: Colors.white,
    borderTopLeftRadius: Metrics.ratio(8),
    borderTopRightRadius: Metrics.ratio(8),
  },
});
