import {View} from 'react-native';
import styles from './styles';
import UserDetails from '../UserDetails';
import PostText from '../PostText';
import Collage from '../Collage';
import {
  ITimeLine,
  ImprintMedia,
  UserImprintInteractions,
  UserState,
} from '../../../types';
import Reactions from '../Reactions';
import ReactionDetail from '../ReactionDetail';
import RelatedArticle from '../RelatedArticle';
import {
  IMPRINT_MEDIA,
  IMPRINT_TIMELINE,
} from '../../../constants/StringConstants';
import {Colors} from '../../../theme';
import AudioPlayer from '../../CreateImprint/AudioPlayer';
import AudioRecorderPlayer from 'react-native-audio-recorder-player';
import React, {useEffect, useState} from 'react';

const audioRecorderPlayer = new AudioRecorderPlayer();

interface TimeLineCardProps {
  item: ITimeLine;
  handleReactionClicked: (
    userImprintIntereaction?: UserImprintInteractions,
    reaction?: string,
    item?: ITimeLine,
  ) => void;
  handleMediaClicked?: (item?: ITimeLine) => void;
  handleVerifyClick: (imprint?: ITimeLine) => void;
  handleMoreOptionClick: (imprint?: ITimeLine) => void;
  onPressArticle: (article: ImprintMedia) => void;
  onUserNameClick?: (userName?: string) => void;
  isGlobal?: boolean;
  user: UserState;
  ListEmptyComponent?:
    | React.ComponentType<any>
    | React.ReactElement
    | null
    | undefined;
}

const TimeLineCard: React.FC<TimeLineCardProps> = ({
  item,
  handleReactionClicked,
  handleMediaClicked,
  handleVerifyClick,
  handleMoreOptionClick,
  onPressArticle,
  onUserNameClick,
  isGlobal,
  user,
}) => {
  const articleItemsArray = item?.imprintMedias?.filter(
    item => item.type === IMPRINT_MEDIA.ARTICLE,
  );
  const collageItemsArray = item?.imprintMedias?.filter(
    item =>
      item.type === IMPRINT_MEDIA.VIDEO ||
      item.type === IMPRINT_MEDIA.IMAGE ||
      item.type === IMPRINT_MEDIA.DOCUMENT,
  );

  const audioItem = item?.imprintMedias.filter(
    item => item.type === IMPRINT_MEDIA.AUDIO,
  );
  const [waveData, setWaveData] = useState(
    Array(200).fill({value: Math.random() * 40 + 10, color: '#C4C4C4'}),
  );
  const [playingItemId, setPlayingItemId] = useState<string | null>(null);
  const [isPlaying, setIsPlaying] = useState<boolean>(false);
  const [currentPosition, setCurrentPosition] = useState(0); // Current position in the audio
  const [totalDuration, setTotalDuration] = useState(0); // Total duration of the audio

  const onStartPlay = async (item, id) => {
    if (playingItemId === id) {
      setIsPlaying(!isPlaying);
    } else {
      setPlayingItemId(id);
      setIsPlaying(true);
    }
    await audioRecorderPlayer.startPlayer(item);
    audioRecorderPlayer.setVolume(1.0);
    setActiveAudioId(id);
    audioRecorderPlayer.addPlayBackListener(e => {
      setTotalDuration(e.duration);
      setCurrentPosition(e.currentPosition);
      setWaveData(prevData => {
        return prevData.map(bar => {
          const randomHeight = Math.random() * 30 + 10;
          return {
            ...bar,
            value: randomHeight,
            color: '#4CAF50',
          };
        });
      });
      if (e.isFinished) {
        setWaveData(
          Array(200).fill({value: Math.random() * 40 + 10, color: '#C4C4C4'}),
        );
        onStopPlay();
        setCurrentPosition(0);
      }
      return;
    });
  };

  useEffect(() => {
    return () => {
      // Ensure audio stops when component unmounts
      onStopPlay && onStopPlay();
    };
  }, []);

  const onStopPlay = () => {
    setIsPlaying(false);
    setPlayingItemId(null);
    audioRecorderPlayer.stopPlayer();
    setWaveData(
      Array(200).fill({value: Math.random() * 40 + 10, color: '#C4C4C4'}),
    );
    setCurrentPosition(0);
  };

  const recentArticles = [...articleItemsArray].sort((a, b) => {
    const dateA = a.createdAt ? new Date(a.createdAt) : null;
    const dateB = b.createdAt ? new Date(b.createdAt) : null;
    if (dateA && dateB) {
      return dateB.getTime() - dateA.getTime();
    }
    return 0;
  });
  const [activeAudioId, setActiveAudioId] = useState<string | null>(null);

  return (
    <View style={styles.container}>
      <UserDetails
        handleVerifyClick={handleVerifyClick}
        imprintData={item}
        onMoreOtionClick={handleMoreOptionClick}
        isGlobal={isGlobal}
        onUserNameClicked={onUserNameClick}
        user={user}
      />
      {audioItem && audioItem?.length > 0 && (
        <>
          <AudioPlayer
            onStartPlay={() => onStartPlay(audioItem[0]?.url, item.id)}
            waveData={waveData}
            recordedFilePath={audioItem[0]?.url}
            isPlying={playingItemId === item.id && isPlaying}
            onStopPlay={onStopPlay}
            totalDuration={totalDuration}
            currentPosition={currentPosition}
          />
        </>
      )}
      <PostText text={item?.description} headline={item.headline} />
      {collageItemsArray && collageItemsArray?.length > 0 ? (
        <Collage
          data={collageItemsArray}
          imprintData={item}
          handleMediaClicked={handleMediaClicked}
        />
      ) : null}
      {articleItemsArray && articleItemsArray?.length > 0 ? (
        <View>
          <RelatedArticle
            articleData={articleItemsArray}
            onPressArticle={onPressArticle}
          />
          <RelatedArticle
            onPressArticle={onPressArticle}
            articleData={recentArticles}
            title={IMPRINT_TIMELINE.RECENT_ARTICLES}
          />
        </View>
      ) : null}
      <View style={styles.timelineBottom}>
        <Reactions
          userImprintInteractions={item.userImprintInteractions}
          handleClick={handleReactionClicked}
          imprintData={item}
          user={user}
        />

        <ReactionDetail
          handleVerifyClick={handleVerifyClick}
          imprintData={item}
          userImprintInteractions={item.userImprintInteractions}
          isGlobal={isGlobal}
          user={user}
          isMediaView={false}
        />
      </View>
      {item && item.ageBucket ? (
        <View
          style={[
            styles.ageBracketContainer,
            {
              borderLeftColor:
                item.ageBucket === IMPRINT_TIMELINE.GREEN
                  ? Colors.ageBracketGreen
                  : Colors.ageBracketAmber,
            },
            {
              borderRightColor:
                item.ageBucket === IMPRINT_TIMELINE.GREEN
                  ? Colors.ageBracketGreen
                  : Colors.ageBracketAmber,
            },
          ]}>
          <View
            style={[
              styles.line,
              {
                backgroundColor:
                  item.ageBucket === IMPRINT_TIMELINE.GREEN
                    ? Colors.ageBracketGreen
                    : Colors.ageBracketAmber,
              },
            ]}></View>
        </View>
      ) : (
        <></>
      )}
    </View>
  );
};

export default TimeLineCard;
