import {StyleSheet} from 'react-native';
import {Colors, Metrics} from '../../../theme';

export default StyleSheet.create({
  container: {
    borderRadius: Metrics.ratio(28),
    backgroundColor: Colors.white,
    marginHorizontal: Metrics.ratio(20),
    marginBottom: Metrics.ratio(20),
  },
  ageBracketContainer: {
    borderBottomLeftRadius: Metrics.ratio(28),
    borderBottomRightRadius: Metrics.ratio(28),
    borderRightWidth: Metrics.ratio(8),
    borderLeftWidth: Metrics.ratio(8),
    paddingBottom: Metrics.ratio(20),
  },
  line: {
    height: Metrics.ratio(2),
    marginHorizontal: Metrics.ratio(6),
    top: Metrics.ratio(19.5),
  },
  timelineBottom: {
    alignItems: 'center',
    flexDirection: 'row',
  },
});
