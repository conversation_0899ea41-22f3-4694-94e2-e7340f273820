import {StyleSheet} from 'react-native';
import {Fonts, Metrics, Colors} from '../../../theme';

export default StyleSheet.create({
  container: {
    marginRight: Metrics.ratio(30),
    alignItems: 'center',
    flexDirection: 'row',
    flex: 1,
  },
  imageContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  image: {
    height: Metrics.ratio(32),
    width: Metrics.ratio(32),
    borderRadius: Metrics.ratio(100),
  },
  otherImage: {
    height: Metrics.ratio(36),
    width: Metrics.ratio(36),
    borderRadius: Metrics.ratio(100),
    borderColor: Colors.white,
    borderWidth: Metrics.ratio(3),
  },
  boldText: {
    fontFamily: Fonts.type.base,
    fontSize: Fonts.size.xSmall,
    color: Colors.textGray,
  },
  lightText: {
    fontFamily: Fonts.type.regular,
    fontSize: Metrics.ratio(13),
    color: Colors.textLight,
  },
  textContainer: {
    flex: 0.8,
    marginHorizontal: Metrics.ratio(10),
  },
  verificationIcon: {
    flex: 0.2,
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  emptyTextContainer: {
    flex: 0.8,
  },
});
