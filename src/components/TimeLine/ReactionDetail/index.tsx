import React, {useEffect, useState} from 'react';
import {Pressable, Text, View} from 'react-native';
import styles from './styles';
import {ITimeLine, UserImprintInteractions, UserState} from '../../../types';
import {connect} from 'react-redux';
import {IMPRINT_TIMELINE} from '../../../constants/StringConstants';
import {
  VERIFIED_ICON,
  FAILED_ICON,
  REQUESTED_ICON,
  REQUEST_DENIED_ICON,
  OPEN_REQUEST_ICON,
  UNVERIFIED_ICON,
} from '../../../constants/AssetSVGConstants';

interface IReactionDetail {
  userImprintInteractions: UserImprintInteractions[];
  imprintData?: ITimeLine;
  handleVerifyClick: (imprint?: ITimeLine) => void;
  isMediaView?: boolean;
  isGlobal?: boolean;
  user: UserState;
}

const ReactionDetail: React.FC<IReactionDetail> = ({
  userImprintInteractions,
  user,
  imprintData,
  handleVerifyClick,
  isGlobal,
}) => {
  let updatedUserInteraction = userImprintInteractions.filter(
    item => item.reaction !== null,
  );

  const [userCount, setUserCount] = useState(0);

  useEffect(() => {
    setUserCount(updatedUserInteraction.length);
  }, [updatedUserInteraction]);

  const getDescriptionText = () => {
    if (userCount > 0) {
      return (
        <View style={styles.textContainer}>
          <Text style={styles.boldText}>
            {userCount}{' '}
            {userCount > 1 ? IMPRINT_TIMELINE.PEOPLE : IMPRINT_TIMELINE.PERSON}
            {IMPRINT_TIMELINE.REACTED_}
          </Text>
        </View>
      );
    }
    return <View style={styles.emptyTextContainer} />;
  };

  const getVerification = () => {
    switch (imprintData?.verificationRequest?.verificationStatus) {
      case IMPRINT_TIMELINE.VERIFIED:
        return (
          <View>
            <VERIFIED_ICON />
          </View>
        );
      case IMPRINT_TIMELINE.FAILED:
        return (
          <View>
            <FAILED_ICON />
          </View>
        );
      case IMPRINT_TIMELINE.REQUESTED:
        return (
          <View>
            <REQUESTED_ICON />
          </View>
        );
      case IMPRINT_TIMELINE.REQUEST_DENIED:
        return (
          <View>
            <REQUEST_DENIED_ICON />
          </View>
        );
      default:
        return (
          <View>
            <OPEN_REQUEST_ICON />
          </View>
        );
    }
  };

  return (
    <View style={styles.container}>
      {getDescriptionText()}
      <Pressable
        onPress={() => handleVerifyClick(imprintData)}
        style={styles.verificationIcon}>
        {getVerification()}
      </Pressable>
    </View>
  );
};

const mapStateToProps = (user: UserState) => user;

export default connect(mapStateToProps)(ReactionDetail);
