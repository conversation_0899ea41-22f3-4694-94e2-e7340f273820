import {Pressable, Text, View, Image} from 'react-native';
import styles from './styles';
import React from 'react';
import {
  EARTH_ICON,
  FAILED_ICON,
  LAUGHTER,
  LIFE,
  LIFE_GRAY,
  LOVE,
  LOVE_GRAY,
  OPEN_REQUEST_ICON,
  PURPOSE,
  PURPOSE_GRAY,
  REQUESTED_ICON,
  REQUEST_DENIED_ICON,
  RESPECT,
  RESPECT_GRAY,
  SAFETY,
  SAFETY_GRAY,
  THREE_DOTS,
  UNVERIFIED_ICON,
  VERIFIED_ICON,
} from '../../../constants/AssetSVGConstants';
import util from '../../../util';
import {ITimeLine, UserState} from '../../../types';
import {
  IMPRINT_SCORES,
  IMPRINT_TIMELINE,
} from '../../../constants/StringConstants';
import {Metrics} from '../../../theme';

interface IUserDetailsProps {
  imprintData?: ITimeLine;
  onMoreOtionClick?: (imprint?: ITimeLine) => void;
  handleVerifyClick: (imprint?: ITimeLine) => void;
  onUserNameClicked?: (userName?: string) => void;
  isMediaView?: boolean;
  isGlobal?: boolean;
  user: UserState;
}

const UserDetails: React.FC<IUserDetailsProps> = ({
  imprintData,
  onMoreOtionClick,
  handleVerifyClick,
  onUserNameClicked,
  isMediaView,
  isGlobal,
  user,
}) => {
  const getHighestScore = () => {
    let highestScore = -Infinity;
    let highestKey = null;
    for (const key in imprintData?.score) {
      if (imprintData?.score.hasOwnProperty(key)) {
        const value = imprintData?.score[key as keyof typeof imprintData.score];
        if (value !== null && value > highestScore) {
          highestScore = value;
          highestKey = key;
        }
      }
    }
    return highestKey;
  };

  const renderSVG = () => {
    const highestScore = getHighestScore();
    switch (highestScore) {
      case IMPRINT_SCORES.LAUGHTER:
        return (
          <View style={styles.imagePadding}>
            <LAUGHTER width={40} height={40} />
          </View>
        );
      case IMPRINT_SCORES.LIFE:
        return (
          <View style={styles.imagePadding}>
            {isGlobal ? (
              <LIFE width={40} height={40} />
            ) : (
              <LIFE_GRAY width={40} height={40} />
            )}
          </View>
        );
      case IMPRINT_SCORES.LOVE:
        return (
          <View style={styles.imagePadding}>
            {isGlobal ? (
              <LOVE width={40} height={40} />
            ) : (
              <LOVE_GRAY width={40} height={40} />
            )}
          </View>
        );
      case IMPRINT_SCORES.PURPOSE:
        return (
          <View style={styles.imagePadding}>
            {isGlobal ? (
              <PURPOSE width={40} height={40} />
            ) : (
              <PURPOSE_GRAY width={40} height={40} />
            )}
          </View>
        );
      case IMPRINT_SCORES.RESPECT:
        return (
          <View style={styles.imagePadding}>
            {isGlobal ? (
              <RESPECT width={40} height={40} />
            ) : (
              <RESPECT_GRAY width={40} height={40} />
            )}
          </View>
        );
      case IMPRINT_SCORES.SAFETY:
        return (
          <View style={styles.imagePadding}>
            {isGlobal ? (
              <SAFETY width={40} height={40} />
            ) : (
              <SAFETY_GRAY width={40} height={40} />
            )}
          </View>
        );
      default:
        return <></>;
    }
  };

  const getVerification = () => {
    switch (imprintData?.verificationRequest?.verificationStatus) {
      case IMPRINT_TIMELINE.VERIFIED:
        return (
          <View>
            <VERIFIED_ICON />
          </View>
        );
      case IMPRINT_TIMELINE.FAILED:
        return (
          <View>
            <FAILED_ICON />
          </View>
        );
      case IMPRINT_TIMELINE.REQUESTED:
        return (
          <View>
            <REQUESTED_ICON />
          </View>
        );
      case IMPRINT_TIMELINE.REQUEST_DENIED:
        return (
          <View>
            <REQUEST_DENIED_ICON />
          </View>
        );
      default:
        if (isGlobal) {
          return (
            <View>
              <OPEN_REQUEST_ICON />
            </View>
          );
        } else {
          if (imprintData?.verificationRequest) {
            return (
              <View>
                <UNVERIFIED_ICON />
              </View>
            );
          } else {
            return (
              <View>
                <REQUESTED_ICON />
              </View>
            );
          }
        }
    }
  };

  return (
    <View
      style={[
        styles.container,
        {paddingBottom: isMediaView ? 10 : Metrics.ratio(20)},
      ]}>
      <Image
        source={{uri: imprintData?.user?.avatarUrl}}
        style={styles.image}
      />
      <View style={styles.nameContainer}>
        <Pressable
          onPress={() =>
            onUserNameClicked && onUserNameClicked(imprintData?.user?.userName)
          }>
          <Text style={styles.name} numberOfLines={2}>
            {imprintData?.user?.userName}
            {imprintData?.checkIn !== null && (
              <Text style={styles.checkIn} numberOfLines={2}>
                {' was at '}
                <Text style={styles.boldText}>
                  {imprintData?.checkIn?.place}
                </Text>
              </Text>
            )}
          </Text>
        </Pressable>
        <View style={styles.timeContainer}>
          <EARTH_ICON />
          <Text style={styles.time}>
            {util.getUpdatedTimeDifference(imprintData?.createdAt ?? '')}
          </Text>
        </View>
      </View>
      <View style={styles.rightContainer}>
        {renderSVG()}
        {!isMediaView &&
        imprintData &&
        !imprintData?.followerRequestSent &&
        imprintData?.userId !== user.data.userId ? (
          <Pressable
            onPress={() => onMoreOtionClick && onMoreOtionClick(imprintData)}>
            <View style={styles.imagePadding}>
              <THREE_DOTS width={40} height={40} />
            </View>
          </Pressable>
        ) : (
          <></>
        )}
        {!isMediaView && imprintData?.userId === user.data.userId ? (
          <Pressable
            onPress={() => onMoreOtionClick && onMoreOtionClick(imprintData)}>
            <View style={styles.imagePadding}>
              <THREE_DOTS width={40} height={40} />
            </View>
          </Pressable>
        ) : (
          <></>
        )}
      </View>
    </View>
  );
};

export default UserDetails;
