import {StyleSheet, Platform} from 'react-native';
import {Colors, Fonts, Metrics} from '../../../theme';

export default StyleSheet.create({
  reactionContainerSelected: {
    width: Metrics.ratio(45),
    height: Metrics.ratio(45),
    borderRadius: Metrics.ratio(100),
    backgroundColor: Colors.black,
    justifyContent: 'center',
    alignItems: 'center',
    shadowOffset: {
      width: 0,
      height: 12.857142448425293,
    },
    shadowOpacity: 0.33,
    shadowRadius: 25.714284896850586,
    elevation: 8,
    alignContent: 'center',
  },
  reactionContainerUnselected: {
    width: Metrics.ratio(40),
    height: Metrics.ratio(40),
    borderRadius: Metrics.ratio(80),
    backgroundColor: Colors.white,
    justifyContent: 'center',
    alignItems: 'center',
    shadowOffset: {
      width: 0,
      height: 12.857142448425293,
    },
    shadowOpacity: 0.33,
    shadowRadius: 25.714284896850586,
    elevation: 8,
  },

  flagSelected: {
    width: Metrics.ratio(40),
    height: Metrics.ratio(40),
    borderRadius: Metrics.ratio(80),
    backgroundColor: Colors.red,
    justifyContent: 'center',
    alignItems: 'center',
    shadowOffset: {
      width: 0,
      height: 12.857142448425293,
    },
    shadowOpacity: 0.33,
    shadowRadius: 25.714284896850586,
    elevation: 8,
  },
  bookmarkContainerSelected: {
    width: Metrics.ratio(40),
    height: Metrics.ratio(40),
    marginLeft: Metrics.ratio(5),
    borderRadius: Metrics.ratio(20),
    backgroundColor: Colors.black,
    alignItems: 'center',

    shadowOffset: {
      width: 0,
      height: 12.857142448425293,
    },
    shadowOpacity: 0.33,
    shadowRadius: 25.714284896850586,
    elevation: 8,
  },
  shoutOutText: {
    fontFamily: Fonts.type.semi_bold,
    position: 'absolute',
    fontSize: Metrics.ratio(10),
    color: Colors.black,
    textAlign: 'center',
  },
  bookmarkContainerUnselected: {
    width: Metrics.ratio(40),
    height: Metrics.ratio(40),
    marginLeft: Metrics.ratio(5),
    borderRadius: Metrics.ratio(20),
    backgroundColor: Colors.white,
    alignItems: 'center',

    shadowOffset: {
      width: 0,
      height: 12.857142448425293,
    },
    shadowOpacity: 0.33,
    shadowRadius: 25.714284896850586,
    elevation: 8,
  },
  shoutOutContainer: {
    position: 'absolute',
    width: Metrics.ratio(22),
    height: Metrics.ratio(16),
    top: Metrics.ratio(-4),
    right: Metrics.ratio(-10),
    backgroundColor: Colors.shoutOutBackground,
    borderWidth: Metrics.ratio(2),
    borderColor: Colors.white,
    borderRadius: 100,
    justifyContent: 'center',
    alignItems: 'center',
  },
  alignSelf: {alignSelf: 'flex-start'},
  container: {
    alignSelf: 'flex-start',
    paddingLeft: Metrics.ratio(24),
  },
  reactionContainer: {
    width: Metrics.screenWidth - 40,
    position: 'absolute',
    flexDirection: 'row',
    top: Metrics.ratio(-30),
    paddingHorizontal: Metrics.ratio(20),
  },
  overRidePadding: {
    paddingHorizontal: Metrics.ratio(0),
  },
  marginTop: {top: Metrics.ratio(-50)},
  mHorizontal: {marginHorizontal: Metrics.ratio(5)},
  moderationContainer: {
    backgroundColor: Colors.gray,
    alignItems: 'center',
    zIndex: 100,
    flexDirection: 'row',
    justifyContent: 'center',
    width: '90%',
    bottom: 0,
    padding: Metrics.ratio(10),
    marginLeft: Metrics.ratio(20),
    marginRight: Metrics.ratio(50),
    borderRadius: Metrics.ratio(10),
  },
});
