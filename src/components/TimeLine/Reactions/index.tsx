import React, {useMemo, useState} from 'react';
import {View, Text} from 'react-native';
import styles from './styles';
import {
  ANGRY,
  BOOKMARK_BLACK,
  BOOKMARK_WHITE,
  DEFAULT_REACTION,
  FLAG,
  FLAG_RED,
  LOVE_REACTION,
  SAD_REACTION,
  SHOUT_OUT,
  SCARED_REACTION,
  RUDI_PROFILE_ICON,
} from '../../../constants/AssetSVGConstants';
import {connect} from 'react-redux';
import {
  ageBucket,
  ITimeLine,
  moderationStatus,
  UserImprintInteractions,
  UserState,
} from '../../../types';
import {ButtonView} from '../..';
import {ReactionType} from '../../../types';
import {Colors, Fonts, Metrics} from '../../../theme';
import {
  IMPRINT_TIMELINE,
  TOAST_MESSAGES,
  TOAST_TYPE,
} from '../../../constants/StringConstants';
import <PERSON><PERSON><PERSON><PERSON>and<PERSON> from 'react-native-outside-press';
import {isUserSubscribed} from '../../../util';
import {TOAST_VISIBILITY_TIMEOUT} from '../../../constants';
import {showToastMsg} from '../../Alert';

interface IReactions {
  user?: UserState;
  handleClick: (
    userImprintInteraction?: UserImprintInteractions,
    reaction?: string,
    imprint?: ITimeLine,
  ) => void;
  userImprintInteractions: UserImprintInteractions[];
  imprintData: ITimeLine;
  isMediaView?: boolean;
}

const Reactions: React.FC<IReactions> = ({
  user,
  userImprintInteractions,
  handleClick,
  imprintData,
  isMediaView,
}) => {
  const [showReactions, setShowReaction] = useState(false);

  const isBookmarked = () => {
    if (!userImprintInteractions || !user) {
      return false;
    }
    const userInteraction = userImprintInteractions.find(
      interaction => interaction?.userId === user?.data?.userId,
    );
    return userInteraction?.isBookmarked || false;
  };

  const isFlagged = () => {
    if (imprintData?.isFlagged) {
      return true;
    }

    if (!userImprintInteractions || !user) {
      return false;
    }

    const userInteraction = userImprintInteractions.find(
      interaction => interaction?.userId === user?.data?.userId,
    );

    return userInteraction?.isRedFlagged || false;
  };

  const getUserReaction = () => {
    if (!userImprintInteractions || !user) {
      return null;
    }
    const userInteraction = userImprintInteractions.find(
      interaction => interaction?.userId === user.data?.userId,
    );
    return userInteraction?.reaction || null;
  };

  const getCurrentUserInteraction = () => {
    if (!userImprintInteractions || !user) {
      return undefined;
    }
    const interaction = userImprintInteractions.find(
      interaction => interaction?.userId === user.data?.userId,
    );

    if (!interaction) {
      return undefined;
    }

    return interaction;
  };

  const reactionIcons = {
    ANGRY: <ANGRY height={25} width={25} />,
    LOVE: <LOVE_REACTION height={20} width={20} />,
    SAD: <SAD_REACTION height={25} width={25} />,
    SCARED: <SCARED_REACTION height={25} width={25} />,
  };

  const renderReactions = () => {
    const userReaction = getUserReaction();

    if (userReaction === null) {
      return <DEFAULT_REACTION />;
    }
    const reactionIcon = reactionIcons[userReaction];

    return reactionIcon ? (
      reactionIcon
    ) : (
      <DEFAULT_REACTION height={20} width={20} />
    );
  };

  const reactionsArray = useMemo(() => {
    const reactions = [
      {
        name: ReactionType.ANGRY,
        icon: <ANGRY height={25} width={25} />,
        style:
          getUserReaction() === ReactionType.ANGRY
            ? styles.reactionContainerSelected
            : styles.reactionContainerUnselected,
      },
      {
        name: ReactionType.LOVE,
        icon: <LOVE_REACTION height={25} width={25} />,
        style:
          getUserReaction() === ReactionType.LOVE
            ? styles.reactionContainerSelected
            : styles.reactionContainerUnselected,
      },
      {
        name: ReactionType.SAD,
        icon: <SAD_REACTION height={25} width={25} />,
        style:
          getUserReaction() === ReactionType.SAD
            ? styles.reactionContainerSelected
            : styles.reactionContainerUnselected,
      },
      {
        name: ReactionType.SCARED,
        icon: <SCARED_REACTION height={25} width={25} />,
        style:
          getUserReaction() === ReactionType.SCARED
            ? styles.reactionContainerSelected
            : styles.reactionContainerUnselected,
      },
      {
        name: IMPRINT_TIMELINE.BOOKMARK,
        icon: isBookmarked() ? (
          <BOOKMARK_WHITE height={60} width={60} />
        ) : (
          <BOOKMARK_BLACK width={60} height={60} />
        ),
        style: isBookmarked()
          ? styles.bookmarkContainerSelected
          : styles.bookmarkContainerUnselected,
      },
      {
        name: IMPRINT_TIMELINE.FLAG,
        icon: isFlagged() ? (
          <FLAG width={20} height={20} />
        ) : (
          <FLAG_RED width={20} height={20} />
        ),
        style: isFlagged()
          ? styles.flagSelected
          : styles.reactionContainerUnselected,
      },
    ];

    if (!imprintData?.isGlobal) {
      reactions.splice(4, 0, {
        name: ReactionType.SHOUT_OUT,
        icon: <SHOUT_OUT height={39} width={39} />,
        style: styles.reactionContainerUnselected,
      });
    }

    return reactions;
  }, [
    userImprintInteractions,
    imprintData?.isGlobal,
    isBookmarked,
    imprintData?.isFlagged,
  ]);

  return (
    <>
      {!isMediaView &&
        [
          moderationStatus.INCIDENT_REPORTED,
          moderationStatus.SUBMITTED,
        ].includes(imprintData.moderationStatus) && (
          <View style={styles.moderationContainer}>
            <Text style={{fontSize: Fonts.size.xxSmall}}>
              {IMPRINT_TIMELINE.MODERATION_STATUS}
            </Text>
          </View>
        )}
      <OutsidePressHandler
        onOutsidePress={() => {
          setShowReaction(false);
        }}>
        <View
          style={[
            styles.container,
            {paddingVertical: isMediaView ? 2 : Metrics.ratio(20)},
          ]}>
          <View
            style={[
              styles.reactionContainer,
              isMediaView && styles.marginTop,
              reactionsArray.length > 6 && styles.overRidePadding,
            ]}>
            {showReactions &&
              reactionsArray.map((item, index) => (
                <View key={`${imprintData.id}-${index}`}>
                  <ButtonView
                    style={[item.style, styles.mHorizontal]}
                    onPress={() => {
                      handleClick(
                        getCurrentUserInteraction(),
                        item.name,
                        imprintData,
                      );
                      setShowReaction(false);
                    }}>
                    {item.icon}
                  </ButtonView>
                  {item.name == ReactionType.SHOUT_OUT &&
                  imprintData?.speakUpCount ? (
                    <View style={styles.shoutOutContainer}>
                      <Text style={styles.shoutOutText}>
                        {imprintData?.speakUpCount}
                      </Text>
                    </View>
                  ) : null}
                </View>
              ))}
          </View>

          <ButtonView
            onPress={() => {
              if (!isUserSubscribed(user as UserState)) {
                showToastMsg(
                  TOAST_MESSAGES.SUBSCRIPTION_ERROR,
                  TOAST_TYPE.DEFAULT,
                  TOAST_VISIBILITY_TIMEOUT,
                  'top',
                );
                return;
              }

              return setShowReaction(!showReactions);
            }}
            style={
              getUserReaction() === null
                ? styles.alignSelf
                : styles.reactionContainerUnselected
            }>
            {getUserReaction() === null ? (
              <DEFAULT_REACTION />
            ) : (
              <RUDI_PROFILE_ICON height={35} width={35} />
            )}
          </ButtonView>
        </View>
      </OutsidePressHandler>
    </>
  );
};

const mapStateToProps = (user: UserState) => user;

export default connect(mapStateToProps)(Reactions);
