import React from 'react';
import {View} from 'react-native';
import styles from './styles';
import RenderHtml from 'react-native-render-html';
import {AppStyles, Colors, Metrics} from '../../../theme';
import {Text} from '../..';
import {decode, encode} from 'html-entities';
import {VALID_TAGS} from '../../../constants';

interface IPostText {
  text: string;
  isMediaView?: boolean;
  headline?: string | null;
}

const PostText: React.FC<IPostText> = ({text, isMediaView, headline}) => {
  const decodedHtml = decode(text);

  let strippedHtml = decodedHtml.replace(
    new RegExp(`</?(${VALID_TAGS.join('|')})[^>]*>`, 'gi'),
    '',
  );

  const safeHtml = encode(strippedHtml, {mode: 'nonAsciiPrintable'});

  return (
    <View
      style={[
        styles.container,
        {paddingBottom: isMediaView ? 0 : Metrics.ratio(20)},
      ]}>
      {headline !== null && (
        <Text
          color={Colors.black}
          style={AppStyles.mBottom5}
          type={'semi_bold'}>
          {headline}
        </Text>
      )}
      <RenderHtml
        source={{html: safeHtml}}
        contentWidth={Metrics.screenWidth}
      />
    </View>
  );
};

export default PostText;
