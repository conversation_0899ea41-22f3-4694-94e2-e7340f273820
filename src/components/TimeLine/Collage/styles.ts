import {StyleSheet} from 'react-native';
import {Colors, Metrics, Fonts, AppStyles} from '../../../theme';

export default StyleSheet.create({
  overlayText: {
    color: Colors.white,
    fontFamily: Fonts.type.regular,
    fontSize: Metrics.ratio(28),
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  playButton: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'center',
    alignItems: 'center',
  },
  absoluteFillObject: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    borderWidth: 5,
    borderColor: Colors.borderColors.activeBorderColor,
  },
  pdfViewContainer: {
    backgroundColor: '#101418',
    height: Metrics.ratio(55),
    alignContent: 'center',
    justifyContent: 'center',
  },
  pdfText: {
    fontFamily: Fonts.type.regular,
    fontSize: Metrics.ratio(14),
    textAlign: 'center',
    color: Colors.white,
  },
  pdfImage: {
    width: '100%',
    height: '100%',
    alignSelf: 'center',
    backgroundColor: Colors.white,
    borderWidth: 1,
    borderColor: Colors.lightGray,
    borderRadius: Metrics.ratio(6),
  },
  twoImagesContainer: {
    flexDirection: 'row',
    height: Metrics.ratio(235),
  },
  flexOne: {
    borderWidth: 1,
    borderColor: Colors.lightGray,
    flex: 1,
  },
  oneImageContainer: {
    height: Metrics.ratio(235),
  },
  border: {borderColor: Colors.gray, borderWidth: 5},
});
