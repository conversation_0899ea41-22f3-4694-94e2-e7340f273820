import {Image, Pressable, Text, View} from 'react-native';
import styles from './styles';
import {PLAY_BUTTON} from '../../../constants/AssetSVGConstants';
import {ITimeLine, ImprintMedia} from '../../../types';
import React from 'react';
import FastImage from 'react-native-fast-image';
import {IMPRINT_MEDIA} from '../../../constants/StringConstants';

interface CollageProps {
  data: ImprintMedia[];
  imprintData?: ITimeLine;
  handleMediaClicked?: (item?: ITimeLine) => void;
}

const Collage: React.FC<CollageProps> = ({
  data,
  handleMediaClicked,
  imprintData,
}) => {
  const renderImages = () => {
    if (data.length === 1) {
      return renderOneImage(0);
    }
    if (data.length === 2) {
      return (
        <>
          {renderOneImage(0)}
          {renderOneImage(1)}
        </>
      );
    }
    if (data.length === 3 || data.length > 3) {
      return (
        <>
          {videoExists() ? (
            <>
              {renderOneImage(0)}
              {renderTwoImages(1, 2)}
            </>
          ) : (
            <>
              {renderTwoImages(0, 1)}
              {renderOneImage(2)}
            </>
          )}
        </>
      );
    }
  };

  const showOverLay = () => {
    return (
      <View style={styles.overlay}>
        <Text style={styles.overlayText}>{'+' + (data.length - 3)}</Text>
      </View>
    );
  };

  const checkImageCount = () => {
    if (data.length > 3) {
      return true;
    }
    return false;
  };

  const videoExists = () => {
    const containsVideo = data.some(item => item.type === IMPRINT_MEDIA.VIDEO);
    if (containsVideo) {
      return true;
    } else {
      return false;
    }
  };

  const showPlayButton = () => {
    return (
      <View style={styles.playButton}>
        <PLAY_BUTTON />
      </View>
    );
  };

  const renderTwoImages = (index1: number, index2: number) => {
    return (
      <View style={styles.twoImagesContainer}>
        <View style={styles.flexOne}>
          {data[index1]?.type == IMPRINT_MEDIA.DOCUMENT ? (
            <Image
              source={require('../../../assets/icons/attachment.png')}
              defaultSource={require('../../../assets/icons/attachment.png')}
              style={styles.pdfImage}
              resizeMode="center"
            />
          ) : (
            <FastImage
              source={{
                uri:
                  data[index1]?.url ||
                  data[index1]?.sourceURL ||
                  data[index1]?.path,
              }}
              style={styles.flexOne}
            />
          )}
          {data[index1]?.type == IMPRINT_MEDIA.VIDEO ? showPlayButton() : <></>}
        </View>
        <View style={styles.flexOne}>
          {data[index2]?.type == IMPRINT_MEDIA.DOCUMENT ? (
            <Image
              source={require('../../../assets/icons/attachment.png')}
              defaultSource={require('../../../assets/icons/attachment.png')}
              style={styles.pdfImage}
              resizeMode="center"
            />
          ) : (
            <FastImage
              source={{
                uri:
                  data[index2]?.url ||
                  data[index2]?.sourceURL ||
                  data[index2]?.path,
              }}
              style={styles.flexOne}
            />
          )}
          {data[index2]?.type == IMPRINT_MEDIA.VIDEO ? showPlayButton() : <></>}
          {checkImageCount() && index2 == 2 ? showOverLay() : <></>}
        </View>
      </View>
    );
  };

  const renderOneImage = (index: number) => {
    return (
      <View
        style={[
          styles.oneImageContainer,
          index > 0 ? styles.border : index > 1 ? styles.border : {},
        ]}>
        {data[index]?.type == IMPRINT_MEDIA.DOCUMENT ? (
          <Image
            source={require('../../../assets/icons/attachment.png')}
            defaultSource={require('../../../assets/icons/attachment.png')}
            style={styles.pdfImage}
            resizeMode="center"
          />
        ) : (
          <FastImage
            source={{
              uri:
                data[index]?.type === IMPRINT_MEDIA.VIDEO
                  ? data[index]?.thumbUrl
                  : data[index]?.url ||
                    data[index]?.sourceURL ||
                    data[index]?.path,
            }}
            style={styles.absoluteFillObject}
            resizeMode="cover"
          />
        )}
        {data[index]?.type == IMPRINT_MEDIA.VIDEO ? showPlayButton() : <></>}
        {checkImageCount() && index == 2 ? showOverLay() : <></>}
      </View>
    );
  };

  return (
    <View style={{flex: 1}}>
      <Pressable
        onPress={() => handleMediaClicked && handleMediaClicked(imprintData)}>
        {renderImages()}
      </Pressable>
    </View>
  );
};

export default Collage;
