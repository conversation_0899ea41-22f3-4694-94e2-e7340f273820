import {View} from 'react-native';
import {styles} from './styles';
import {ButtonView, Text} from '..';
import {Colors} from '../../theme';

interface TabsProps {
  onTab1Press: () => void;
  onTab2Press: () => void;
  tab1Text: string;
  tab2Text: string;
  isActive: boolean;
}

const MediaTabs: React.FC<TabsProps> = ({
  onTab1Press,
  onTab2Press,
  tab1Text,
  tab2Text,
  isActive,
}) => {
  return (
    <View style={styles.container}>
      <ButtonView
        style={[
          styles.activeButton,
          {backgroundColor: isActive ? Colors.black1 : Colors.white},
        ]}
        onPress={onTab1Press}>
        <Text
          size={'buttonText'}
          color={
            isActive ? Colors.white : Colors.tabsTextColor.inActiveFriendList
          }
          type="semi_bold">
          {tab1Text}
        </Text>
      </ButtonView>
      <ButtonView
        style={[
          styles.requestButton,
          {backgroundColor: !isActive ? Colors.black1 : Colors.white},
        ]}
        onPress={onTab2Press}>
        <Text
          size={'buttonText'}
          color={
            !isActive ? Colors.white : Colors.tabsTextColor.inActiveFriendList
          }
          type="semi_bold">
          {tab2Text}
        </Text>
      </ButtonView>
    </View>
  );
};

export default MediaTabs;
