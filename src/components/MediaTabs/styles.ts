import {StyleSheet} from 'react-native';
import {Colors, Metrics} from '../../theme';

export const styles = StyleSheet.create({
  container: {
    marginVertical: Metrics.ratio(24),
    flexDirection: 'row',
    marginHorizontal: Metrics.baseMargin,
  },
  activeButton: {
    borderRadius: Metrics.ratio(100),
    backgroundColor: Colors.black1,
    paddingHorizontal: Metrics.ratio(16),

    paddingVertical: Metrics.ratio(11),
    alignItems: 'center',
    justifyContent: 'center',
  },
  requestButton: {
    marginLeft: Metrics.ratio(10),
    borderRadius: Metrics.ratio(100),
    backgroundColor: Colors.black1,
    paddingHorizontal: Metrics.ratio(16),
    paddingVertical: Metrics.ratio(11),
    alignItems: 'center',
    justifyContent: 'center',
  },
});
