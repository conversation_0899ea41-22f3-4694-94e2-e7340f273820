// @flow
import {StyleSheet} from 'react-native';
import {Metrics} from '../../../theme';

export default StyleSheet.create({
  checkInImageContainer: {alignSelf: 'center', marginTop: Metrics.baseMargin},
  removeCheckInImage: {
    position: 'absolute',
    zIndex: 999,
    right: Metrics.ratio(10),
    top: Metrics.ratio(10),
  },
  checkInImage: {
    height: Metrics.ratio(250),
    width: Metrics.screenWidth * 0.9,
  },
});
