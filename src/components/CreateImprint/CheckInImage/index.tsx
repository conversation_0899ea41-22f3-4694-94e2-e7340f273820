// @flow

import React from 'react';
import {View, Image} from 'react-native';
import {ButtonView} from '../../';
import styles from './styles';
import {CROSS_ROUND} from '../../../constants/AssetSVGConstants';
import _ from 'lodash';

interface CheckInImageProps {
  imageUri?: string;
  removeImage: () => void;
}

const CheckInImage: React.FC<CheckInImageProps> = ({imageUri, removeImage}) => {
  return (
    <View style={styles.checkInImageContainer}>
      <ButtonView style={styles.removeCheckInImage} onPress={removeImage}>
        <CROSS_ROUND />
      </ButtonView>

      <Image source={{uri: imageUri}} style={styles.checkInImage} />
    </View>
  );
};

export default CheckInImage;
