// @flow

import React, {useRef} from 'react';
import {Image, Linking, View} from 'react-native';
import {Text, ButtonView} from '../../';
import styles from './styles';
import _ from 'lodash';
import {FlatList} from 'react-native-gesture-handler';
import {SafeAreaView} from 'react-native-safe-area-context';
import Video from 'react-native-video';
import {useNavigation} from '@react-navigation/native';
import {CROSS_ROUND, PLAY_ICON} from '../../../constants/AssetSVGConstants';
import {
  CREATE_IMPRINT,
  MEDIA_FILE_TYPE,
} from '../../../constants/StringConstants';
import {AppStyles, Colors} from '../../../theme';
import Routes from '../../../constants/RouteConstants';
import util from '../../../util';
import {ImprintMedia, MediaItem} from '../../../types';
import OpenFile from 'react-native-file-viewer';

interface ListProps {
  mediaList: ImprintMedia[];
  removeMedia: (index: number) => void;
  onRightPress: () => void;
}

const MediaComponent: React.FC<ListProps> = ({
  mediaList,
  removeMedia,
  onRightPress,
}) => {
  const videoRef = useRef<Video | null>(null);
  const navigation = useNavigation();

  const renderMediaItem = ({item, index}: {item: MediaItem; index: number}) => {
    const type = util.getType(item.mime as string);

    const isDocs = item.mime?.includes(MEDIA_FILE_TYPE.DOCUMENT);

    return (
      <ButtonView
        style={AppStyles.alignSelfCenter}
        onPress={() =>
          type == 'Document'
            ? OpenFile.open(item.file as string, {
                displayName: item.filename,
                showOpenWithDialog: true,
              })
            : (navigation.navigate as (route: string, {}) => void)(
                Routes.VIEW_MEDIA,
                {
                  item: item,
                },
              )
        }>
        <ButtonView
          style={styles.removeContainer}
          onPress={() => removeMedia(index)}>
          <CROSS_ROUND />
        </ButtonView>
        {type === 'Video' ? (
          <>
            <Video
              resizeMode="cover"
              paused={true}
              volume={2.0}
              source={{
                uri: item.path,
              }}
              ref={videoRef}
              style={styles.images}
              onLoad={() => videoRef?.current?.seek(1)}
            />
            <View style={styles.videoPlayIcon}>
              <PLAY_ICON />
            </View>
          </>
        ) : (
          <Image
            source={{uri: item.sourceURL || item.path}}
            style={styles.images}
          />
        )}
      </ButtonView>
    );
  };
  const renderHeader = () => {
    return (
      <ButtonView onPress={onRightPress} style={styles.button}>
        <Text color={Colors.text.white} size={'buttonText'}>
          {CREATE_IMPRINT.DONE}
        </Text>
      </ButtonView>
    );
  };
  return (
    <SafeAreaView style={styles.listContainer}>
      {renderHeader()}
      <FlatList
        showsVerticalScrollIndicator={false}
        data={mediaList}
        renderItem={renderMediaItem}
      />
    </SafeAreaView>
  );
};

export default MediaComponent;
