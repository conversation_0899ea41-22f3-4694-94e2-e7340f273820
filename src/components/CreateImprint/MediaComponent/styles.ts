// @flow
import {StyleSheet} from 'react-native';
import {Colors, Metrics} from '../../../theme';

export default StyleSheet.create({
  removeContainer: {
    position: 'absolute',
    marginTop: Metrics.ratio(20),
    zIndex: 999,
    right: Metrics.ratio(10),
    top: Metrics.ratio(20),
  },
  images: {
    marginVertical: Metrics.baseMargin,
    height: Metrics.ratio(250),
    width: Metrics.screenWidth * 0.85,
  },

  videoPlayIcon: {
    position: 'absolute',
    top: Metrics.ratio(100),
    right: Metrics.ratio(140),
  },
  button: {
    alignSelf: 'flex-end',
    marginRight: Metrics.ratio(32),
    backgroundColor: Colors.background.publishActiveColor,
    paddingHorizontal: Metrics.baseMargin,
    paddingVertical: Metrics.smallMargin,
    borderRadius: Metrics.ratio(6, 6),
  },
  listContainer: {
    backgroundColor: Colors.gray,
    position: 'absolute',
    zIndex: 999,
    width: Metrics.screenWidth,
    height: Metrics.screenHeight,
  },
});
