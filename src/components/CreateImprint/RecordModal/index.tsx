import React, {useEffect, useMemo, useState} from 'react';
import {BottomSheetModal, BottomSheetModalProvider} from '@gorhom/bottom-sheet';
import {Colors, Metrics} from '../../../theme';
import {ButtonView, Text} from '../../';
import {
  MICRO_PHONE_POST,
  PLAY_ICON,
  STOP,
  TICK_ICON,
  CROSS_ROUND,
} from '../../../constants/AssetSVGConstants';
import {styles} from './styles';
import {Dimensions, Platform, View} from 'react-native';
import Svg, {Rect} from 'react-native-svg';
import {Pressable, Switch} from 'react-native-gesture-handler';
import {CREATE_IMPRINT} from '../../../constants/StringConstants';
const SCREEN_WIDTH = Dimensions.get('window').width;
const NUM_BARS = 200; // Number of bars in the waveform
const BAR_SPACING = 1; // Space between bars
const BAR_WIDTH = (SCREEN_WIDTH - (NUM_BARS - 1) * BAR_SPACING) / NUM_BARS;

interface RecordModalProps {
  onStartRecording: () => void;
  onStopRecording: () => void;
  bottomSheetModalRef: any;
  onStartPlay: () => void;
  waveData?: any;
  recordedFilePath?: null | string;
  resetRecording: () => void;
  submitRecording?: () => void;
  isRecording: boolean;
  isPlaying: boolean;
  timers: boolean;
  isRecordingAudio: boolean;
  toggleRecordingMode: (value: any) => void;
  onStopPlay: () => void;
}

const RecordModal: React.FC<RecordModalProps> = ({
  onStopRecording,
  onStartRecording,
  onStartPlay,
  waveData,
  recordedFilePath,
  bottomSheetModalRef,
  resetRecording,
  submitRecording,
  isRecording,
  isPlaying,
  isRecordingAudio,
  toggleRecordingMode,
  onStopPlay,
  timers,
}) => {
  const snapPoints = useMemo(() => ['10%', '30%'], []);

  const closeModal = () => {
    onStopRecording();
  };

  return (
    <BottomSheetModalProvider>
      <BottomSheetModal
        handleIndicatorStyle={styles.handleStyle}
        contentHeight={Metrics.screenHeight * 0.5}
        backgroundStyle={styles.bottomSheet}
        enablePanDownToClose={true}
        ref={bottomSheetModalRef}
        index={1}
        snapPoints={snapPoints}
        onDismiss={closeModal}>
        {Platform.OS === 'android' && (
          <View style={styles.switchContainer}>
            <Text
              size={'xxSmall'}
              style={{marginLeft: Metrics.smallMargin}}
              color={Colors.black}>
              {CREATE_IMPRINT.SPEECH_TEXT}
            </Text>
            <Pressable onPress={toggleRecordingMode}>
              <Switch
                disabled
                value={isRecordingAudio}
                key={'string'}
                thumbColor={Colors.black}
                trackColor={{false: 'gray', true: Colors.black3}}
              />
            </Pressable>
            <Text
              size={'xxSmall'}
              color={Colors.black}
              style={{marginRight: Metrics.smallMargin}}>
              {CREATE_IMPRINT.RECORD}
            </Text>
          </View>
        )}
        <View style={styles.container}>
          <View style={styles.waveformContainer}>
            <Svg height="50" width="100%">
              {waveData.map((bar, index) => (
                <Rect
                  key={index}
                  x={index * (BAR_WIDTH + BAR_SPACING)}
                  y={50 - bar.value}
                  width={BAR_WIDTH}
                  height={bar.value}
                  fill={bar.color}
                  rx="2"
                />
              ))}
            </Svg>
          </View>
          <View style={styles.controls}>
            <ButtonView
              onPress={
                !recordedFilePath
                  ? onStartRecording
                  : isPlaying
                  ? onStopPlay
                  : onStartPlay
              }>
              {!recordedFilePath ? (
                <MICRO_PHONE_POST />
              ) : isPlaying ? (
                <STOP height={50} width={50} />
              ) : (
                <PLAY_ICON />
              )}
            </ButtonView>
            {isRecordingAudio && (
              <Text
                color={Colors.black}
                style={{textAlign: 'center', marginTop: 10}}>
                {timers}s
              </Text>
            )}
            <View style={{flexDirection: 'row', alignItems: 'center'}}>
              <ButtonView
                onPress={() => {
                  if (!recordedFilePath) {
                    onStopRecording();
                  } else {
                    resetRecording();
                  }
                }}>
                {!recordedFilePath ? <STOP /> : <CROSS_ROUND />}
              </ButtonView>
              {recordedFilePath && (
                <ButtonView
                  onPress={submitRecording && submitRecording}
                  style={{marginHorizontal: Metrics.smallMargin}}>
                  <TICK_ICON height={30} width={30} />
                </ButtonView>
              )}
            </View>
          </View>
        </View>
      </BottomSheetModal>
    </BottomSheetModalProvider>
  );
};

export default RecordModal;
