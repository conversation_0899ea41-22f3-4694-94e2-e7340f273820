import {StyleSheet} from 'react-native';
import {Metrics, Colors} from '../../../theme';

export const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  waveformContainer: {
    width: '100%',
    height: 50,
    marginVertical: 20,
    backgroundColor: Colors.background.article,
    borderRadius: Metrics.ratio(8),
    overflow: 'visible',
  },
  controls: {
    paddingHorizontal: Metrics.ratio(10),
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },

  handleStyle: {
    backgroundColor: Colors.handle,
    height: Metrics.ratio(6),
    borderRadius: Metrics.ratio(8),
    width: Metrics.ratio(45),
  },
  bottomSheet: {
    backgroundColor: Colors.background.home,
    elevation: 10,
    shadowColor: Colors.black,
    shadowOffset: {
      width: 0,
      height: Metrics.ratio(12) as number,
    },
    shadowOpacity: 0.58,
    shadowRadius: 16.0,
  },
  switchContainer: {
    alignSelf: 'flex-end',
    alignItems: 'center',
    flexDirection: 'row',
    marginRight: Metrics.ratio(20),
  },
});
