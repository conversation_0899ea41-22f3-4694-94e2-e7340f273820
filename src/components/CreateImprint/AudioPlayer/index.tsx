import React from 'react';
import {Colors, Fonts, Metrics} from '../../../theme';
import {ButtonView} from '../../';
import {PLAY_ICON, BIN, STOP} from '../../../constants/AssetSVGConstants';
import {styles} from './styles';
import {Dimensions, View, Text} from 'react-native';
import Svg, {Rect} from 'react-native-svg';
const SCREEN_WIDTH = Dimensions.get('window').width;
const NUM_BARS = 200; // Number of bars in the waveform
const BAR_SPACING = 1; // Space between bars
const BAR_WIDTH = (SCREEN_WIDTH - (NUM_BARS - 1) * BAR_SPACING) / NUM_BARS;

import util from '../../../util';
import {useFocusEffect} from '@react-navigation/native';

interface AudioPlayerProps {
  onStartPlay: () => void;
  waveData?: any;
  recordedFilePath?: null | string;
  DeleteAudio?: () => void;
  isPlying?: boolean;
  onStopPlay: () => void;
  currentPosition?: any;
  totalDuration?: any;
  extraStyle?: any;
}

const AudioPlayer: React.FC<AudioPlayerProps> = ({
  onStartPlay,
  waveData,
  recordedFilePath,
  DeleteAudio,
  isPlying,
  onStopPlay,
  currentPosition,
  totalDuration,
  extraStyle,
}) => {
  useFocusEffect(
    React.useCallback(() => {
      return () => {
        onStopPlay();
      };
    }, []),
  );
  return (
    <View style={styles.container}>
      <Text
        style={{
          fontSize: Fonts.size.xxSmall,
          marginRight: Metrics.ratio(10),
        }}>
        {totalDuration !== 0
          ? `${util.formatTimer(currentPosition)}/${util.formatTimer(
              totalDuration,
            )}`
          : '     '}
      </Text>

      <View style={[styles.waveformContainer, extraStyle]}>
        <Svg height="50" width="100%">
          {waveData.map((bar: any, index: any) => (
            <Rect
              key={index}
              x={index * (BAR_WIDTH + BAR_SPACING)}
              y={50 - bar.value}
              width={BAR_WIDTH}
              height={bar.value}
              fill={bar.color}
              rx="2"
            />
          ))}
        </Svg>
      </View>
      <View style={styles.controls}>
        <ButtonView
          onPress={!isPlying ? onStartPlay : onStopPlay}
          style={{
            marginLeft: Metrics.ratio(10),
            backgroundColor: Colors.background.home,
            borderRadius: 35,
          }}>
          {!isPlying ? <PLAY_ICON height={35} width={35} /> : <STOP />}
        </ButtonView>
        {DeleteAudio && (
          <ButtonView onPress={DeleteAudio}>
            <BIN />
          </ButtonView>
        )}
      </View>
    </View>
  );
};

export default AudioPlayer;
