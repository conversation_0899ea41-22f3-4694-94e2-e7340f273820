import {StyleSheet} from 'react-native';
import {Metrics, Colors} from '../../../theme';

export const styles = StyleSheet.create({
  container: {
    marginHorizontal: Metrics.ratio(20),
    flexDirection: 'row',
    // backgroundColor: 'red',
    //  flex: 1,
    // justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'flex-start',
  },
  waveformContainer: {
    alignSelf: 'flex-start',
    //  flexDirection: 'row',
    width: '75%',
    height: 40,
    marginVertical: 20,
    backgroundColor: Colors.gray,
    borderRadius: 20,
    overflow: 'hidden',
  },
  controls: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
    // width: '90%',
  },
});
