import {Platform, StyleSheet} from 'react-native';
import {Metrics, Colors} from '../../theme';

export const styles = StyleSheet.create({
  sheetHeight: Platform.OS == 'ios' ? {height: '50%'} : {height: '55%'},

  mediaButton: {
    height: Metrics.ratio(50),
    borderRadius: Metrics.ratio(6),
    backgroundColor: Colors.itemColors.itemBackgroundColor,
    alignItems: 'center',
    marginHorizontal: Metrics.ratio(24),
    flexDirection: 'row',
  },
  commonButtonStyle: {
    height: Metrics.ratio(50),
    borderRadius: Metrics.ratio(6),
    backgroundColor: Colors.itemColors.itemBackgroundColor,
    alignItems: 'center',
    marginHorizontal: Metrics.ratio(24),
    flexDirection: 'row',
    marginVertical: Metrics.baseMargin,
    paddingHorizontal: Metrics.ratio(10),
  },
});
