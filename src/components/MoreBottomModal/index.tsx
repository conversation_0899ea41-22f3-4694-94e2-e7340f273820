import React from 'react';
import {View} from 'react-native';
import {AppStyles, Colors} from '../../theme';
import {BottomSheetModal, ButtonView, Text} from '..';
import {
  CAMERA_GREEN,
  LOCATION_MARKER,
  VIDEO_GREEN,
  SPEAK_UP,
  BOOKMARK_RED,
  ARTICLES,
} from '../../constants/AssetSVGConstants';
import {PROFILE_OPTIONS_LIST} from '../../constants/StringConstants';
import {styles} from './styles';

interface BottomSheetModalProps {
  showMoreModal: boolean;
  hideModal: () => void;
  onPhotoPress: () => void;
  onVideosPress: () => void;
  onArticlesPress: () => void;
  onCheckInPress: () => void;
  onSpeakUpPress: () => void;
  onBookMarksPress: () => void;
}

const MoreBottomMOdal: React.FC<BottomSheetModalProps> = ({
  onPhotoPress,
  onVideosPress,
  onCheckInPress,
  onSpeakUpPress,
  onArticlesPress,
  onBookMarksPress,
  showMoreModal,
  hideModal,
}) => {
  return (
    <BottomSheetModal
      customStyle={styles.sheetHeight}
      visible={showMoreModal}
      onClose={hideModal}
      //  onBackdropPress={hideModal}
    >
      <View>
        <ButtonView onPress={onPhotoPress} style={styles.mediaButton}>
          <CAMERA_GREEN />
          <Text
            color={Colors.itemColors.subTitleColor}
            style={AppStyles.mLeft5}
            size={'medium'}
            type="medium">
            {PROFILE_OPTIONS_LIST.PHOTOS}
          </Text>
        </ButtonView>
        <ButtonView onPress={onVideosPress} style={styles.commonButtonStyle}>
          <VIDEO_GREEN />
          <Text
            color={Colors.itemColors.subTitleColor}
            style={AppStyles.mLeft5}
            size={'medium'}
            type="medium">
            {PROFILE_OPTIONS_LIST.VIDEOS}
          </Text>
        </ButtonView>
        <ButtonView
          onPress={onCheckInPress}
          style={[styles.commonButtonStyle, AppStyles.mTop0]}>
          <LOCATION_MARKER />
          <Text
            color={Colors.itemColors.subTitleColor}
            style={AppStyles.mLeft10}
            size={'medium'}
            type="medium">
            {PROFILE_OPTIONS_LIST.CHECK_INS}
          </Text>
        </ButtonView>
        <ButtonView
          onPress={onSpeakUpPress}
          style={[styles.commonButtonStyle, AppStyles.mTop0]}>
          <View style={AppStyles.flexRow}>
            <SPEAK_UP />
            <Text
              color={Colors.itemColors.subTitleColor}
              style={AppStyles.mLeft10}
              size={'medium'}
              type="medium">
              {PROFILE_OPTIONS_LIST.SPEAK_UP}
            </Text>
          </View>
        </ButtonView>
        <ButtonView
          onPress={onBookMarksPress}
          style={[styles.commonButtonStyle, AppStyles.mTop0]}>
          <View style={AppStyles.flexRow}>
            <BOOKMARK_RED />
            <Text
              color={Colors.itemColors.subTitleColor}
              style={AppStyles.mLeft10}
              size={'medium'}
              type="medium">
              {PROFILE_OPTIONS_LIST.BOOK_MARKS}
            </Text>
          </View>
        </ButtonView>
        <ButtonView
          onPress={onArticlesPress}
          style={[styles.commonButtonStyle, AppStyles.mTop0]}>
          <View style={AppStyles.flexRow}>
            <ARTICLES />
            <Text
              color={Colors.itemColors.subTitleColor}
              style={AppStyles.mLeft10}
              size={'medium'}
              type="medium">
              {PROFILE_OPTIONS_LIST.ARTICLES}
            </Text>
          </View>
        </ButtonView>
      </View>
    </BottomSheetModal>
  );
};

export default MoreBottomMOdal;
