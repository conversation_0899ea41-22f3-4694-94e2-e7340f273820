// @flow
import {StyleSheet} from 'react-native';
import {Colors, Metrics, Fonts, AppStyles} from '../../theme';
import {DH, DW} from '../../theme/responsive';

export default StyleSheet.create({
  labelContainer: {
    position: 'absolute',
    marginTop: DH(-10),
    zIndex: 999,
    marginLeft: DW(20),
  },
  inputContainer: {
    overflow: 'hidden',
    justifyContent: 'space-between',
    alignItems: 'center',
    flexDirection: 'row',
    height: Metrics.ratio(55),
    borderColor: Colors.borderColors.activeBorderColor,
    borderWidth: Metrics.ratio(2),
    borderRadius: Metrics.ratio(8),
    marginHorizontal: Metrics.ratio(8),
  },
  hitSlop: {top: 20, bottom: 20, left: 20, right: 20},
});
