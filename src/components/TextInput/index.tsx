import React, {forwardRef, useRef} from 'react';
import {
  TextInput as RNTextInput,
  View,
  Text,
  TextInputProps as RNTextInputProps,
} from 'react-native';
import {ButtonView} from '../';
import {Colors, Fonts, Images, Metrics} from '../../theme';
import styles from './styles';
import _ from 'lodash';

interface TextInputProps extends RNTextInputProps {
  label?: string;
  placeholder?: string;
  error?: string;
  containerStyle?: any;
  onPress?: () => void;
  multiline?: boolean;
  placeholderTextColor?: string;
  rightImage?: any;
  leftImage?: any;
  isCorrect?: boolean;
  backgroundColor?: string;
  Style?: {};
}

const TextInput: React.ForwardRefRenderFunction<RNTextInput, TextInputProps> = (
  {
    label = '',
    error = '',
    containerStyle = {marginTop: 10},
    onPress,
    multiline = false,
    rightImage,
    leftImage,
    placeholderTextColor = Colors.gray,
    isCorrect = false,
    backgroundColor,
    Style,
    placeholder,

    ...rest
  },
  ref,
) => {
  const myRef = ref || useRef<RNTextInput>(null);

  return (
    <View style={containerStyle}>
      <View style={styles.labelContainer}>
        <Text
          style={{
            fontFamily: Fonts.type.semi_bold,
            color: Colors.black,
            fontSize: Fonts.size.xSmall,
            backgroundColor: backgroundColor ? backgroundColor : Colors.white,
            padding: 2,
          }}>
          {label}
        </Text>
      </View>
      <View
        style={[
          styles.inputContainer,
          {
            borderColor: isCorrect
              ? Colors.borderColors.incorrectBorderColor
              : Colors.borderColors.activeBorderColor,
          },
        ]}>
        <View
          style={{flexDirection: 'row', alignItems: 'center', marginLeft: 10}}>
          {leftImage && leftImage}

          <RNTextInput
            ref={myRef}
            placeholder={placeholder}
            placeholderTextColor={Colors.text.placeHolderTextColor}
            style={{
              marginLeft: 10,
              width: Metrics.screenWidth * 0.65,
              height: 56,
              fontSize: Fonts.size.xSmall,
              color: Colors.black,
            }}
            {...rest}
          />
        </View>
        {rightImage && (
          <ButtonView
            hitSlop={{top: 20, bottom: 20, left: 20, right: 20}}
            style={{
              marginRight: 10,
            }}
            onPress={onPress}>
            {rightImage}
          </ButtonView>
        )}
      </View>
    </View>
  );
};

export default forwardRef(TextInput);

//export default TextInput;
