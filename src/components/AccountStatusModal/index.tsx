import {View, Text} from 'react-native';
import styles from './styles';
import {AppButton} from '../../components';
import {connect} from 'react-redux';
import {COMMON, TOAST_MESSAGES} from '../../constants/StringConstants';
import _ from 'lodash';
import {Colors} from 'react-native/Libraries/NewAppScreen';
import {useNavigation, useNavigationState} from '@react-navigation/native'; // <-- import this
import util from '../../util';
import {
  appealDecisionRequest,
  setSystemAction,
  userLogout,
} from '../../actions/UserActions';
import {
  ACCOUNT_STATUS,
  CONTENT_TYPE_MAPPING,
  ITransparencyPayload,
  UserState,
} from '../../types';
import Routes from '../../constants/RouteConstants';

interface AccountStatusModalProps {
  user: UserState;
  title: string;
  userLogout: () => void;
  appealDecisionRequest: (
    payload: ITransparencyPayload,
    responseCallback: any,
  ) => void;
  setSystemAction: (payload?: any) => void;
}

const AccountStatusModal: React.FC<AccountStatusModalProps> = ({
  title,
  userLogout,
  appealDecisionRequest,
  setSystemAction,
  user,
}) => {
  const currentRouteName = useNavigationState(state => {
    const route = state?.routes[state.index];
    return route?.name;
  });
  const navigation = useNavigation();

  const isLoginScreen = currentRouteName === 'authStack';

  if (currentRouteName === undefined) {
    return null;
  }

  const onClickAppeal = () => {
    let systemActionForImprint = user.userInfo.systemActionForImprint;
    let systemActionForUserReport = user.userInfo?.systemActionForUserReport;
    let systemActionForChat = user.userInfo?.systemActionForChat;

    const actionObject =
      systemActionForImprint ||
      systemActionForChat ||
      systemActionForUserReport;

    if (!actionObject) {
      return;
    }

    const {violationContentId, contentType} = actionObject;

    if (!contentType || !(contentType in CONTENT_TYPE_MAPPING)) {
      console.error(`Invalid content type: ${contentType}`);
      return;
    }

    const complexType =
      CONTENT_TYPE_MAPPING[contentType as keyof typeof CONTENT_TYPE_MAPPING];

    const payload: ITransparencyPayload = {
      complex_type: complexType,
      content_id: violationContentId,
    };
    setSystemAction({});
    appealDecisionRequest(payload, (res: any) => {
      if (res) {
        (navigation.navigate as (route: string, params: any) => void)(
          Routes.REPORT_SCREEN,
          {url: res.redirectUrl, title: ''},
        );
      }
    });
  };

  return (
    <View style={styles.mainContainer}>
      <View style={styles.container}>
        <View style={styles.titleContainer}>
          <Text style={styles.title}>
            {title.charAt(0).toUpperCase() + title.slice(1).toLowerCase()}
          </Text>
        </View>
        <Text style={styles.subHeading}>
          {title === ACCOUNT_STATUS.SUSPEND
            ? TOAST_MESSAGES.USER_SUSPEND
            : TOAST_MESSAGES.USER_BANNED}
        </Text>

        {currentRouteName !== undefined && (
          <View style={styles.buttonContainer}>
            {isLoginScreen ? (
              <>
                <AppButton
                  buttonStye={styles.retakeButton}
                  text={COMMON.APPEAL_DECISION}
                  type={'xxxSmall'}
                  onPress={() => onClickAppeal()}
                />
                <AppButton
                  buttonStye={[styles.nextButton]}
                  text={COMMON.LOGOUT}
                  textColor={Colors.white}
                  onPress={() => {
                    util.handleUserLogout(userLogout);
                  }}
                />
              </>
            ) : (
              <AppButton
                buttonStye={styles.retakeButton}
                text={COMMON.OK}
                onPress={() => {
                  util.handleUserLogout(
                    userLogout,
                    navigation.navigate(Routes.LOGIN as never),
                  );
                }}
              />
            )}
          </View>
        )}
      </View>
    </View>
  );
};

const mapStateToProps = (state: any) => ({user: state.user});

export default connect(mapStateToProps, {
  userLogout,
  appealDecisionRequest,
  setSystemAction,
})(AccountStatusModal);
