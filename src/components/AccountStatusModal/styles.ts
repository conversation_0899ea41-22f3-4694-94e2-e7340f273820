import {StyleSheet} from 'react-native';
import {Colors, Metrics, Fonts} from '../../theme';

// @flow

export default StyleSheet.create({
  mainContainer: {
    height: Metrics.ratio(Metrics.screenHeight),
    width: Metrics.ratio(Metrics.screenWidth),
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    alignItems: 'center',
  },
  container: {
    width: Metrics.ratio(Metrics.screenWidth - 40),
    height: Metrics.ratio(Metrics.screenHeight / 3),
    backgroundColor: 'white',
    position: 'absolute',
    borderRadius: Metrics.ratio(10),
    bottom: Metrics.ratio(Metrics.screenHeight / 2 - 100),
  },
  titleContainer: {
    flexDirection: 'row',
    borderTopLeftRadius: Metrics.ratio(10),
    borderTopRightRadius: Metrics.ratio(10),
    backgroundColor: Colors.lightGray,
    height: Metrics.ratio(50),
    alignItems: 'center',
    justifyContent: 'center',
  },
  title: {
    fontFamily: Fonts.type.bold,
    fontSize: Fonts.size.normal,
    color: Colors.black,
  },
  buttonContainer: {
    bottom: 0,
    marginVertical: Metrics.ratio(10),
    width: Metrics.ratio(Metrics.screenWidth - 60),
    flexDirection: 'row',
    alignSelf: 'center',
    justifyContent: 'space-evenly',
    alignItems: 'flex-end',
  },
  retakeButton: {
    width: Metrics.ratio(140),
    backgroundColor: Colors.gray,
    borderColor: Colors.black,
    borderWidth: Metrics.ratio(1),
    borderRadius: Metrics.ratio(6),
    marginHorizontal: Metrics.ratio(0),
  },

  subHeading: {
    fontFamily: Fonts.type.medium,
    alignSelf: 'center',
    margin: Metrics.ratio(20),
    alignItems: 'center',
  },
  nextButton: {
    marginHorizontal: Metrics.ratio(0),
    width: Metrics.ratio(140),
  },
});
