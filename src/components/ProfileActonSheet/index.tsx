import {forwardRef, useImperativeHandle} from 'react';
import {useActionSheet} from '@expo/react-native-action-sheet';
import {COMMON, FRIENDS, PROFILE} from '../../constants/StringConstants';

type ProfileActionSheetProps = {
  onSelect: (selectedOption: string) => void;
  userData: any;
};

export type ProfileActonSheet = {
  handlePress: () => void;
  userData: any;
};

const ProfileActionSheetComponent = forwardRef<
  ProfileActonSheet,
  ProfileActionSheetProps
>(({onSelect, userData}, ref) => {
  const {showActionSheetWithOptions} = useActionSheet();

  const handlePress = () => {
    const options: string[] = [];

    if (userData.isFollower) {
      options.push(FRIENDS.REMOVE);
    }

    if (userData?.isUserBlocked === true) {
      options.push(FRIENDS.UN_BLOCK_USER);
    } else {
      options.push(FRIENDS.BLOCK_USER);
    }
    if (userData?.blockedImprints) {
      options.push(FRIENDS.UN_BLOCK);
    } else {
      options.push(FRIENDS.BLOCK);
    }

    options.push(FRIENDS.REPORT_USER);
    options.push(COMMON.CANCEL);
    const cancelButtonIndex = options.length - 1;

    showActionSheetWithOptions(
      {
        options,
        cancelButtonIndex,
        title: PROFILE.PROFILE_ACTIONS,
      },
      (selectedIndex?: number) => {
        if (
          selectedIndex !== undefined &&
          selectedIndex !== cancelButtonIndex
        ) {
          onSelect(options[selectedIndex]);
        }
      },
    );
  };

  useImperativeHandle(ref, () => ({
    handlePress,
    userData,
  }));

  return null;
});

export default ProfileActionSheetComponent;
