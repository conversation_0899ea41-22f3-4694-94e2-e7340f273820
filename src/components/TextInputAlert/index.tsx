import React, {useState} from 'react';
import {Alert, Modal, TextInput, Pressable, View, Text} from 'react-native';
import {Fonts} from '../../theme';
import styles from './styles';

interface ITextInputAlert {
  visible: boolean;
  onDone: (text: string) => void;
  onClose: () => void;
  placeHolder?: string;
  description?: string;
}

const TextInputAlert: React.FC<ITextInputAlert> = ({
  visible,
  onDone,
  onClose,
  placeHolder = 'Enter your reason here for flagging this post...',
  description = `Please mention why you want to flag this post? Our human moderators
  will review this post and take appropriate action.`,
}) => {
  const [text, setText] = useState('');

  const handleDone = () => {
    onDone(text);
    setText('');
  };

  return (
    <Modal
      animationType="slide"
      transparent={true}
      visible={visible}
      onRequestClose={() => onClose('')}>
      <View
        style={{
          width: '90%',
          height: '100%',
          justifyContent: 'center',
          alignSelf: 'center',
        }}>
        <View
          style={{
            backgroundColor: 'white',
            padding: 20,
            borderRadius: 10,
            elevation: 5,
          }}>
          <Text
            style={{
              fontFamily: Fonts.type.bold,
              fontSize: 20,
              textAlign: 'center',
              paddingBottom: 8,
            }}>
            Are You Sure?
          </Text>
          <Text
            style={{
              fontFamily: Fonts.type.semi_bold,
              textAlign: 'center',
              fontSize: 14,
            }}>
            {description}
          </Text>
          <TextInput
            multiline={true}
            numberOfLines={4}
            onChangeText={setText}
            value={text}
            placeholder={placeHolder}
            placeholderTextColor="#999" // Optional: customize the placeholder text color
            style={{
              borderWidth: 1,
              borderColor: 'black',
              marginTop: 10,
              paddingTop: 10,
              height: 100, // Adjust height as needed
              padding: 10,
              fontFamily: Fonts.type.semi_bold,
            }}
          />
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'center',
              marginTop: 20,
            }}>
            <Pressable onPress={handleDone} style={styles.doneButton}>
              <Text style={{color: 'white', fontFamily: Fonts.type.bold}}>
                Done
              </Text>
            </Pressable>
            <View style={{width: 20}} />
            <Pressable onPress={onClose} style={styles.closeButton}>
              <Text style={{color: 'black', fontFamily: Fonts.type.bold}}>
                Cancel
              </Text>
            </Pressable>
          </View>
        </View>
      </View>
    </Modal>
  );
};

export default TextInputAlert;
