import React, {useEffect, useState} from 'react';
import {
  View,
  Pressable,
  Text,
  PermissionsAndroid,
  Platform,
} from 'react-native';
import styles from './styles.ts';
import {
  ATTACHMENT_ICON,
  FILE_ICON,
  MINIMIZE_ICON,
  RUDI_CROSS,
  RUDI_ICON,
  RUDI_SEND_BUTTON,
} from '../../constants/AssetSVGConstants.ts';
import CustomNavbar from '../CustomNavbar/index.tsx';
import Metrics from '../../theme/Metrics.ts';
import {connect} from 'react-redux';
import {
  getRudiMessagesRequest,
  getRudiFileRequest,
  sendRudiMessageRequest,
  uploadRudiFileRequest,
} from '../../actions/RudiActions.ts';
import {
  Message,
  RudiFileType,
  RudiMessageType,
  UserInfo,
} from '../../types/index.tsx';
import {
  Bubble,
  BubbleProps,
  GiftedChat,
  IMessage,
  InputToolbar,
  InputToolbarProps,
  Send,
  SendProps,
  Time,
} from 'react-native-gifted-chat';
import util from '../../util/index.tsx';
import Colors from '../../theme/Colors.ts';
import RNFS from 'react-native-fs';
import Loader from '../Loader/index.tsx';
import * as DocumentPicker from 'react-native-document-picker';
import Share from 'react-native-share';
import {showToastMsg} from '../Alert/index.tsx';
import {useIsFocused} from '@react-navigation/native';
import {Keyboard, KeyboardEvent} from 'react-native';
import {
  RUDI_MESSAGE,
  TOAST_MESSAGES,
  TOAST_TYPE,
} from '../../constants/StringConstants.ts';
import RenderHtml from 'react-native-render-html';

interface RudiMessageProps {
  getRudiMessagesRequest: (payload: any, callback: (res: any) => void) => void;
  getRudiFileRequest: (callback: (res: any) => void) => void;
  sendRudiMessageRequest: (payload: any, callback: (res: any) => void) => void;
  uploadRudiFileRequest: (payload: any, callback: (res: any) => void) => void;
  rudiMessages: RudiMessageType;
  user: UserInfo;
}

const RudiMessage: React.FC<RudiMessageProps> = ({
  getRudiMessagesRequest,
  getRudiFileRequest,
  sendRudiMessageRequest,
  uploadRudiFileRequest,
  rudiMessages,
  user,
}) => {
  const [rudiMessageSelected, setRudiMessageSelected] = useState(false);
  const [messages, setMessages] = useState<IMessage[]>([]);
  const [rudiFile, setRudiFile] = useState<RudiFileType>();
  const [loading, setLoading] = useState<boolean>(false);
  const [page, setPage] = useState<number>(1);
  const isFocused = useIsFocused();
  const [isKeyboardOpen, setIsKeyboardOpen] = useState(false);

  const onPress = () => {
    setRudiMessageSelected(!rudiMessageSelected);
  };

  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      'keyboardDidShow',
      handleKeyboardDidShow,
    );
    const keyboardDidHideListener = Keyboard.addListener(
      'keyboardDidHide',
      handleKeyboardDidHide,
    );

    return () => {
      keyboardDidShowListener.remove();
      keyboardDidHideListener.remove();
    };
  }, []);

  const handleKeyboardDidShow = (event: KeyboardEvent) => {
    setIsKeyboardOpen(true);
  };

  const handleKeyboardDidHide = (event: KeyboardEvent) => {
    setIsKeyboardOpen(false);
  };

  useEffect(() => {
    if (!isFocused) {
      setPage(1);
      setRudiMessageSelected(false);
    }
  }, [isFocused]);

  useEffect(() => {
    if (rudiMessageSelected) {
      const payLoad = {
        page: page,
      };
      getRudiMessagesRequest(payLoad, (res: RudiMessageType) => {
        if (res) {
          if (page > 1) {
            const updatedResponse: IMessage[] = util.transformRudiMessages(
              res.messages,
              user,
            );
            setMessages([...messages, ...updatedResponse]);
          } else {
            const updatedResponse: IMessage[] = util.transformRudiMessages(
              res.messages,
              user,
            );
            setMessages(updatedResponse);
          }
        }
      });
      if (rudiFile === undefined) {
        getRudiFileRequest((res: RudiFileType) => {
          setRudiFile(res);
        });
      }
    }
  }, [rudiMessageSelected, page]);

  const renderBubble = (props: any) => {
    return (
      <Bubble
        {...props}
        textStyle={{
          left: styles.textLeftStyle,
          right: styles.textRightStyle,
        }}
        customTextStyle={styles.combineTextStyle}
        wrapperStyle={{
          right: styles.chatBubbleRightStyle,
          left: styles.chatBubbleLeftStyle,
        }}
      />
    );
  };

  const renderTime = (props: any) => {
    return (
      <Time
        {...props}
        timeTextStyle={{
          left: styles.timeText,
          right: styles.timeText,
        }}
      />
    );
  };

  const checkPermission = async () => {
    if (Platform.OS === 'ios') {
      return true;
    }
    const granted = await PermissionsAndroid.request(
      PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE,
      {
        title: RUDI_MESSAGE.PERMISSION_TITLE,
        message: RUDI_MESSAGE.PERMISSION_MESSAGE,
        buttonPositive: RUDI_MESSAGE.PERMISSION_BUTTON,
      },
    );
    return granted === PermissionsAndroid.RESULTS.GRANTED;
  };

  const getDownloadDirectoryPath = () => {
    if (Platform.OS === 'ios') {
      return RNFS.DocumentDirectoryPath;
    } else {
      return RNFS.DownloadDirectoryPath || RNFS.DocumentDirectoryPath;
    }
  };

  const downloadFile = async () => {
    const url = rudiFile?.fileUrl ?? '';
    const downloadDir = getDownloadDirectoryPath();
    const destination = `${downloadDir}/${rudiFile?.fileName}`;
    if (await checkPermission()) {
      RNFS.downloadFile({
        fromUrl: url,
        toFile: destination,
      })
        .promise.then(res => {
          if (res.statusCode === 200) {
            saveToFile(destination);
          } else {
            showToastMsg(RUDI_MESSAGE.ERROR_DOWNLOADING);
          }
        })
        .catch(error => {
          showToastMsg(RUDI_MESSAGE.ERROR_DOWNLOADING);
        });
    } else {
      showToastMsg(RUDI_MESSAGE.PERMISSION_REQUIRED);
    }
  };

  const saveToFile = (filePath: string) => {
    const shareOptions = {
      title: RUDI_MESSAGE.SAVE_FILE_TO,
      url: `file://${filePath}`,
      type: rudiFile?.fileType,
      saveToFiles: true,
    };
    Share.open(shareOptions);
  };

  const renderSend = (props: any) => {
    return (
      <Send {...props} containerStyle={styles.sendIcon}>
        <View style={styles.sendContainer}>
          <View>
            <RUDI_SEND_BUTTON />
          </View>
          <Pressable style={styles.send} onPress={() => pickAndUploadFile()}>
            <ATTACHMENT_ICON width={30} height={30} />
          </Pressable>
        </View>
      </Send>
    );
  };

  const pickAndUploadFile = () => {
    DocumentPicker.pick({
      type: [
        DocumentPicker.types.doc,
        DocumentPicker.types.docx,
        DocumentPicker.types.pdf,
        DocumentPicker.types.plainText,
        DocumentPicker.types.ppt,
        DocumentPicker.types.pptx,
      ],
    })
      .then(res => {
        const file = res[0];
        const formData = new FormData();
        formData.append('file', {
          uri: file.uri,
          type: file.type,
          name: file.name,
        });

        setLoading(true);
        uploadRudiFileRequest(formData, (res: any) => {
          if (res) {
            setRudiFile(res);
            showToastMsg(TOAST_MESSAGES.FILE_UPLOADED, TOAST_TYPE.SUCCESS);
          }
          setLoading(false);
        });
      })
      .catch(error => {});
  };

  const renderInputToolbar = (props: any) => {
    return (
      <InputToolbar
        {...props}
        containerStyle={styles.inputContainer}
        placeholderTextColor={Colors.text.placeHolderTextColor}
      />
    );
  };

  const onSend = (message: any) => {
    setMessages(previousMessages =>
      GiftedChat.append(previousMessages, message),
    );

    Keyboard.dismiss();

    setLoading(true);
    let messageObjectArray: any[] = [];

    if (rudiMessages.messages.length > 20) {
      rudiMessages.messages.slice(-20).forEach((message: Message) => {
        const newMessage = {
          content: message.content,
          role: message.senderType,
        };
        messageObjectArray.push(newMessage);
      });
    } else {
      rudiMessages.messages.forEach((message: Message) => {
        const newMessage = {
          content: message.content,
          role: message.senderType,
        };
        messageObjectArray.push(newMessage);
      });
    }

    const newMessageToAppend = {
      content: message[0].text,
      role: 'user',
    };

    messageObjectArray.push(newMessageToAppend);

    const payload = {
      ageBucket: user.ageBucket,
      messageObject: messageObjectArray,
      summary: rudiMessages.summary,
      fileSummary: rudiFile && rudiFile.fileSummary ? rudiFile.fileSummary : '',
      convId: rudiMessages.convId,
      collectionName: rudiMessages.collectionName,
      count: messageObjectArray.length - 1,
    };

    sendRudiMessageRequest(payload, (res: any) => {
      if (res) {
        const newMessages = [
          {
            _id: Math.random().toString(),
            text: res.rudiMessage,
            createdAt: new Date(),
            user: {
              _id: RUDI_MESSAGE.BOT_ID,
              name: RUDI_MESSAGE.BOT_NAME,
              avatar: '',
            },
          },
        ];

        setMessages(previousMessages =>
          GiftedChat.append(previousMessages, newMessages),
        );
        setTimeout(() => {
          setLoading(false);
        }, 1000);
      }
      setLoading(false);
    });
  };

  const renderFooterView = () => {
    return rudiFile ? (
      <Pressable
        style={[
          styles.footerContainer,
          isKeyboardOpen
            ? Platform.OS === 'android'
              ? {marginBottom: 80}
              : {marginBottom: 20}
            : {marginBottom: 80},
        ]}
        onPress={() => downloadFile()}>
        <View style={styles.uploadedFileContainer}>
          <View style={styles.uploadBar}></View>
          <Text style={styles.uploadText}>{RUDI_MESSAGE.UPLOADED_FILE}</Text>
          <View style={styles.uploadBar}></View>
        </View>
        <View style={styles.file}>
          <FILE_ICON width={26} height={26} />
          <Text style={styles.fileText}>{RUDI_MESSAGE.FILE_DETAIL}</Text>
        </View>
      </Pressable>
    ) : (
      <></>
    );
  };

  const renderMessageText = (props: {currentMessage: any}) => {
    const {currentMessage} = props;

    return (
      <View style={styles.combineTextStyle}>
        <RenderHtml
          source={{html: currentMessage.text}}
          contentWidth={Metrics.screenWidth}
        />
      </View>
    );
  };

  return (
    <View
      style={[
        styles.mainContainer,
        {
          height: rudiMessageSelected ? Metrics.screenHeight : 0,
          backgroundColor: rudiMessageSelected
            ? 'rgba(0, 0, 0, 0.5)'
            : 'transparent',
        },
      ]}>
      <Pressable style={styles.rudiIcon} onPress={() => onPress()}>
        {rudiMessageSelected ? (
          <RUDI_CROSS width={70} height={70} />
        ) : (
          <RUDI_ICON width={70} height={70} />
        )}
      </Pressable>
      {rudiMessageSelected ? (
        <View style={styles.container}>
          <CustomNavbar
            style={styles.navBar}
            hasTitleWIthImage
            hasBack={false}
            hasMultiRight={true}
            rightBtnImage1={<MINIMIZE_ICON />}
            rightBtnPress1={() => setRudiMessageSelected(false)}
          />
          <View style={styles.seperator}></View>
          <GiftedChat
            maxComposerHeight={Platform.OS === 'ios' ? 80 : 60}
            textInputProps={styles.textInputProps}
            renderTime={renderTime}
            listViewProps={{
              scrollEventThrottle: 600,
              onEndReachedThreshold: 0.1,
              onEndReached: () => {
                if (messages.length < rudiMessages?.totalMessageCount) {
                  setPage(page + 1);
                }
              },
            }}
            messages={messages}
            onSend={(messages: never[] | IMessage[]) => onSend(messages)}
            user={{
              _id: user.userId,
              name: user.displayName,
              avatar: user.avatarUrl,
            }}
            inverted={true}
            renderSend={(props: SendProps<IMessage>) => renderSend(props)}
            renderInputToolbar={(props: InputToolbarProps<IMessage>) =>
              renderInputToolbar(props)
            }
            showAvatarForEveryMessage={true}
            alwaysShowSend
            renderUsernameOnMessage={true}
            renderBubble={(props: BubbleProps<IMessage>) => renderBubble(props)}
            renderFooter={() => renderFooterView()}
            renderAvatarOnTop
            bottomOffset={Platform.OS === 'ios' ? 140 : 0}
            renderMessageText={renderMessageText}
          />
          <Loader loading={loading} />
        </View>
      ) : null}
    </View>
  );
};

const mapStateToProps = (state: any) => ({
  rudiMessages: state.rudi.rudiMessage,
  user: state.user.userInfo,
});

export default connect(mapStateToProps, {
  getRudiMessagesRequest,
  getRudiFileRequest,
  sendRudiMessageRequest,
  uploadRudiFileRequest,
})(RudiMessage);
