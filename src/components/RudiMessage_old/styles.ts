import {StyleSheet} from 'react-native';
import {Colors, Fonts, Metrics} from '../../theme';

// @flow

export default StyleSheet.create({
  mainContainer: {
    position: 'absolute',
    width: Metrics.ratio(Metrics.screenWidth),
  },
  rudiIcon: {
    width: Metrics.ratio(78),
    height: Metrics.ratio(78),
    top: Metrics.ratio(Metrics.screenHeight - 202),
    left: Metrics.ratio(Metrics.screenWidth - 90),
  },
  container: {
    width: Metrics.ratio(Metrics.screenWidth - 40),
    right: Metrics.ratio(20),
    left: Metrics.ratio(20),
    height: Metrics.ratio(Metrics.screenHeight - 380),
    borderTopLeftRadius: Metrics.ratio(20),
    borderTopRightRadius: Metrics.ratio(20),
    borderBottomLeftRadius: Metrics.ratio(20),
    marginTop: Metrics.ratio(90),
    backgroundColor: Colors.white,
  },
  messageHeader: {
    borderTopLeftRadius: Metrics.ratio(20),
    borderTopRightRadius: Metrics.ratio(20),
    height: Metrics.ratio(70),
  },
  seperator: {
    height: Metrics.ratio(1),
    backgroundColor: Colors.background.publishInactiveColor,
  },
  textInputProps: {
    marginRight: Metrics.ratio(100),
    left: Metrics.ratio(40),
    justifyContent: 'space-between',
    fontSize: Fonts.size.xSmall,
    backgroundColor: Colors.white,
    color: Colors.text.placeHolderTextColor,
    paddingTop: Metrics.ratio(12),
    paddingBottom: Metrics.ratio(12),
    paddingLeft: Metrics.ratio(20),
    minHeight: Metrics.ratio(44),
    maxHeight: Metrics.ratio(100),
    borderRadius: Metrics.ratio(48),
    borderWidth: Metrics.ratio(1.5),
    borderColor: Colors.textInputBorder,
  },
  textLeftStyle: {
    maxWidth: Metrics.ratio(Metrics.screenWidth * 0.6),
    backgroundColor: Colors.leftChatBubble,
    color: Colors.leftChatText,
  },
  textRightStyle: {
    fontFamily: Fonts.type.regular,
    color: Colors.rightChatText,
    marginHorizontal: Metrics.baseMargin,
  },
  combineTextStyle: {
    overflow: 'hidden',
    paddingHorizontal: Metrics.ratio(10),
    paddingVertical: Metrics.ratio(5),
  },
  chatBubbleRightStyle: {
    marginBottom: Metrics.ratio(10),
    backgroundColor: Colors.rightChatBubble,
    borderWidth: Metrics.ratio(0),
    borderColor: 'transparent',
  },
  chatBubbleLeftStyle: {
    overflow: 'hidden',
    marginBottom: Metrics.ratio(10),
    backgroundColor: Colors.leftChatBubble,
    borderWidth: Metrics.ratio(0),
    borderColor: 'transparent',
  },
  timeText: {
    color: Colors.text.placeHolderTextColor,
  },
  sendIcon: {
    borderRadius: Metrics.ratio(25),
    position: 'absolute',
    left: Metrics.ratio(Metrics.screenWidth * 0.77),
  },
  inputContainer: {
    fontSize: Fonts.size.large,
    justifyContent: 'center',
    bottom: 0,
    maxHeight: Metrics.ratio(200),
    // minHeight: 200,
    height: Metrics.ratio(108),
    shadowColor: Colors.black,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: Metrics.ratio(10.84),
    elevation: 5,
    borderBottomLeftRadius: Metrics.ratio(20),
    backgroundColor: Colors.rightChatBubble,
    borderColor: 'transparent',
  },
  sendContainer: {
    flexDirection: 'row',
  },
  send: {
    position: 'absolute',
    right: Metrics.ratio(Metrics.screenWidth * 0.78),
    alignSelf: 'center',
  },
  footerContainer: {
    alignItems: 'center',
  },
  uploadedFileContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingBottom: Metrics.ratio(10),
  },
  uploadBar: {
    height: Metrics.ratio(1),
    width: '20%',
    backgroundColor: Colors.background.primary,
  },
  uploadText: {
    paddingHorizontal: Metrics.ratio(20),
    color: Colors.background.primary,
  },
  file: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: Metrics.ratio(20),
  },
  fileText: {
    paddingHorizontal: Metrics.ratio(20),
  },
  navBar: {
    width: Metrics.screenWidth - 40,
    borderTopLeftRadius: Metrics.ratio(20),
    borderTopRightRadius: Metrics.ratio(20),
  },
});
