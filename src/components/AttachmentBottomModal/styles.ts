import {StyleSheet} from 'react-native';
import {Metrics, Colors} from '../../theme';

export const styles = StyleSheet.create({
  handleStyle: {
    backgroundColor: Colors.handle,
    height: Metrics.ratio(6),
    borderRadius: Metrics.ratio(8),
    width: Metrics.ratio(45),
  },
  bottomSheet: {
    elevation: 10,
    shadowColor: Colors.black,
    shadowOffset: {
      width: 0,
      height: Metrics.ratio(12) as number,
    },
    shadowOpacity: 0.58,
    shadowRadius: 16.0,
  },
  mediaButton: {
    height: Metrics.ratio(50),
    borderRadius: Metrics.ratio(6),
    backgroundColor: Colors.itemColors.itemBackgroundColor,
    alignItems: 'center',
    marginHorizontal: Metrics.ratio(24),
    flexDirection: 'row',
  },
  tagButton: {
    marginTop: Metrics.baseMargin,
    height: Metrics.ratio(50),
    borderRadius: Metrics.ratio(6),
    backgroundColor: Colors.itemColors.itemBackgroundColor,
    alignItems: 'center',
    marginHorizontal: Metrics.ratio(24),
    flexDirection: 'row',
    paddingLeft: Metrics.ratio(10),
  },
  verifyButtonStyle: {
    justifyContent: 'space-between',
    height: Metrics.ratio(50),
    borderRadius: Metrics.ratio(6),
    backgroundColor: Colors.itemColors.itemBackgroundColor,
    alignItems: 'center',
    marginHorizontal: Metrics.ratio(24),
    flexDirection: 'row',
    marginVertical: Metrics.baseMargin,
    paddingHorizontal: Metrics.ratio(10),
  },
  commonButtonStyle: {
    height: Metrics.ratio(50),
    borderRadius: Metrics.ratio(6),
    backgroundColor: Colors.itemColors.itemBackgroundColor,
    alignItems: 'center',
    marginHorizontal: Metrics.ratio(24),
    flexDirection: 'row',
    marginVertical: Metrics.baseMargin,
    paddingHorizontal: Metrics.ratio(10),
  },
  checkInButton: {
    height: Metrics.ratio(50),
    paddingHorizontal: Metrics.ratio(10),
    borderRadius: Metrics.ratio(6),
    backgroundColor: Colors.itemColors.itemBackgroundColor,
    alignItems: 'center',
    marginHorizontal: Metrics.ratio(24),
    flexDirection: 'row',
  },
  unSelectToggle: {
    backgroundColor: Colors.white,
    height: Metrics.ratio(22),
    width: Metrics.ratio(22),
    borderRadius: Metrics.ratio(100),
    borderWidth: Metrics.ratio(1),
    borderColor: Colors.gray,
  },
  selectedToggle: {borderWidth: Metrics.ratio(4), borderColor: Colors.black},
});
