// @flow
import {StyleSheet} from 'react-native';
import {Colors, Fonts, Metrics} from '../../theme';

export default StyleSheet.create({
  graph: {
    backgroundColor: Colors.white,
    alignSelf: 'center',
    paddingHorizontal: Metrics.ratio(10),
  },
  dotText: {
    fontFamily: Fonts.type.regular,
    fontSize: Metrics.ratio(10),
    color: Colors.black,
  },
  dotContainer: {
    height: Metrics.ratio(20),
    paddingHorizontal: Metrics.ratio(5),
    backgroundColor: Colors.background.publishInactiveColor,
    position: 'absolute',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: Metrics.ratio(6),
  },
});
