import {LineChart} from 'react-native-chart-kit';
import {Colors, Fonts, Metrics} from '../../theme';
import {getColor} from '../../helpers/AnalyticsHelper';
import styles from './styles';
import {Text, View} from 'react-native';
import {useEffect, useState} from 'react';

interface IGraphProps {
  valueData: any;
  optionSelected: string;
}

const Graph: React.FC<IGraphProps> = ({valueData, optionSelected}) => {
  const [dotIndex, setDotIndex] = useState<number>(-1);

  useEffect(() => {
    setDotIndex(-1);
  }, [optionSelected]);

  return (
    <LineChart
      onDataPointClick={value => setDotIndex(value.index)}
      renderDotContent={({x, y, index}) => {
        if (dotIndex > -1 && dotIndex === index) {
          return (
            <View
              style={[
                {
                  top: y - 36,
                  left: x - 25,
                },
                styles.dotContainer,
              ]}>
              <Text style={styles.dotText}>
                {optionSelected + ' : ' + valueData[index].count}
              </Text>
            </View>
          );
        } else {
          return null;
        }
      }}
      data={{
        labels: valueData.map((item: any) =>
          new Date(item.date).toLocaleString('default', {
            month: 'short',
          }),
        ),
        datasets: [
          {
            data: valueData.map((item: any) => item.count),
          },
          {
            data: [0],
            withDots: false,
          },
          {
            data: [10],
            withDots: false,
          },
        ],
      }}
      width={Metrics.screenWidth - 50}
      yLabelsOffset={20}
      segments={10}
      height={Metrics.screenWidth - 50}
      yAxisInterval={1}
      chartConfig={{
        strokeWidth: 2,
        fillShadowGradientToOpacity: 0,
        backgroundGradientFrom: Colors.white,
        backgroundGradientTo: Colors.white,
        useShadowColorFromDataset: false,
        decimalPlaces: 0,
        color: (opacity = 0) => getColor(optionSelected),
        labelColor: (opacity = 1) => `rgba(0, 0, 0, ${opacity})`,
        propsForBackgroundLines: {
          strokeDasharray: '',
          stroke: Colors.graphLines,
        },
        propsForDots: {
          r: '2',
          strokeWidth: '3',
          stroke: getColor(optionSelected),
          fill: getColor(optionSelected),
        },
        propsForLabels: {
          fontFamily: Fonts.type.semi_bold,
          fontSize: 10,
        },
      }}
      style={styles.graph}
    />
  );
};

export default Graph;
