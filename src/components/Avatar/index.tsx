// @flow
import React from 'react';
import PropTypes from 'prop-types';
import {ImageStyle, View, ViewStyle, Image} from 'react-native';
import {Text} from '..';
import styles from './styles';

interface AvatarProps {
  image: string;
  style?: ViewStyle;
  imageStyle?: ImageStyle;
}

const Avatar: React.FC<AvatarProps> = ({image, style, imageStyle}) => {
  return (
    <View style={[styles.profileContainer, style]}>
      <Image
        source={{uri: image}}
        style={[styles.profileImage, imageStyle]}
        resizeMode={'cover'}
      />
    </View>
  );
};

Avatar.propTypes = {
  image: PropTypes.string.isRequired,
  style: PropTypes.object,
  imageStyle: PropTypes.object,
};

Avatar.defaultProps = {
  image:
    'https://cdn.pixabay.com/photo/2016/08/08/09/17/avatar-1577909_960_720.png',
  style: styles.profileContainer,
  imageStyle: styles.profileImage,
};

export default Avatar;
