// @flow
import {StyleSheet} from 'react-native';
import {Colors, Metrics} from '../../theme';

export default StyleSheet.create({
  container: {
    flex: 1,
    //   backgroundColor: Colors.background.primary,
  },
  profileContainer: {
    marginTop: Metrics.doubleBaseMargin,
    alignSelf: 'center',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    overflow: 'hidden',
    height: Metrics.ratio(320, 320),
    width: Metrics.ratio(320, 320),
    borderRadius: Metrics.ratio(320 / 2),
    borderColor: Colors.white,
  },
  profileImage: {
    height: Metrics.ratio(320),
    width: Metrics.ratio(320),
    //  position: 'absolute',
  },
});
