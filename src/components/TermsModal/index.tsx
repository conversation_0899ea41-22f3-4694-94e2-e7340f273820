import React from 'react';
import {
  Modal,
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';
import {Colors} from '../../theme';
import {COMMON, EULA, SUBSCRIPTION} from '../../constants/StringConstants';

interface TermsModalProps {
  visible: boolean;
  onCancel: () => void;
  onAccept: () => void;
}

const TermsModal: React.FC<TermsModalProps> = ({
  visible,
  onCancel,
  onAccept,
}) => {
  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={onCancel}>
      <View style={styles.modalContainer}>
        <View style={styles.modalContent}>
          <ScrollView
            contentContainerStyle={{
              backgroundColor: Colors.lightGray1,
            }}>
            <Text style={styles.modalTitle}>{EULA.title}</Text>

            {EULA.content.map((item, index) => (
              <View key={index} style={styles.listItem}>
                <Text style={styles.modalText}>{item}</Text>
              </View>
            ))}
          </ScrollView>

          <View style={styles.modalButtons}>
            <TouchableOpacity
              style={[styles.button, styles.cancelButton]}
              onPress={onCancel}>
              <Text style={[styles.buttonText, {color: Colors.black}]}>
                {COMMON.CANCEL}
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.button, styles.acceptButton]}
              onPress={onAccept}>
              <Text style={styles.buttonText}>{COMMON.ACCEPT}</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    height: '70%',
    width: '80%',
    backgroundColor: Colors.white,
    borderRadius: 10,
    padding: 20,
    alignItems: 'center',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
    color: Colors.black,
  },
  modalText: {
    fontSize: 14,
    textAlign: 'left',
    marginBottom: 20,
    color: Colors.black,
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  button: {
    flex: 1,
    padding: 10,
    borderRadius: 5,
    alignItems: 'center',
    marginHorizontal: 5,
  },
  cancelButton: {
    backgroundColor: Colors.gray,
  },
  acceptButton: {
    backgroundColor: Colors.black,
  },
  buttonText: {
    color: Colors.white,
    fontWeight: 'bold',
  },
  listItem: {
    marginBottom: 12,
  },
});

export default TermsModal;
