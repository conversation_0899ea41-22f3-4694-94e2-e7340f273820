import React from 'react';
import {View, Image, Pressable} from 'react-native';
import styles from './styles';
import {Scores, UserInfo} from '../../types';
import {
  ADD_FRIEND,
  BUDDY_HOOD,
  WHITE_PLUS,
} from '../../constants/AssetSVGConstants';
import {AppStyles, Colors} from '../../theme';
import {ButtonView, Text} from '../../components';
import util from '../../util';
import {svgComponents} from '../../constants';

interface UserCoverDetailsProps {
  imageEdit?: () => void;
  user: UserInfo;
  image: string;
  scores: Scores;
  isOwnProfile?: boolean;
  onFollowPress?: () => void;
  showFollowButton?: boolean;
}

const UserCoverDetails: React.FC<UserCoverDetailsProps> = ({
  user,
  imageEdit,
  image,
  scores,
  isOwnProfile,
  onFollowPress,
  showFollowButton,
}) => {
  return (
    <View style={styles.container}>
      <View style={styles.topBorder} />
      <Pressable onPress={imageEdit}>
        <View style={styles.profileImage}>
          <Image source={{uri: image}} style={styles.image} />
        </View>
        {imageEdit ? (
          <View style={styles.plus}>
            <WHITE_PLUS />
          </View>
        ) : (
          showFollowButton && (
            <ButtonView style={styles.FriendAdd} onPress={onFollowPress}>
              <ADD_FRIEND width={35} height={35} />
            </ButtonView>
          )
        )}
      </Pressable>
      <View style={styles.voucherContainer}>
        <Text style={[styles.name, !imageEdit && {marginTop: -10}]}>
          {user.displayName}
        </Text>
        {isOwnProfile && user?.discountApplied?.availedBHDiscount ? (
          <BUDDY_HOOD />
        ) : null}
      </View>

      <Text style={styles.userName}>{user.userName}</Text>

      <View style={styles.svgContainer}>
        {Object.entries(scores)
          .sort(([, a], [, b]) => b - a)
          .slice(0, 2)
          .map(([key, value]) => {
            const SvgComponent = svgComponents[key.toLowerCase()];
            if (SvgComponent) {
              return (
                <View key={key} style={styles.reactionContainer}>
                  <SvgComponent height={25} width={25} />
                  <Text
                    color={Colors.black}
                    size={'xSmall'}
                    style={AppStyles.mLeft5}>
                    {util.capitalizeFirstLetter(key)}
                  </Text>
                </View>
              );
            }
            return null;
          })}
      </View>
    </View>
  );
};

export default UserCoverDetails;
