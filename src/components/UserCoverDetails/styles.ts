import {StyleSheet} from 'react-native';
import {Colors, Fonts, Metrics} from '../../theme';

export default StyleSheet.create({
  container: {
    paddingHorizontal: Metrics.ratio(20),
    backgroundColor: 'white',
    borderBottomLeftRadius: Metrics.ratio(21.5),
    borderBottomRightRadius: Metrics.ratio(21.5),
    paddingBottom: Metrics.ratio(16),
    alignItems: 'center',
  },
  topBorder: {
    height: Metrics.ratio(1),
    backgroundColor: Colors.gray,
    width: Metrics.screenWidth * 0.9,
    alignSelf: 'center',
  },
  cover: {
    alignItems: 'center',
    flex: 1,
    overflow: 'hidden',
    borderBottomLeftRadius: Metrics.ratio(32),
    borderBottomRightRadius: Metrics.ratio(32),
  },
  profileImage: {
    marginVertical: Metrics.baseMargin,
    height: Metrics.ratio(140),
    width: Metrics.ratio(140),
    borderRadius: Metrics.ratio(100),
    borderWidth: Metrics.ratio(5),
    borderColor: Colors.gray,
    justifyContent: 'center',
    alignItems: 'center',
  },
  plus: {
    width: Metrics.ratio(40),
    height: Metrics.ratio(40),
    backgroundColor: 'black',
    marginLeft: Metrics.ratio(100),
    borderRadius: Metrics.ratio(100),
    borderWidth: Metrics.ratio(4),
    borderColor: Colors.gray,
    marginTop: Metrics.ratio(-50),
    alignItems: 'center',
    justifyContent: 'center',
  },
  FriendAdd: {
    width: Metrics.ratio(40),
    height: Metrics.ratio(40),
    backgroundColor: 'black',
    marginLeft: Metrics.ratio(100),
    borderRadius: Metrics.ratio(100),
    borderWidth: Metrics.ratio(4),
    borderColor: Colors.gray,
    marginTop: Metrics.ratio(-50),
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: Metrics.ratio(10),
  },
  name: {
    fontFamily: Fonts.type.semi_bold,
    fontSize: Metrics.ratio(32),
    color: Colors.text.homeTitleColor,
    marginLeft: Metrics.ratio(24),
  },
  userName: {
    color: Colors.text.placeHolderTextColor,
    fontSize: Fonts.size.normal,
    marginTop: Metrics.ratio(-5),
    marginBottom: Metrics.ratio(10),
  },
  occupation: {
    fontFamily: Fonts.type.medium,
    fontSize: Metrics.ratio(16),
    color: Colors.textLight,
    marginLeft: Metrics.ratio(24),
  },
  title: {
    fontSize: Metrics.ratio(24),
    fontWeight: 'bold',
    marginBottom: Metrics.ratio(16),
  },
  image: {
    height: Metrics.ratio(130),
    width: Metrics.ratio(130),
    borderRadius: Metrics.ratio(100),
  },
  animatedView: {
    alignItems: 'center',
    flexDirection: 'row',
  },
  imageContainer: {
    flex: 1,
    alignItems: 'center',
  },
  textContainer: {
    width: Metrics.ratio(105),
    right: Metrics.ratio(0),
    position: 'absolute',
  },
  reactionContainer: {
    paddingHorizontal: Metrics.ratio(10),
    paddingVertical: Metrics.ratio(2),
    marginLeft: Metrics.ratio(10),
    flexDirection: 'row',
    backgroundColor: Colors.symbolBackground,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: Metrics.ratio(100),
  },

  svgContainer: {
    alignSelf: 'center',
    flexDirection: 'row',
    alignItems: 'center',

    borderRadius: Metrics.ratio(16),
  },
  voucherContainer: {flexDirection: 'row', alignItems: 'center'},
});
