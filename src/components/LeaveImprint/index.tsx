import {styles} from './styles';
import {Text} from '../';
import {ButtonView} from '..';
import {
  ATTACHMENT_POST,
  LOCATION_POST,
  MICRO_PHONE_POST,
} from '../../constants/AssetSVGConstants';
import {AppStyles, Colors, Metrics} from '../../theme';
import {HOME} from '../../constants/StringConstants';
import {View} from 'react-native';

interface imprintLeaveBoxProps {
  onLeaveBoxPress: () => void;
}

const LeaveImprint: React.FC<imprintLeaveBoxProps> = ({onLeaveBoxPress}) => {
  return (
    <ButtonView style={styles.container} onPress={onLeaveBoxPress}>
      <Text
        color={Colors.text.leaveImprintColor}
        size={'normal'}
        type="regular">
        {HOME.LEAVE_IMPRINT}
      </Text>

      <View style={styles.leaveContainer}>
        <ATTACHMENT_POST />
        <View style={AppStyles.flexRow}>
          <LOCATION_POST />
          <View style={AppStyles.mLeft10}>
            <MICRO_PHONE_POST />
          </View>
        </View>
      </View>
    </ButtonView>
  );
};

export default LeaveImprint;
