import React from 'react';
import {View, TextInput, ActivityIndicator, ViewStyle} from 'react-native';
import {Colors, AppStyles} from '../../theme';
import styles from './styles';
import {SEARCH_BAR_ICON} from '../../constants/AssetSVGConstants';

interface Props {
  containerStyle?: ViewStyle;
  placeholder?: string;
  onSearchText?: (text: string) => void;
  isSearching?: boolean;
  autoFocus: boolean;
  value?: string;
}

const defaultProps: Props = {
  placeholder: 'Search',
  onSearchText: () => {},
  isSearching: false,
  autoFocus: false,
  value: '',
};

const SearchBar: React.FC<Props> = ({
  placeholder,
  isSearching,
  onSearchText,
  autoFocus,
  containerStyle,
  value,
  ...rest
}) => {
  return (
    <View style={[styles.container, containerStyle]}>
      <View style={styles.searchWrapper}>
        <SEARCH_BAR_ICON />
        <TextInput
          autoFocus={autoFocus}
          placeholder={placeholder}
          placeholderTextColor={Colors.text.placeHolderTextColor}
          style={styles.textInput}
          returnKeyType="search"
          value={value}
          onChangeText={text => {
            if (onSearchText) onSearchText(text);
          }}
          {...rest}
        />
        {isSearching && (
          <ActivityIndicator
            size="small"
            color={Colors.white}
            style={AppStyles.mRight10}
          />
        )}
      </View>
    </View>
  );
};

SearchBar.defaultProps = defaultProps;

export default SearchBar;
