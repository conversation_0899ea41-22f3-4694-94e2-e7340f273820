// @flow
import {StyleSheet} from 'react-native';
import {Colors, Metrics, AppStyles, Fonts} from '../../theme';
import Util from '../../util';

export default StyleSheet.create({
  container: {
    backgroundColor: Colors.white,
    marginHorizontal: Metrics.baseMargin,
    paddingHorizontal: Metrics.baseMargin,
    borderWidth: Metrics.ratio(1),
    borderColor: Colors.borderColor,
  },
  searchWrapper: {
    alignSelf: 'center',
    borderRadius: Metrics.borderRadius,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },

  textInput: {
    marginLeft: Metrics.ratio(10),
    fontSize: Fonts.size.xSmall,
    fontFamily: Fonts.type.base,
    ...AppStyles.flex,
    ...AppStyles.pRight10,
    height: Util.isPlatformAndroid() ? Metrics.ratio(40) : Metrics.ratio(50),

    ...AppStyles.pTop0,
    ...AppStyles.pBottom0,
    color: Colors.black,
  },
});
