import {TextInput, View, Text, Image, Pressable} from 'react-native';
import {styles} from './styles';
import {AppButton} from '../..';
import {Colors, Metrics} from '../../../theme';
import {PROFILE_SETUP, SIGN_UP} from '../../../constants/StringConstants';
import {TICK_BLUE, TICK_ICON_NEXT} from '../../../constants/AssetSVGConstants';
import React from 'react';
import {
  PRIVACY_POLICY_LINK,
  PRIVACY_POLICY_LINK_LIVE,
  TERMS_AND_CONDITIONS_LINK,
  TERMS_AND_CONDITIONS_LINK_LIVE,
} from '../../../constants';
import Routes from '../../../constants/RouteConstants';
import {BASE_URL} from '../../../config/WebService';

interface IBio {
  setAbout: (val: string) => void;
  about: string;
  onRegisterPressed: () => void;
  isGoogleLogin?: boolean;
  navigation: any;
  checkClicked?: boolean;
  onCheckClickedChange?: (val: boolean) => void;
}

const Bio: React.FC<IBio> = ({
  setAbout,
  about,
  onRegisterPressed,
  isGoogleLogin,
  navigation,
  checkClicked,
  onCheckClickedChange,
}) => {
  // const [checkClicked, setCheckClicked] = React.useState<boolean>(false);

  const handleCheckClick = () => {
    onCheckClickedChange && onCheckClickedChange(!checkClicked); // Toggle and notify parent
  };

  return (
    <View style={{marginHorizontal: Metrics.ratio(40)}}>
      <Text style={styles.text}>{PROFILE_SETUP.ABOUT}</Text>
      <View style={styles.container}>
        <TextInput
          style={styles.input}
          placeholder={SIGN_UP.BIO_PLACEHOLDER}
          placeholderTextColor={Colors.text.gray}
          multiline
          onChangeText={val => setAbout(val)}
          value={about}
        />
      </View>
      {isGoogleLogin && (
        <View style={styles.above18Container}>
          <Pressable onPress={() => handleCheckClick()}>
            {checkClicked ? (
              <TICK_BLUE width={20} height={20} />
            ) : (
              <TICK_ICON_NEXT width={20} height={20} />
            )}
          </Pressable>
          <Text style={styles.above18Text}>
            {SIGN_UP.I_AGREE}
            <Text
              style={styles.linkText}
              onPress={() =>
                (
                  navigation.navigate as (
                    route: string,
                    params: {url: string; title: string},
                  ) => void
                )(Routes.REPORT_SCREEN, {
                  url: BASE_URL.includes('sigma')
                    ? PRIVACY_POLICY_LINK
                    : PRIVACY_POLICY_LINK_LIVE,
                  title: 'Privacy Policy',
                })
              }>
              {SIGN_UP.PRIVACY_POLICY}
            </Text>{' '}
            and{' '}
            <Text
              style={styles.linkText}
              onPress={() =>
                (
                  navigation.navigate as (
                    route: string,
                    params: {url: string; title: string},
                  ) => void
                )(Routes.REPORT_SCREEN, {
                  url: BASE_URL.includes('sigma')
                    ? TERMS_AND_CONDITIONS_LINK
                    : TERMS_AND_CONDITIONS_LINK_LIVE,
                  title: 'Terms of Service',
                })
              }>
              {SIGN_UP.TERMS_OF_SERVICE}
            </Text>
            .
          </Text>
        </View>
      )}
      <AppButton
        text={SIGN_UP.REGISTER}
        onPress={onRegisterPressed}
        textColor={Colors.text.white}
        buttonStye={styles.button}
      />
    </View>
  );
};

export default Bio;
