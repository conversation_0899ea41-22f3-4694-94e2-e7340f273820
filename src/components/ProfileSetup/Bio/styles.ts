import {StyleSheet} from 'react-native';
import {Colors, Fonts, Metrics} from '../../../theme';

export const styles = StyleSheet.create({
  input: {
    width: Metrics.ratio(321),
    height: Metrics.ratio(232),
    paddingTop: Metrics.ratio(18),
    paddingBottom: Metrics.ratio(18),
    paddingHorizontal: Metrics.ratio(12),
    color: Colors.black,
  },

  container: {
    marginTop: Metrics.ratio(-8),
    height: Metrics.ratio(232),
    borderRadius: Metrics.ratio(8),
    borderColor: Colors.borderColor,
    borderWidth: Metrics.ratio(2),
  },
  text: {
    zIndex: 1,
    width: Metrics.ratio(44),
    height: Metrics.ratio(18),
    marginLeft: Metrics.ratio(12),
    marginTop: Metrics.ratio(32),
    backgroundColor: Colors.white,
    textAlign: 'center',
    justifyContent: 'center',
  },
  button: {
    marginHorizontal: 0,
    marginTop: Metrics.ratio(30),
  },
  above18Container: {
    //  alignSelf: 'flex-start',
    flexDirection: 'row',
    paddingTop: Metrics.ratio(20),
  },

  above18Text: {
    // flex: 1,
    fontSize: 14,
    color: '#000',
    //  flexWrap: 'wrap',
    // flex: 1,
  },

  linkText: {
    fontSize: 13,
    fontWeight: 'bold',
    color: Colors.black, // or any link color
    textDecorationLine: 'underline',
  },
});
