import _ from 'lodash';
import React, {useRef, useState} from 'react';
import {AppButton, TextInput, Loader, Text} from '../../';
import {View, TextInput as RNTextInput, Pressable} from 'react-native';
import styles from './styles';
import {DATE_ICON, PROFILE_ICON} from '../../../constants/AssetSVGConstants';
import {Colors, Metrics} from '../../../theme';
import {PROFILE_SETUP, SIGN_UP} from '../../../constants/StringConstants';
import {UserInfo} from '../../../types';
import DatePicker from 'react-native-date-picker';
import util from '../../../util';
import PhoneInput from 'react-native-phone-number-input';

interface ActionsProps {
  personalInformation: UserInfo;
  nextButton: () => void;
  onBlur?: () => any;
  setUserName: (val: string) => void;
  setDisplayName: (val: string) => void;
  correct?: boolean;
  userName: string;
  onFocus?: () => any;
  displayName: string;
  dob: Date | undefined;
  setDob: (Date: Date) => void;
  showValue: boolean;
  phoneValue: string;
  setPhoneValue: (val: string) => void;
  valid: boolean;
  setValid: (isValid: boolean) => void;
  phoneError: string | null;
  setPhoneError: (error: string | null) => void;
  countryCode: string;
  setCountryCode: (val: string) => void;
}

export const PersonalInformation: React.FC<ActionsProps> = ({
  nextButton,
  setUserName,
  setDisplayName,
  correct,
  userName,
  onFocus,
  displayName,
  onBlur,
  personalInformation,
  dob,
  setDob,
  phoneValue,
  setPhoneValue,
  valid,
  setValid,
  phoneError,
  setPhoneError,
  countryCode,
  setCountryCode,
}) => {
  const userNameRef = useRef<RNTextInput>(null);
  const displayNameREf = useRef<RNTextInput>(null);
  const [open, setOpen] = useState(false);

  const phoneInput = useRef<PhoneInput>(null);

  const handlePhoneChange = (text: string) => {
    const isValid = phoneInput.current?.isValidNumber(text);
    setValid(isValid ? isValid : false);
    setPhoneValue(text);

    if (!isValid) {
      setPhoneError('Invalid phone number');
    } else {
      setPhoneError(null);
    }
  };

  return (
    <View style={styles.container}>
      <TextInput
        ref={userNameRef}
        containerStyle={styles.emailInputContainer}
        label={PROFILE_SETUP.USER_NAME}
        placeholder={PROFILE_SETUP.PLACEHOLDER}
        leftImage={<PROFILE_ICON />}
        onChangeText={val => setUserName(val)}
        value={userName}
        isCorrect={correct || false}
        onBlur={onBlur ? onBlur : () => {}}
        onFocus={onFocus ? onFocus : () => {}}
        onSubmitEditing={() => displayNameREf.current?.focus()}
      />
      <TextInput
        ref={displayNameREf}
        containerStyle={styles.passwordInputContainer}
        label={PROFILE_SETUP.DISPLAY_NAME}
        placeholder={PROFILE_SETUP.PLACEHOLDER}
        leftImage={<PROFILE_ICON />}
        value={displayName}
        onChangeText={val => setDisplayName(val)}
      />
      {!personalInformation?.profile?.dob && (
        <>
          <Pressable
            style={{
              marginHorizontal: Metrics.ratio(30),
              marginVertical: Metrics.ratio(10),
            }}
            onPress={() => setOpen(!open)}>
            <TextInput
              label={SIGN_UP.DOB_LABEL}
              placeholder={SIGN_UP.DOB_PLACEHOLDER}
              rightImage={<DATE_ICON />}
              value={dob ? util.getFormattedDate(dob) : ''}
              onPress={() => {
                setOpen(!open);
              }}
              editable={false}
              onPressIn={() => setOpen(!open)}
            />
          </Pressable>

          <DatePicker
            // maximumDate={util.getMinimumDate()}
            modal
            locale="en"
            mode="date"
            open={open}
            date={dob ? dob : new Date()}
            onConfirm={date => {
              setOpen(false);
              setDob(date);
            }}
            onCancel={() => {
              setOpen(false);
            }}
          />
        </>
      )}

      <View style={styles.phoneContainer}>
        <PhoneInput
          key={countryCode}
          ref={phoneInput}
          value={phoneValue}
          defaultCode={countryCode !== '' ? countryCode : 'US'}
          textInputStyle={styles.phoneTextInput}
          layout="first"
          onChangeText={handlePhoneChange}
          onChangeCountry={country => {
            setCountryCode(country.cca2); // or country.callingCode[0] for +1, +92, etc.
          }}
          // onChangeFormattedText={text => {
          //   setFormattedValue(text);
          // }}
          containerStyle={styles.phoneInputContainer}
        />
      </View>
      <AppButton
        buttonStye={{marginTop: Metrics.doubleBaseMargin}}
        text={PROFILE_SETUP.NEXT}
        onPress={nextButton}
        textColor={Colors.text.white}
      />
    </View>
  );
};
