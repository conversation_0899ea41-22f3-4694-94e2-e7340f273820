import {View} from 'react-native';
import {styles} from './styles';
import {AppButton, Avatar} from '../..';
import {Colors} from '../../../theme';
import {PROFILE_SETUP} from '../../../constants/StringConstants';
import {PLACEHOLDER_IMAGE} from '../../../constants';

interface IUploadImage {
  imageSource: string;
  nextButton: () => void;
  retakeButton: () => void;
}

const UploadedImage: React.FC<IUploadImage> = ({
  imageSource,
  nextButton,
  retakeButton,
}) => {
  return (
    <View style={styles.container}>
      <Avatar image={imageSource != '' ? imageSource : PLACEHOLDER_IMAGE} />
      <View style={styles.buttonContainer}>
        <AppButton
          buttonStye={styles.retakeButton}
          text={PROFILE_SETUP.RETAKE_PHOTO}
          onPress={retakeButton}
        />
        <AppButton
          buttonStye={styles.nextButton}
          text={PROFILE_SETUP.NEXT}
          textColor={Colors.white}
          onPress={nextButton}
        />
      </View>
    </View>
  );
};

export default UploadedImage;
