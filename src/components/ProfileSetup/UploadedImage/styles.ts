import {Dimensions, StyleSheet} from 'react-native';
import {Colors, Fonts, Metrics} from '../../../theme';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'space-between',
  },
  buttonContainer: {
    marginBottom: Metrics.ratio(10),
    width: Dimensions.get('window').width,
    flexDirection: 'row',
    alignSelf: 'center',
    justifyContent: 'space-evenly',
  },
  retakeButton: {
    width: Metrics.ratio(174),
    backgroundColor: Colors.white,
    borderColor: Colors.black,
    borderWidth: 1,
    borderRadius: Metrics.ratio(6),
    marginHorizontal: 0,
  },
  nextButton: {
    marginHorizontal: Metrics.ratio(0),
    width: Metrics.ratio(174),
  },
});
