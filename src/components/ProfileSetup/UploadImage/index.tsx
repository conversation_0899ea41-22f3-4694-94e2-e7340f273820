import {TextInput, View} from 'react-native';
import {styles} from './styles';
import {AppButton, ButtonView, CustomNavbar, Text} from '../..';
import {Colors, Metrics} from '../../../theme';
import {PROFILE_SETUP, SIGN_UP} from '../../../constants/StringConstants';
import {CAMERA_ICON, UPLOAD_ICON} from '../../../constants/AssetSVGConstants';
import Spacer from '../../Spacer';
import {hasNotch} from 'react-native-device-info';

interface IUploadImage {
  onCameraPress: () => void;
  onGalleryPress: () => void;
  showNavBar?: boolean;
  close?: () => void;
}

const UploadImage: React.FC<IUploadImage> = ({
  onCameraPress,
  onGalleryPress,
  showNavBar,
  close,
}) => {
  return (
    <View>
      {showNavBar && (
        <>
          <View
            style={
              hasNotch() ? styles.statusBar : styles.statusBarWithoutNotch
            }></View>
          <CustomNavbar
            title={'Upload Image'}
            titleSize={16}
            leftBtnPress={close}
            hasBack={true}
            hasMultiRight={true}
            rightBtnPress2={() => alert('messagePress')}
          />
        </>
      )}
      <View style={styles.uploadImageContainer}>
        <ButtonView style={{alignItems: 'center'}} onPress={onGalleryPress}>
          <UPLOAD_ICON />
          <Text
            color={Colors.black}
            type="semi_bold"
            textAlign="center"
            size={'medium'}
            style={{marginTop: Metrics.baseMargin}}>
            {PROFILE_SETUP.SELECT_PHOTO}
          </Text>
        </ButtonView>
      </View>
      <Spacer
        containerStyle={{marginHorizontal: 10}}
        lineStyle={{backgroundColor: '#C2CCD6'}}
      />
      <ButtonView style={styles.button} onPress={onCameraPress}>
        <View style={{marginRight: 10}}>
          <CAMERA_ICON />
        </View>
        <Text type="regular" size={'xxSmall'} color={Colors.black}>
          {PROFILE_SETUP.OPEN_CAMERA}
        </Text>
      </ButtonView>
    </View>
  );
};

export default UploadImage;
