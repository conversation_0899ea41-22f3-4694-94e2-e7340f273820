import {StyleSheet} from 'react-native';
import {Colors, Fonts, Metrics} from '../../../theme';
import {getStatusBarHeight} from 'react-native-iphone-x-helper';

export const styles = StyleSheet.create({
  uploadImageContainer: {
    borderRadius: 12,
    alignSelf: 'center',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#E6EAEE',
    width: Metrics.ratio(300),
    paddingVertical: Metrics.ratio(50, 50),
    marginVertical: Metrics.ratio(20),
    marginTop: Metrics.screenWidth / 2 - 140,
  },
  button: {
    paddingHorizontal: 10,
    width: Metrics.ratio(300),
    paddingVertical: 10,
    alignSelf: 'center',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#F3F6F6',
    flexDirection: 'row',
    borderRadius: 6,
  },
  nextButton: {
    marginHorizontal: Metrics.ratio(0),
    width: Metrics.ratio(300),
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: Metrics.ratio(40),
  },
  statusBar: {
    backgroundColor: Colors.white,
    height: getStatusBarHeight() + 30,
  },
  statusBarWithoutNotch: {
    backgroundColor: Colors.white,
    height: Metrics.ratio(30),
  },
});
