import React, {FunctionComponent} from 'react';
import {View, ViewStyle} from 'react-native';
import Text from '../Text'; // Assuming you have a separate Text component
import styles from './styles'; // Import styles if required
import {AppStyles, Colors} from '../../theme';
import {EMPTY_STATE} from '../../constants/AssetSVGConstants';
import {COMMON} from '../../constants/StringConstants';

interface EmptyStateTextProps {
  text?: string;
  containerStyle?: ViewStyle;
  textProps?: any;
}

const EmptyStateText: FunctionComponent<EmptyStateTextProps> = ({
  text = COMMON.EMPTY_STATE,
  containerStyle = {},
  textProps = {},
}) => {
  return (
    <View style={[styles.container, containerStyle]}>
      <EMPTY_STATE />
      <Text
        type="semi_bold"
        color={Colors.black}
        {...textProps}
        textAlign="center"
        style={AppStyles.mTop10}>
        {text}
      </Text>
    </View>
  );
};

export default EmptyStateText;
