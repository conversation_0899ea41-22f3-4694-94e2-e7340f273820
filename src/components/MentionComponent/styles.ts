// @flow
import {StyleSheet} from 'react-native';
import {Colors, Metrics, Fonts} from '../../theme';

export default StyleSheet.create({
  userinfoContainer: {
    backgroundColor: Colors.white,
    flexDirection: 'row',
    alignItems: 'center',
    padding: Metrics.baseMargin,
  },
  imageContainer: {
    marginTop: Metrics.ratio(0),
    height: Metrics.ratio(45),
    width: Metrics.ratio(45),
  },
  image: {
    height: Metrics.ratio(45),
    width: Metrics.ratio(45),
  },
  inputStyle: {
    color: Colors.tabsTextColor.activeTabColor,
    fontFamily: Fonts.type.regular,
    fontSize: Fonts.size.buttonText,
    marginHorizontal: Metrics.baseMargin,
  },
  mentionsText: {fontFamily: Fonts.type.bold, color: Colors.blue},
  hashtagContainer: {
    backgroundColor: Colors.gray,
    paddingHorizontal: Metrics.ratio(20),
    alignSelf: 'center',
  },
});
