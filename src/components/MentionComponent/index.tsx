import {Avatar, Text} from '../';
import {ButtonView} from '..';
import {AppStyles, Colors} from '../../theme';
import {View} from 'react-native';
import {FOLLOWERS, ImprintMentions, ITagObject} from '../../types';
import {FC, useEffect, useState} from 'react';
import styles from './styles';
import {
  MentionInput,
  MentionSuggestionsProps,
  mentionRegEx,
  replaceMentionValues,
} from 'react-native-controlled-mentions';
import {CREATE_IMPRINT} from '../../constants/StringConstants';

interface mentionsProps {
  getHashtagsList: (payload: any, callback: (res: any) => void) => void;
  postDescription: string;
  followersList: FOLLOWERS[];
  onChangePostDescription: (desc: string) => void;
  mentionsList: ImprintMentions[];
  setMentionsList: React.Dispatch<React.SetStateAction<ImprintMentions[]>>;
  onHashtagSelect: (hashtags: string[]) => void;
}

const MentionComponent: React.FC<mentionsProps> = ({
  postDescription,
  followersList,
  onChangePostDescription,
  mentionsList,
  setMentionsList,
  getHashtagsList,
  onHashtagSelect,
}) => {
  const [hashtagsList, setHashtagsList] = useState<any[]>([]);
  const [hashtagsFetched, setHashtagsFetched] = useState(false);
  const [selectedHashtags, setSelectedHashtags] = useState<string[]>([]);

  const handleSuggestionPress = (
    item: FOLLOWERS | any,
    trigger: '@' | '#',
    onSuggestionPress: (arg: {
      id: string;
      name: string;
      trigger: '@' | '#';
    }) => void,
  ) => {
    if (trigger === '@') {
      const mentionObject: ITagObject = {
        id: item.id,
        name: item.displayName,
        trigger: '@',
      };
      const newMentionList = [
        ...mentionsList,
        {userId: item.id, userName: item.userName},
      ];
      onSuggestionPress(mentionObject);
      setMentionsList(newMentionList);
    } else if (trigger === '#') {
      const hashtagObject = {
        id: item.id,
        name: item.hashtag.replace('#', ''),
        trigger: '#' as '@' | '#',
      };

      onSuggestionPress(hashtagObject);
      setSelectedHashtags(prev => {
        return prev.includes(item.hashtag) ? prev : [...prev, item.hashtag];
      });
      onHashtagSelect(selectedHashtags);
    }
  };

  const renderMentionSuggestions: FC<MentionSuggestionsProps> = ({
    keyword,
    onSuggestionPress,
  }) => {
    if (keyword == null) {
      return null;
    }

    return (
      <View>
        {followersList
          .filter(user =>
            user.displayName.toLowerCase().includes(keyword.toLowerCase()),
          )
          .map(user => (
            <ButtonView
              key={user.id}
              onPress={() =>
                handleSuggestionPress(user, '@', onSuggestionPress)
              }>
              <View style={styles.userinfoContainer}>
                <Avatar
                  image={user.avatarUrl}
                  style={styles.imageContainer}
                  imageStyle={styles.image}
                />
                <View style={AppStyles.alignStart}>
                  <Text
                    color={Colors.itemColors.titleColor}
                    textAlign="center"
                    style={AppStyles.mLeft10}
                    type="bold"
                    size={'buttonText'}>
                    {user.displayName}
                  </Text>
                  <Text
                    color={Colors.text.placeHolderTextColor}
                    style={AppStyles.mLeft10}
                    type="base"
                    size={'xSmall'}>
                    {user.userName ?? ''}
                  </Text>
                </View>
              </View>
            </ButtonView>
          ))}
      </View>
    );
  };

  const renderHashtagSuggestions: FC<MentionSuggestionsProps> = ({
    keyword,
    onSuggestionPress,
  }) => {
    if (keyword === null) {
      return null;
    }

    return (
      <View style={styles.hashtagContainer}>
        {hashtagsList
          .filter(item =>
            item.name.toLowerCase().includes(keyword?.toLowerCase()),
          )
          .map(tag => (
            <ButtonView
              key={tag.id}
              onPress={() =>
                handleSuggestionPress(tag, '#', onSuggestionPress)
              }>
              <Text
                color={Colors.itemColors.titleColor}
                textAlign="left"
                style={AppStyles.mBottom15}
                type="base"
                size={'buttonText'}>
                {tag.hashtag}
              </Text>
            </ButtonView>
          ))}
      </View>
    );
  };

  useEffect(() => {
    if (!hashtagsFetched) {
      getHashtagsList({}, res => {
        setHashtagsList(res);
        setHashtagsFetched(true);
      });
    }
  }, [hashtagsFetched]);

  return (
    <MentionInput
      maxLength={200}
      scrollEnabled
      value={replaceMentionValues(
        postDescription,
        ({name, trigger}) => `${trigger ?? '@'}${name}`,
      )}
      onChange={onChangePostDescription}
      partTypes={[
        {
          trigger: '@',
          pattern: mentionRegEx,
          renderSuggestions: renderMentionSuggestions,
          isInsertSpaceAfterMention: true,
          textStyle: styles.mentionsText,
          isBottomMentionSuggestionsRender: true,
        },
        {
          trigger: '#',
          pattern: /#(\w+)/,
          renderSuggestions: renderHashtagSuggestions,
          isInsertSpaceAfterMention: true,
          textStyle: styles.mentionsText,
          isBottomMentionSuggestionsRender: true,
        },
      ]}
      placeholder={CREATE_IMPRINT.LEAVE_IMPRINT}
      style={styles.inputStyle}
      placeholderTextColor={Colors.tabsTextColor.activeTabColor}
    />
  );
};

export default MentionComponent;
