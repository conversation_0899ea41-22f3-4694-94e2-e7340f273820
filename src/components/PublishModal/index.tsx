import React from 'react';
import {View} from 'react-native';
import {AppStyles, Colors} from '../../theme';
import {ButtonView, Text, BottomSheetModal, AppButton} from '..';

import {CREATE_IMPRINT} from '../../constants/StringConstants';
import styles from './styles';
import {LIFE_GLOBAL, LIFE_PERSONAL} from '../../constants/AssetSVGConstants';

interface BottomSheetModalProps {
  hideModal: () => void;
  onDonePressed: () => void;
  onCancelPress: () => void;
  personalTribe: boolean;
  togglePersonalButton: () => void;
  toggleGlobalButton: () => void;
  publishModal: boolean;
}

const PublishModal: React.FC<BottomSheetModalProps> = ({
  hideModal,
  onDonePressed,
  onCancelPress,
  personalTribe,
  publishModal,
  togglePersonalButton,
  toggleGlobalButton,
}) => {
  return (
    <BottomSheetModal
      customStyle={{height: '50%'}}
      visible={publishModal}
      onClose={hideModal}
      onBackdropPress={hideModal}>
      <View>
        <Text style={styles.title}>{CREATE_IMPRINT.PUBLISH_IMPRINT}</Text>
        <ButtonView style={styles.toggleButton} onPress={togglePersonalButton}>
          <View style={styles.toggleSubStyle}>
            <LIFE_PERSONAL height={32} width={32} />
            <Text
              color={Colors.text.titleColor}
              type="medium"
              size={'xSmall'}
              style={styles.buttonText}>
              {CREATE_IMPRINT.PERSONAL_TRIBE}
            </Text>
          </View>
          <View
            style={[
              styles.unSelectToggle,
              personalTribe ? styles.selectedToggle : {},
            ]}
          />
        </ButtonView>
        <ButtonView
          style={[styles.toggleButton, AppStyles.marginVerticalBase]}
          onPress={toggleGlobalButton}>
          <View style={styles.toggleSubStyle}>
            <LIFE_GLOBAL height={32} width={32} />

            <Text
              color={Colors.text.titleColor}
              type="medium"
              size={'xSmall'}
              style={styles.buttonText}>
              {CREATE_IMPRINT.GLOBAL_TRIBE}
            </Text>
          </View>
          <View
            style={[
              styles.unSelectToggle,
              !personalTribe ? styles.selectedToggle : {},
            ]}
          />
        </ButtonView>
        <AppButton
          text={CREATE_IMPRINT.PUBLISH}
          textColor={Colors.white}
          onPress={onDonePressed}
          buttonStye={styles.doneButton}
        />
        <AppButton
          text={CREATE_IMPRINT.CANCEL}
          textColor={Colors.black}
          onPress={onCancelPress}
          buttonStye={styles.cancelButton}
        />
      </View>
    </BottomSheetModal>
  );
};
export default PublishModal;
