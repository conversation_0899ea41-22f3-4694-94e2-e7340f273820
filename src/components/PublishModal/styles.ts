import {StyleSheet} from 'react-native';
import {Colors, Fonts, Metrics} from '../../theme';

export default StyleSheet.create({
  title: {
    fontFamily: Fonts.type.bold,
    fontSize: Metrics.ratio(22),
    lineHeight: Metrics.ratio(18),
    textAlign: 'center',
    zIndex: 999,
    color: Colors.black,
    paddingVertical: Metrics.ratio(16),
  },
  cancelButton: {
    borderColor: Colors.BlackButton,
    borderWidth: Metrics.ratio(1),
    backgroundColor: Colors.white,
    paddingHorizontal: Metrics.ratio(100),
    marginTop: Metrics.ratio(20),
  },
  doneButton: {
    paddingHorizontal: Metrics.ratio(100),
    marginTop: Metrics.ratio(20),
  },

  toggleButton: {
    alignItems: 'center',
    flexDirection: 'row',
    backgroundColor: Colors.itemColors.itemBackgroundColor,
    borderWidth: 1,
    borderColor: Colors.itemColors.itemBackgroundColor,
    borderRadius: Metrics.ratio(6),
    justifyContent: 'space-between',
    marginHorizontal: Metrics.baseMargin,
    paddingHorizontal: Metrics.baseMargin,
  },
  toggleSubStyle: {flexDirection: 'row', alignItems: 'center'},
  buttonText: {
    marginLeft: Metrics.ratio(10),
    paddingVertical: Metrics.ratio(23),
  },
  unSelectToggle: {
    backgroundColor: Colors.white,
    height: Metrics.ratio(22),
    width: Metrics.ratio(22),
    borderRadius: Metrics.ratio(100),
    borderWidth: Metrics.ratio(1),
    borderColor: Colors.gray,
  },
  selectedToggle: {borderWidth: Metrics.ratio(4), borderColor: Colors.black},
});
