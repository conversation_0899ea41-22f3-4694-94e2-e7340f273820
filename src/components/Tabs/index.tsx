import {View} from 'react-native';
import {styles} from './styles';
import {Text} from '../';
import {ButtonView} from '..';
import {Colors} from '../../theme';
import {HOME} from '../../constants/StringConstants';

interface TabsProps {
  onPersonalPress: () => void;
  onGlobalPress: () => void;
  isPersonalSelect: boolean;
}

const Tabs: React.FC<TabsProps> = ({
  onPersonalPress,
  onGlobalPress,
  isPersonalSelect,
}) => {
  const renderBar = () => {
    return <View style={styles.barStyle} />;
  };

  return (
    <View style={styles.container}>
      <ButtonView onPress={onPersonalPress} style={styles.tabChild}>
        <Text
          color={
            isPersonalSelect
              ? Colors.tabsTextColor.activeTabColor
              : Colors.tabsTextColor.inActiveTabColor
          }
          size={'normal'}
          type={isPersonalSelect ? 'semi_bold' : 'regular'}>
          {HOME.PERSONAL}
        </Text>
        {isPersonalSelect && renderBar()}
      </ButtonView>
      <ButtonView onPress={onGlobalPress} style={styles.tabChild}>
        <Text
          color={
            !isPersonalSelect
              ? Colors.tabsTextColor.activeTabColor
              : Colors.tabsTextColor.inActiveTabColor
          }
          size={'normal'}
          type={!isPersonalSelect ? 'semi_bold' : 'regular'}>
          {HOME.GLOBAL}
        </Text>
        {!isPersonalSelect && renderBar()}
      </ButtonView>
    </View>
  );
};

export default Tabs;
