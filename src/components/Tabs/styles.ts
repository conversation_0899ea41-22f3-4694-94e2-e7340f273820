import {StyleSheet} from 'react-native';
import {Colors, Metrics} from '../../theme';

export const styles = StyleSheet.create({
  container: {
    paddingTop: Metrics.ratio(10),
    alignItems: 'center',
    justifyContent: 'space-around',
    flexDirection: 'row',
    backgroundColor: Colors.white,
    borderBottomRightRadius: Metrics.ratio(24),
    borderBottomLeftRadius: Metrics.ratio(24),
  },
  barStyle: {
    height: 2,
    width: Metrics.ratio(67),
    position: 'absolute',
    borderRadius: Metrics.ratio(3),
    backgroundColor: '#393C4C',
    bottom: 0,
  },
  tabChild: {
    justifyContent: 'center',
    alignSelf: 'center',
    height: Metrics.ratio(52),
    alignItems: 'center',
    flex: 1,
  },
});
