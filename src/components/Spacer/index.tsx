import React from 'react';
import {View, Text, StyleSheet, ViewStyle} from 'react-native';
import styles from './styles.ts';

interface DividerWithTextProps {
  containerStyle?: ViewStyle;
  lineStyle?: ViewStyle;
}

const DividerWithText: React.FC<DividerWithTextProps> = ({
  containerStyle,
  lineStyle,
}) => {
  return (
    <View style={[styles.container, containerStyle]}>
      <View style={[styles.line, lineStyle]} />
      <View style={styles.textContainer}>
        <Text style={styles.text}>OR</Text>
      </View>
      <View style={[styles.line, lineStyle]} />
    </View>
  );
};

export default DividerWithText;
