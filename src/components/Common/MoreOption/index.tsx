import React, {memo} from 'react';
import {View, Text, Pressable} from 'react-native';
import {
  IMPRINT_MORE_OPTIONS,
  NOTIFICATION_TYPE,
} from '../../../constants/StringConstants';
import {
  ADD_FRIEND,
  HIDE_IMPRINT,
  MODAL_DELETE,
  REQUESTED_ICON,
} from '../../../constants/AssetSVGConstants';
import MoreOptionsBottomModal from '../../../components/TimeLine/MoreOptionBottomModal';
import styles from './styles';
import {AppButton} from '../..';
import Colors from '../../../theme/Colors';
import {ITimeLine} from '../../../types';

interface MoreOptionsBottomModalProps {
  visible: boolean;
  hideModal: () => void;
  onFollowPress: (userId: string) => void;
  userId: string;
  type?: string;
  onAccept?: () => void;
  onDecline?: () => void;
  onDeletePress?: (userId: string) => void;
  isSelfUser?: string;
  onBlockPress?: (userId: string) => void;
  onReportPress?: (userId: string) => void;
  onBookmarkPress?: (userId: string) => void;
  imprintData?: ITimeLine;
}

const RenderModal: React.FC<MoreOptionsBottomModalProps> = ({
  visible,
  hideModal,
  onFollowPress,
  userId,
  type,
  onAccept,
  onDecline,
  onDeletePress,
  isSelfUser,

  onBlockPress,
  onReportPress,
  onBookmarkPress,
  imprintData,
}) => {
  return (
    <MoreOptionsBottomModal
      visible={visible}
      onClose={hideModal}
      onBackdropPress={hideModal}>
      <View style={styles.modalParentContainer}>
        {isSelfUser !== imprintData?.userId && (
          <>
            {!imprintData?.isFollower && !imprintData?.followerRequestSent && (
              <Pressable
                style={styles.modalContainer}
                onPress={() => onFollowPress(userId)}>
                <ADD_FRIEND />
                <View style={styles.modalTextContainer}>
                  <Text style={styles.modalTextOne}>
                    {IMPRINT_MORE_OPTIONS.ADD_FRIEND}
                  </Text>
                  <Text style={styles.modalTextTwo}>
                    {IMPRINT_MORE_OPTIONS.ADD_FRIEND_SUBTITLE}
                  </Text>
                </View>
              </Pressable>
            )}
            {imprintData?.isFollower && (
              <>
                <Pressable
                  style={styles.modalContainer}
                  onPress={() => onBlockPress && onBlockPress(userId)}>
                  <HIDE_IMPRINT />
                  <View style={styles.modalTextContainer}>
                    <Text style={styles.modalTextOne}>
                      {IMPRINT_MORE_OPTIONS.BLOCK}
                    </Text>
                    <Text style={styles.modalTextTwo}>
                      {IMPRINT_MORE_OPTIONS.BLOCK_IMPRINTS_TEXT}
                    </Text>
                  </View>
                </Pressable>
              </>
            )}
          </>
        )}

        {type === NOTIFICATION_TYPE.MENTION_NOTIFICATION && (
          <View style={styles.headerContainer}>
            <Text style={styles.headerText}>
              {IMPRINT_MORE_OPTIONS.CONFIRM_TEXT}
            </Text>
            <View style={styles.buttonContainer}>
              <AppButton
                buttonStye={styles.confirmButton}
                text={IMPRINT_MORE_OPTIONS.ACCEPT}
                onPress={() => onAccept && onAccept()}
                textColor={Colors.white}
                type={'semi_bold'}
              />
              <AppButton
                buttonStye={styles.deleteButton}
                text={IMPRINT_MORE_OPTIONS.DECLINE}
                textColor={Colors.black}
                onPress={() => onDecline && onDecline()}
                type={'semi_bold'}
              />
            </View>
          </View>
        )}
        {isSelfUser == imprintData?.userId &&
          type !== NOTIFICATION_TYPE.MENTION_NOTIFICATION && (
            <Pressable
              style={styles.modalContainer}
              onPress={() => onDeletePress && onDeletePress(userId)}>
              <MODAL_DELETE />
              <View style={styles.modalTextContainer}>
                <Text style={styles.modalTextOne}>
                  {IMPRINT_MORE_OPTIONS.DELETE}
                </Text>
                <Text style={styles.modalTextTwo}>
                  {IMPRINT_MORE_OPTIONS.DELETE_IMPRINT_TEXT}
                </Text>
              </View>
            </Pressable>
          )}
      </View>
    </MoreOptionsBottomModal>
  );
};

export default memo(RenderModal);
