import {StyleSheet} from 'react-native';
import {Colors, Fonts, Metrics} from '../../../theme';

export default StyleSheet.create({
  modalParentContainer: {
    marginTop: 0,
  },
  modalContainer: {
    marginTop: Metrics.ratio(25),
    marginHorizontal: Metrics.ratio(40),
    flexDirection: 'row',
  },
  modalTextContainer: {
    paddingLeft: Metrics.ratio(20),
    flex: 1,
    justifyContent: 'center',
  },
  modalTextOne: {
    fontFamily: Fonts.type.semi_bold,
    fontSize: Metrics.ratio(13),
    color: Colors.text.titleColor,
  },
  modalTextTwo: {
    fontFamily: Fonts.type.medium,
    fontSize: Metrics.ratio(11),
    color: Colors.textLight,
  },
  modalOptionSeperator: {
    marginTop: Metrics.ratio(10),
    backgroundColor: Colors.lightGray,
    height: 0.5,
    width: Metrics.screenWidth - 40,
    alignSelf: 'center',
  },
  confirmButton: {
    paddingHorizontal: Metrics.ratio(24),
    height: Metrics.ratio(40),
    backgroundColor: Colors.BlackButton,
    borderColor: Colors.black,
    borderWidth: Metrics.ratio(1),
    borderRadius: Metrics.ratio(6),
    marginHorizontal: Metrics.ratio(0),
  },
  deleteButton: {
    height: Metrics.ratio(40),
    marginLeft: Metrics.baseMargin,
    backgroundColor: Colors.gray2,
    paddingHorizontal: Metrics.ratio(24),
  },
  buttonContainer: {
    marginTop: Metrics.baseMargin,
    justifyContent: 'center',
    marginBottom: Metrics.ratio(10),
    flexDirection: 'row',
  },
  headerContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerText: {
    textAlign: 'center',
    fontSize: Fonts.size.small,
    fontFamily: Fonts.type.medium,
  },
});
