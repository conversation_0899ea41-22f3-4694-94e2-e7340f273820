import React from 'react';
import {ActivityIndicator, TextProps} from 'react-native';
import {ButtonView, Text} from '../../components';
import styles from './styles';
import {Colors, Metrics} from '../../theme';

interface ButtonViewProps extends TextProps {
  text: String;
  buttonStye?: {};
  textColor?: any;
  onPress: any;
  type?: any;
  isLoading?: boolean;
  size?:
    | 'medium'
    | 'buttonText'
    | 'small'
    | 'normal'
    | 'xxxxSmall'
    | 'xxxSmall'
    | 'xxSmall'
    | 'xSmall'
    | 'Large'
    | 'large'
    | 'xLarge'
    | 'xxLarge'
    | 'xxxLarge'
    | 'xxxxLarge'
    | number;
}

const AppButton: React.FC<ButtonViewProps> = ({
  text = '',
  buttonStye = styles.buttonStyle,
  textColor = Colors.text.black,
  onPress,
  type = 'medium',
  isLoading = false,
  size = 'buttonText',
  ...rest
}) => {
  return (
    <ButtonView
      style={[styles.buttonStyle, buttonStye]}
      onPress={onPress}
      {...rest}>
      {isLoading ? (
        <ActivityIndicator color={Colors.white} />
      ) : (
        <Text size={size} type={type} color={textColor}>
          {text}
        </Text>
      )}
    </ButtonView>
  );
};

export default AppButton;
