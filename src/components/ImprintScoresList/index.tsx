import {FlatList, View, ViewStyle} from 'react-native';
import {styles} from './styles';
import {Text} from '../';
import {ButtonView} from '..';
import {AppStyles, Colors, Metrics} from '../../theme';
import {useRef} from 'react';
import {IMPRINT_SCORES_LIST} from '../../constants';
import {UserState} from '../../types';
import {isUserSubscribed} from '../../util';

interface scoresProps {
  isPersonal: boolean;
  onScoresPress: (scoreName: string) => void;
  selectedScores: string[];
  data?: any;
  exrtaStyle?: ViewStyle | undefined;
  mainStyle?: ViewStyle | undefined;
  user?: UserState;
}

const ImprintScoresList: React.FC<scoresProps> = ({
  isPersonal,
  onScoresPress,
  selectedScores,
  data = IMPRINT_SCORES_LIST, // Default to the original list
  exrtaStyle = {},
  mainStyle = {},
  user,
}) => {
  const flatListRef = useRef<any>();

  const handlePress = (item: {serverScoreName: string}, index: number) => {
    flatListRef.current?.scrollToIndex({
      animated: true,
      index: index,
      viewPosition: 0.5,
    });
    onScoresPress(item.serverScoreName);
  };

  return (
    <View style={[styles.marginVertical, mainStyle]}>
      <FlatList
        ref={flatListRef}
        extraData={isPersonal}
        showsHorizontalScrollIndicator={false}
        horizontal
        style={{marginHorizontal: Metrics.baseMargin}}
        data={data}
        renderItem={({item, index}) => {
          const shouldRenderImage = item.serverScoreName !== 'all';

          return (
            <ButtonView
              style={[
                styles.itemContainer,
                exrtaStyle,
                selectedScores.includes(item.serverScoreName)
                  ? styles.selectedColor
                  : {},
              ]}
              onPress={() => handlePress(item, index)}>
              {shouldRenderImage ? (
                isPersonal || !isUserSubscribed(user as UserState) ? (
                  <item.personalImage />
                ) : (
                  <item.globalImage />
                )
              ) : null}
              <Text
                style={AppStyles.mLeft5}
                color={
                  selectedScores.includes(item.serverScoreName)
                    ? Colors.white
                    : Colors.black
                }
                size={'xSmall'}
                type="medium">
                {item.displayScoreName}
              </Text>
            </ButtonView>
          );
        }}
      />
    </View>
  );
};

export default ImprintScoresList;
