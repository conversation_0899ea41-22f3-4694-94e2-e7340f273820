// @flow
import {StyleSheet} from 'react-native';
import {Colors, Metrics, AppStyles} from '../../theme';

export default StyleSheet.create({
  questionContainer: {
    marginBottom: Metrics.ratio(24),
  },

  optionButton: {
    padding: Metrics.ratio(10),
    backgroundColor: Colors.white,
    marginTop: Metrics.ratio(20),
    borderRadius: Metrics.ratio(5),
    borderColor: Colors.borderColors.activeBorderColor,
    borderWidth: Metrics.ratio(2),
  },

  selectedOption: {
    backgroundColor: Colors.black1,
  },
  paddingTop0: {
    paddingTop: 0,
  },
  separator: {
    marginTop: -10,
    alignSelf: 'center',
    height: 1,
    width: '90%',
    backgroundColor: Colors.gray,
    marginHorizontal: Metrics.baseMargin,
  },
});
