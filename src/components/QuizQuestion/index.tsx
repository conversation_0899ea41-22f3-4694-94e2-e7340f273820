import React from 'react';
import {View} from 'react-native';
import {
  ISelectedAnswer,
  IOptions,
  I_QUESTION,
  IQuizResponse,
} from '../../types';
import {ButtonView, Text} from '../../components/';
import styles from './styes';
import {Colors} from '../../theme';

interface QuestionItemProps {
  item: I_QUESTION | IQuizResponse;
  selectedAnswers: ISelectedAnswer[];
  handleOptionPress: (
    questionId: string,
    optionId: string,
    optionKey?: string,
  ) => void;
}

const QuestionItem: React.FC<QuestionItemProps> = ({
  item,
  selectedAnswers,
  handleOptionPress,
}) => {
  const renderOption = (questionId: string, option: IOptions) => {
    const isSelected = selectedAnswers.find(
      (answer: ISelectedAnswer) =>
        answer.questionId === questionId && answer.optionId === option.id,
    );

    return (
      <ButtonView
        key={option.id}
        style={[styles.optionButton, isSelected ? styles.selectedOption : {}]}
        onPress={() =>
          handleOptionPress(questionId, option.id, option.optionKey)
        }>
        <Text
          color={isSelected ? Colors.white : Colors.textGray}
          size={'xSmall'}>
          {option.text}
        </Text>
      </ButtonView>
    );
  };

  return (
    <View style={styles.questionContainer}>
      <Text color={Colors.black2} type="bold" size={'normal'}>
        {item.text}
      </Text>
      {item.options.map((option: IOptions) => renderOption(item.id, option))}
    </View>
  );
};

export default QuestionItem;
