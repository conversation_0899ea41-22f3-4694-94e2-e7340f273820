import {StyleSheet} from 'react-native';
import {Colors, Metrics, Fonts} from '../../theme';

// @flow

export default StyleSheet.create({
  mainContainer: {
    height: Metrics.ratio(Metrics.screenHeight),
    width: Metrics.ratio(Metrics.screenWidth),
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    alignItems: 'center',
  },
  container: {
    width: Metrics.ratio(Metrics.screenWidth - 60),
    height: Metrics.ratio(Metrics.screenHeight - 300),
    backgroundColor: 'white',
    position: 'absolute',
    borderRadius: Metrics.ratio(10),
    bottom: Metrics.ratio(Metrics.screenHeight / 2 - 250),
  },
  titleContainer: {
    flexDirection: 'row',
    borderTopLeftRadius: Metrics.ratio(10),
    borderTopRightRadius: Metrics.ratio(10),
    backgroundColor: Colors.lightGray,
    height: Metrics.ratio(60),
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  title: {
    fontFamily: Fonts.type.medium,
    fontSize: Fonts.size.normal,
    color: Colors.black,
    paddingLeft: Metrics.ratio(20),
  },
  buttonContainer: {
    marginVertical: Metrics.ratio(10),
    width: Metrics.ratio(Metrics.screenWidth - 60),
    flexDirection: 'row',
    alignSelf: 'center',
    justifyContent: 'space-evenly',
  },
  retakeButton: {
    width: Metrics.ratio(140),
    backgroundColor: Colors.white,
    borderColor: Colors.black,
    borderWidth: Metrics.ratio(1),
    borderRadius: Metrics.ratio(6),
    marginHorizontal: Metrics.ratio(0),
  },
  nextButton: {
    marginHorizontal: Metrics.ratio(0),
    width: Metrics.ratio(140),
  },
  cross: {
    paddingRight: 20,
  },
  above18Container: {
    flexDirection: 'row',
    padding: Metrics.ratio(20),
    alignItems: 'center',
  },
  above18Text: {
    paddingHorizontal: 10,
  },
  loader: {
    position: 'absolute',
    zIndex: 9999,
    top: 0,
    bottom: 50,
  },
  kinDescription: {
    fontFamily: Fonts.type.regular,
    textAlign: 'left',
    fontSize: Fonts.size.xxSmall,
  },
  kinHeading: {margin: Metrics.ratio(10), fontFamily: Fonts.type.bold},
  safetyContainer: {
    flexDirection: 'row',
    paddingHorizontal: Metrics.ratio(20),
  },
});
