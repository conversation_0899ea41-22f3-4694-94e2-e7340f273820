// @flow
import {StyleSheet} from 'react-native';
import {Colors, Metrics, Fonts} from '../../theme';

export default StyleSheet.create({
  kinDetails: {
    fontFamily: Fonts.type.medium,
    fontSize: Fonts.size.xxSmall,
    color: Colors.black,
  },
  kinRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginHorizontal: Metrics.ratio(20),
    paddingTop: Metrics.ratio(5),
    alignItems: 'center',
  },
  kinStatus: {
    marginLeft: Metrics.ratio(5),

    fontSize: Fonts.size.xxxSmall,
    backgroundColor: Colors.KIN_STATUS_COLORS.PENDING_BACKGROUND,
    borderRadius: Metrics.ratio(5),
    padding: Metrics.ratio(2),
    borderWidth: Metrics.ratio(1),
    overflow: 'hidden',
    borderColor: 'transparent',
    fontFamily: Fonts.type.base,
    color: Colors.KIN_STATUS_COLORS.PENDING_TEXT,
  },
  rejectStatus: {
    marginLeft: Metrics.ratio(5),
    fontSize: Fonts.size.xxxSmall,
    backgroundColor: Colors.KIN_STATUS_COLORS.REJECTED_BACKGROUND,
    color: Colors.KIN_STATUS_COLORS.REJECTED_TEXT,
  },
  acceptStatus: {
    marginLeft: Metrics.ratio(5),
    fontSize: Fonts.size.xxxSmall,
    backgroundColor: Colors.KIN_STATUS_COLORS.ACCEPTED_BACKGROUND,
    color: Colors.KIN_STATUS_COLORS.ACCEPTED_TEXT,
  },
  kinActionAbleItems: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  dropDown: {
    borderColor: Colors.borderColors.activeBorderColor,
    width: Metrics.screenWidth * 0.8,
    alignSelf: 'center',
    top: 0,
    position: 'relative',
  },
  dropDownContainer: {
    borderRadius: Metrics.ratio(5),
    alignSelf: 'center',
    width: Metrics.screenWidth * 0.8,
    height: Metrics.ratio(56),
    backgroundColor: Colors.white,
    borderColor: Colors.borderColors.activeBorderColor,
  },
  dropDownPlaceholder: {
    width: Metrics.ratio(70),
    backgroundColor: Colors.white,
    position: 'relative',
    bottom: -10,
    zIndex: 99999,
    marginLeft: Metrics.ratio(20),
    fontFamily: Fonts.type.semi_bold,
    color: Colors.black,
    fontSize: Fonts.size.xSmall,
  },
  marginBottom: {marginBottom: 10},
});
