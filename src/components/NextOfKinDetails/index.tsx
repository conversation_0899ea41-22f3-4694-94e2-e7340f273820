import {Pressable, Text, View, Image} from 'react-native';
import {MODAL_DELETE} from '../../constants/AssetSVGConstants';
import TextInput from '../TextInput';
import styles from './styles';
import {INextOfKin} from '../../types';
import {useState, useEffect} from 'react';
import {KIN_STATUS, NEXT_OF_KIN} from '../../constants/StringConstants';
import DropDownPicker from 'react-native-dropdown-picker';
import _ from 'lodash';
import {KIN_RELATION} from '../../constants';

interface NextOfKinDetailsProps {
  data: INextOfKin;
  onUpdate: (updatedData: INextOfKin) => void;
  onDelete: (item: INextOfKin) => void;
  onRemind?: (item: INextOfKin) => void;
}

const NextOfKindDetails: React.FC<NextOfKinDetailsProps> = ({
  data,
  onUpdate,
  onDelete,
  onRemind = () => {},
}) => {
  const [nextOfKinData, setNextOfKinData] = useState(data);
  const [items, setItems] = useState(KIN_RELATION);
  const [relationType, setRelationType] = useState(
    _.isEmpty(data.relation) ? items[0].label : data.relation,
  );
  const [open, setOpen] = useState(false);

  useEffect(() => {
    onUpdate(nextOfKinData);
  }, [nextOfKinData]);

  useEffect(() => {
    setNextOfKinData(data);
  }, [data]);

  useEffect(() => {
    setNextOfKinData({...nextOfKinData, relation: relationType});
  }, [relationType]);

  return (
    <View>
      <View style={styles.kinRow}>
        <Text style={styles.kinDetails}>Kin Details</Text>
        <View style={styles.kinActionAbleItems}>
          <Pressable onPress={() => onDelete(nextOfKinData)}>
            <MODAL_DELETE />
          </Pressable>
          {nextOfKinData.id && nextOfKinData.hasApproved == null ? (
            <Pressable
              onPress={
                nextOfKinData.hasApproved !== false
                  ? () => onRemind(nextOfKinData)
                  : () => {}
              }>
              <Image
                source={require('../../assets/icons/resend.png')}
                style={{height: 25, width: 25}}
              />
            </Pressable>
          ) : null}
          {nextOfKinData.id && (
            <Text
              style={[
                nextOfKinData.hasApproved === null
                  ? styles.kinStatus
                  : nextOfKinData.hasApproved === true
                  ? [styles.kinStatus, styles.acceptStatus]
                  : [styles.kinStatus, styles.rejectStatus],
              ]}>
              {nextOfKinData.hasApproved === null
                ? KIN_STATUS.PENDING
                : nextOfKinData.hasApproved === true
                ? KIN_STATUS.ACCEPTED
                : KIN_STATUS.REJECTED}
            </Text>
          )}
        </View>
      </View>
      <View>
        <TextInput
          containerStyle={{
            marginVertical: 10,
          }}
          label={'Name'}
          placeholder={'Name'}
          value={nextOfKinData.name}
          editable={nextOfKinData.isEditAble ? true : false}
          onChangeText={val => setNextOfKinData({...nextOfKinData, name: val})}
          backgroundColor="#FFF9F4"
        />
        <TextInput
          containerStyle={{
            marginVertical: 10,
          }}
          label={'Email'}
          placeholder={'Email'}
          value={nextOfKinData.email}
          editable={nextOfKinData.isEditAble ? true : false}
          onChangeText={val => setNextOfKinData({...nextOfKinData, email: val})}
          backgroundColor="#FFF9F4"
        />
        <View style={styles.marginBottom}>
          <Text style={styles.dropDownPlaceholder}>{NEXT_OF_KIN.RELATION}</Text>

          <DropDownPicker
            listMode="SCROLLVIEW"
            open={open}
            value={relationType}
            items={items}
            setOpen={setOpen}
            setValue={setRelationType}
            setItems={setItems}
            style={styles.dropDownContainer}
            dropDownContainerStyle={styles.dropDown}
          />
        </View>
      </View>
    </View>
  );
};

export default NextOfKindDetails;
