import {Linking, Alert, Platform} from 'react-native';
import {userInfoRequest} from '../actions/UserActions';
import DataHandler from './DataHandler';

export interface YotiDeepLinkParams {
  sessionId?: string;
  status?: 'success' | 'failure' | 'cancelled';
  error?: string;
}

class YotiService {
  private static instance: YotiService;
  private isProcessingDeepLink = false;

  static getInstance(): YotiService {
    if (!YotiService.instance) {
      YotiService.instance = new YotiService();
    }
    return YotiService.instance;
  }

  initializeDeepLinkListener() {
    const linkingListener = Linking.addEventListener(
      'url',
      this.handleDeepLink,
    );

    Linking.getInitialURL().then(url => {
      if (url) {
        this.handleDeepLink({url});
      }
    });

    return () => {
      linkingListener?.remove();
    };
  }

  private handleDeepLink = async ({url}: {url: string}) => {
    if (!url || this.isProcessingDeepLink) return;

    // Check if this is a Yoti return URL
    if (url.includes('yoti-return') || url.includes('age-verification')) {
      this.isProcessingDeepLink = true;

      try {
        console.log('Yoti deep link received:', url);

        const params = this.parseDeepLinkParams(url);

        await this.refreshUserInfo();

        this.handleYotiResult(params);
      } catch (error) {
        console.error('Error handling Yoti deep link:', error);
        Alert.alert(
          'Verification Error',
          'Failed to process age verification result.',
        );
      } finally {
        this.isProcessingDeepLink = false;
      }
    }
  };

  private parseDeepLinkParams(url: string): YotiDeepLinkParams {
    const urlObj = new URL(url);
    const params: YotiDeepLinkParams = {};

    urlObj.searchParams.forEach((value, key) => {
      switch (key) {
        case 'sessionId':
          params.sessionId = value;
          break;
        case 'status':
          params.status = value as 'success' | 'failure' | 'cancelled';
          break;
        case 'error':
          params.error = value;
          break;
      }
    });

    return params;
  }

  private async refreshUserInfo(): Promise<any> {
    return new Promise((resolve, reject) => {
      const store = DataHandler.getStore();
      if (!store) {
        reject(new Error('Store not available'));
        return;
      }

      store.dispatch(
        userInfoRequest((response: any) => {
          if (response) {
            console.log(
              'User info refreshed after Yoti verification:',
              response.ageVerified,
            );
            resolve(response);
          } else {
            reject(new Error('Failed to refresh user info'));
          }
        }) as any,
      );
    });
  }

  private handleYotiResult(params: YotiDeepLinkParams) {
    const {status, error} = params;

    switch (status) {
      case 'success':
        Alert.alert(
          'Age Verification Successful',
          'Your age has been verified successfully.',
          [{text: 'OK', onPress: () => this.navigateAfterVerification()}],
        );
        break;

      case 'failure':
        Alert.alert(
          'Age Verification Failed',
          error || 'Age verification failed. Please try again.',
          [
            {text: 'Retry', onPress: () => this.retryVerification()},
            {text: 'Cancel', style: 'cancel'},
          ],
        );
        break;

      case 'cancelled':
        Alert.alert(
          'Age Verification Cancelled',
          'Age verification was cancelled. You can retry from your profile.',
          [{text: 'OK', style: 'cancel'}],
        );
        break;

      default:
        const userState = DataHandler.getStore()?.getState()?.user;
        if (userState?.userInfo?.ageVerified) {
          this.navigateAfterVerification();
        }
        break;
    }
  }

  private navigateAfterVerification() {
    console.log(
      'Age verification completed, navigation will be handled by current flow',
    );
  }

  private retryVerification() {
    const store = DataHandler.getStore();
    if (store) {
      console.log('Retrying age verification...');
    }
  }

  async openYotiApp(sessionUrl: string): Promise<boolean> {
    const yotiSchemes = ['yoti://', 'yoti-app://', 'com.yoti.mobile://'];

    try {
      let supported = false;
      let workingScheme = '';

      for (const scheme of yotiSchemes) {
        try {
          const canOpen = await Linking.canOpenURL(scheme);
          if (canOpen) {
            supported = true;
            workingScheme = scheme;
            break;
          }
        } catch (schemeError) {
          console.warn(`Error checking ${scheme}:`, schemeError);
        }
      }

      if (supported) {
        await Linking.openURL(sessionUrl);
        return true;
      } else {
        await this.openYotiAppStore();
        return false;
      }
    } catch (err) {
      await this.openYotiAppStore();
      return false;
    }
  }
  /**
   * Open app store to install Yoti app
   */
  private async openYotiAppStore() {
    const appStoreUrl =
      'https://apps.apple.com/gb/app/yoti-your-digital-identity/id983980808';

    const playStoreUrl =
      'https://play.google.com/store/apps/details?id=com.yoti.mobile.android.live';

    try {
      const url = Platform.OS === 'ios' ? appStoreUrl : playStoreUrl;
      await Linking.openURL(url);
    } catch (error) {
      console.error('Error opening app store:', error);
    }
  }

  /**
   * Check if user needs age verification
   */
  static needsAgeVerification(userInfo: any): boolean {
    return userInfo && userInfo.ageVerified === false;
  }
}

export default YotiService;
