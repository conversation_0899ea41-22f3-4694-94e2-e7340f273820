// @flow
// Facebook Settings
export const FACEBOOK_APP_ID = '';
export const FACEBOOK_PERMISSIONS = ['email', 'public_profile'];
export const PROFILE_REQUEST_PARAMS = {
  fields: {
    string:
      'id, name, email, first_name, last_name, gender, verified, picture.type(large), birthday, hometown',
  },
};
export function profileRequestConfig(accessToken) {
  return {
    accessToken,
    parameters: PROFILE_REQUEST_PARAMS,
  };
}
// Google SignIN_Setting
export const WEB_CLIENT_ID =
  '854449431593-1gve4kb3k7n4uuk6h7te0amqmohrc6g2.apps.googleusercontent.com';
export const IOS_CLIENT_ID =
  '854449431593-i1et3hhisopnfpdc9ftonmdjmtktgjph.apps.googleusercontent.com';
