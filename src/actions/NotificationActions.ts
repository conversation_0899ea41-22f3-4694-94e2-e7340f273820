import {ITransparencyPayload} from '../types';
import {
  GET_IMPRINT_BY_ID,
  GET_NOTIFICATIONS,
  READ_NOTIFICATION,
  UPDATE_VERIFICATION,
  GET_TRANSPARENCY_DATA,
  GET_PUB_SUB_TOKEN,
} from './ActionTypes';

interface Action {
  type: string;
  payload?: any;
  response?: any;
  responseCallback?: any;
}

export function getNotificationRequest(
  payload: any,
  responseCallback: any,
): Action {
  return {
    payload,
    responseCallback,
    type: GET_NOTIFICATIONS.REQUEST,
  };
}

export function getNotificationSuccess(
  responseCallback: any,
  payload: any,
): Action {
  return {
    payload,
    responseCallback,
    type: GET_NOTIFICATIONS.SUCCESS,
  };
}

export function readNotificationRequest(
  payload: any,
  responseCallback: any,
): Action {
  return {
    payload,
    responseCallback,
    type: READ_NOTIFICATION.REQUEST,
  };
}

export function readNotificationSuccess(responseCallback: any): Action {
  return {
    responseCallback,
    type: READ_NOTIFICATION.SUCCESS,
  };
}

export function getImprintByIdRequest(
  payload: any,
  responseCallback: any,
): Action {
  return {
    payload,
    responseCallback,
    type: GET_IMPRINT_BY_ID.REQUEST,
  };
}

export function getImprintByIdSuccess(
  responseCallback: any,
  payload: any,
): Action {
  return {
    payload,
    responseCallback,
    type: GET_IMPRINT_BY_ID.SUCCESS,
  };
}

export function updateVerificationRequest(
  payload: any,
  responseCallback: any,
): Action {
  return {
    payload,
    responseCallback,
    type: UPDATE_VERIFICATION.REQUEST,
  };
}

export function updateVerificationSuccess(responseCallback: any): Action {
  return {
    responseCallback,
    type: UPDATE_VERIFICATION.SUCCESS,
  };
}

export function getTransparencyDataRequest(
  payload: ITransparencyPayload,
  responseCallback: any,
): Action {
  return {
    payload,
    responseCallback,
    type: GET_TRANSPARENCY_DATA.REQUEST,
  };
}

export function getPubSubTokenRequest(responseCallback: any): Action {
  return {
    responseCallback,
    type: GET_PUB_SUB_TOKEN.REQUEST,
  };
}
