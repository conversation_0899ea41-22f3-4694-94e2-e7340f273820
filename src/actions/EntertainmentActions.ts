import {
  IEntertainmentContent,
  INewsPayload,
  IQuiz,
  IQuizContentResponse,
  ISharePayload,
  ISubmitQuizPayload,
} from '../types';
import {
  GET_ENTERTAINMENTS_CONTENT,
  SHARE_IMPRINT,
  QUIZ_PREVIEW_LIST,
  ENTERTAINMENT_QUIZ,
  ENTERTAINMENT_QUIZ_ATTEMPT,
  QUIZ_UPDATE_ITEM,
  UPDATE_REWARD_ITEM,
  GET_NEWS,
} from './ActionTypes';

interface Action {
  type: string;
  payload?: any;
  response?: any;
  responseCallback?: any;
  updatedItem?: any;
}

export function getEntertainmentsContentRequest(
  payload: IEntertainmentContent,
  responseCallback: any,
): Action {
  return {
    payload,
    responseCallback,
    type: GET_ENTERTAINMENTS_CONTENT.REQUEST,
  };
}

export function shareImprintRequest(
  payload: ISharePayload,
  responseCallback: any,
): Action {
  return {
    payload,
    responseCallback,
    type: SHARE_IMPRINT.REQUEST,
  };
}

export function getQuizPreviewRequest(responseCallback: any): Action {
  return {
    responseCallback,
    type: QUIZ_PREVIEW_LIST.REQUEST,
  };
}

export function getQuizPreviewSuccess(response: IQuizContentResponse): Action {
  return {
    response,
    type: QUIZ_PREVIEW_LIST.SUCCESS,
  };
}

export function updateQuizItem(updatedItem: any): Action {
  return {
    updatedItem,
    type: QUIZ_UPDATE_ITEM,
  };
}

export function updateRewardItem(updatedItem: any): Action {
  return {
    updatedItem,
    type: UPDATE_REWARD_ITEM,
  };
}

export function getEntertainmentQuiz(
  payload: IQuiz,
  responseCallback: any,
): Action {
  return {
    payload,
    responseCallback,
    type: ENTERTAINMENT_QUIZ.REQUEST,
  };
}

export function submitQuizRequest(
  payload: ISubmitQuizPayload,
  responseCallback: any,
): Action {
  return {
    payload,
    responseCallback,
    type: ENTERTAINMENT_QUIZ_ATTEMPT.REQUEST,
  };
}

export function getNewsContentRequest(
  payload: INewsPayload,
  responseCallback: any,
): Action {
  return {
    payload,
    responseCallback,
    type: GET_NEWS.REQUEST,
  };
}
