import {IFreeTextSearchPayload, ITimeLine} from '../types';

import {FREE_TEXT_SEARCH_USERS, FREE_TEXT_SEARCH_IMPRINTS} from './ActionTypes';

interface Action {
  type: string;
  payload?: any;
  response?: any;
  responseCallback?: any;
  data?: any;
}

export function getFreeTextSearchUsersRequest(
  payload: IFreeTextSearchPayload,
  responseCallback: any,
): Action {
  return {
    payload,
    responseCallback,
    type: FREE_TEXT_SEARCH_USERS.REQUEST,
  };
}

export function getFreeTextSearchUserSuccess(responseCallback: any): Action {
  return {
    responseCallback,
    type: FREE_TEXT_SEARCH_USERS.SUCCESS,
  };
}

export function getFreeTextImprintsRequest(
  payload: IFreeTextSearchPayload,
  responseCallback: any,
): Action {
  return {
    payload,
    responseCallback,
    type: FREE_TEXT_SEARCH_IMPRINTS.REQUEST,
  };
}

export function getFreeTextImprintsSuccess(
  response: ITimeLine[],
  payload: IFreeTextSearchPayload,
): Action {
  return {
    response,
    payload,
    type: FREE_TEXT_SEARCH_IMPRINTS.SUCCESS,
  };
}
