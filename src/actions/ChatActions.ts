import {
  <PERSON><PERSON>te<PERSON>hat,
  <PERSON>eleteMessage,
  IFlagConversationPayload,
  IFlagMessagePayload,
  IGetMessagesPayload,
} from '../types';
import {
  CREATE_CHAT_GROUP,
  RECENT_CHAT_LIST,
  GET_CONVERSATION_MESSAGES,
  UPDATE_READ_STATUS,
  GET_CHAT_TOKEN,
  DELETE_CHAT_GROUP,
  LEAVE_CONVERSATION,
  FLAG_MESSAGE,
  FLAG_CONVERSATION,
  DELETE_CHAT,
  DELETE_MESSAGE,
} from './ActionTypes';

interface Action {
  type: string;
  payload?: any;
  response?: any;
  responseCallback?: any;
}

export function getRecentChatListRequest(responseCallback: any): Action {
  return {
    responseCallback,
    type: RECENT_CHAT_LIST.REQUEST,
  };
}
export function createChatGroupRequest(
  payload: FormData,
  responseCallback: any,
): Action {
  return {
    payload,
    responseCallback,
    type: CREATE_CHAT_GROUP.REQUEST,
  };
}

export function getConversationMessagesRequest(
  payload: IGetMessagesPayload,
  responseCallback: any,
): Action {
  return {
    payload,
    responseCallback,
    type: GET_CONVERSATION_MESSAGES.REQUEST,
  };
}

export function updateMessageReadStatus(
  payload: any,
  responseCallback: any,
): Action {
  return {
    payload,
    responseCallback,
    type: UPDATE_READ_STATUS.REQUEST,
  };
}

export function getChatTokenRequest(responseCallback: any): Action {
  return {
    responseCallback,
    type: GET_CHAT_TOKEN.REQUEST,
  };
}

export function deleteChatGroupRequest(
  payload: IDeleteChat,
  responseCallback: any,
): Action {
  return {
    payload,
    responseCallback,
    type: DELETE_CHAT_GROUP.REQUEST,
  };
}

export function deleteMessageRequest(
  payload: IDeleteMessage,
  responseCallback: any,
): Action {
  return {
    payload,
    responseCallback,
    type: DELETE_MESSAGE.REQUEST,
  };
}

export function deleteChatRequest(
  payload: IDeleteChat,
  responseCallback: any,
): Action {
  return {
    payload,
    responseCallback,
    type: DELETE_CHAT.REQUEST,
  };
}

export function leaveConversationRequest(
  payload: IDeleteChat,
  responseCallback: any,
): Action {
  return {
    payload,
    responseCallback,
    type: LEAVE_CONVERSATION.REQUEST,
  };
}

export function flagMessageRequest(
  payload: IFlagMessagePayload,
  responseCallback: any,
): Action {
  return {
    payload,
    responseCallback,
    type: FLAG_MESSAGE.REQUEST,
  };
}
export function flagConversationRequest(
  payload: IFlagConversationPayload,
  responseCallback: any,
): Action {
  return {
    payload,
    responseCallback,
    type: FLAG_CONVERSATION.REQUEST,
  };
}
