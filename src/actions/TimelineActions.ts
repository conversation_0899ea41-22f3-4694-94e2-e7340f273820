import {
  G<PERSON><PERSON><PERSON><PERSON>_TIMELINE,
  PER<PERSON><PERSON>L_TIMELINE,
  POST_REACTION,
  RESET_TIMELINE,
  IMPRINT_VERIFICATION_REQUEST,
  IMPRINT_SHOUT_OUT,
  GET_USER_TIMELINE,
  GET_FILTERED_TIMELINE,
  IMPRINT_BOOKMARK,
  REQUEST_FOLLOW,
  GET_NEXT_OF_KIN,
  DELETE_NEXT_OF_KIN,
  ADD_NEXT_OF_KIN,
  GET_SUBSCRIPTION_PLANS,
  GET_SUBSCRIPTION_PACKAGE_FEATURES,
  REMIND_NEXT_OF_KIN,
  DELETE_IMPRINT,
  USER_TIMELINE,
  REPORT_CONTENT,
  VIOLATION_POLICIES,
  UPDATE_TIME_LINE,
  REMIND_LATER,
} from './ActionTypes';
import {
  IDeleteImprintPayload,
  IReportContentPayload,
  ITimeLine,
  ITimeLinePayLoad,
  IViolationContent,
  PostReactionPayload,
} from '../types';

interface Action {
  type: string;
  payload?: any;
  response?: any;
  isGlobal?: boolean;
  responseCallback?: any;
}

export function globalTimeLineRequest(
  payload: ITimeLinePayLoad,
  responseCallback: any,
): Action {
  return {
    responseCallback,
    payload,
    type: GLOBAL_TIMELINE.REQUEST,
  };
}

export function globalTimeLineSuccess(
  response: ITimeLine[],
  payload: ITimeLinePayLoad,
): Action {
  return {
    response,
    payload,
    type: GLOBAL_TIMELINE.SUCCESS,
  };
}

export function resetTimeLine(): Action {
  return {
    type: RESET_TIMELINE.SUCCESS,
  };
}

export function personalTimeLineRequest(
  payload: ITimeLinePayLoad,
  responseCallback: any,
): Action {
  return {
    responseCallback,
    payload,
    type: PERSONAL_TIMELINE.REQUEST,
  };
}

export function personalTimeLineSuccess(
  response: ITimeLine[],
  payload: ITimeLinePayLoad,
): Action {
  return {
    response,
    payload,
    type: PERSONAL_TIMELINE.SUCCESS,
  };
}

export function postReactionRequest(
  payload: PostReactionPayload,
  responseCallback: any,
): Action {
  return {
    payload,
    responseCallback,
    type: POST_REACTION.REQUEST,
  };
}

export function postReactionSuccess(response: any, isGlobal: boolean): Action {
  return {
    response,
    isGlobal,
    type: POST_REACTION.SUCCESS,
  };
}

export function imprintVerificationRequest(
  payload: any,
  responseCallback: any,
): Action {
  return {
    payload,
    responseCallback,
    type: IMPRINT_VERIFICATION_REQUEST.REQUEST,
  };
}

export function imprintVerificationSuccess(
  response: any,
  isGlobal: boolean,
): Action {
  return {
    response,
    isGlobal,
    type: IMPRINT_VERIFICATION_REQUEST.SUCCESS,
  };
}

export function imprintShoutoutRequest(
  payload: any,
  responseCallback: any,
): Action {
  return {
    payload,
    responseCallback,
    type: IMPRINT_SHOUT_OUT.REQUEST,
  };
}

export function imprintShoutoutSuccess(response: any): Action {
  return {
    response,
    type: IMPRINT_SHOUT_OUT.SUCCESS,
  };
}

export function getUserTimeLineRequest(
  payload: ITimeLinePayLoad,
  responseCallback: any,
): Action {
  return {
    responseCallback,
    payload,
    type: GET_USER_TIMELINE.REQUEST,
  };
}

export function deleteImprintRequest(
  payload: IDeleteImprintPayload,
  responseCallback: any,
): Action {
  return {
    responseCallback,
    payload,
    type: DELETE_IMPRINT.REQUEST,
  };
}
export function deleteImprintSuccess(response: any): Action {
  return {
    response,
    type: DELETE_IMPRINT.SUCCESS,
  };
}

export function getUserTimeLineSuccess(
  response: ITimeLine[],
  payload: ITimeLinePayLoad,
): Action {
  return {
    response,
    payload,
    type: GET_USER_TIMELINE.SUCCESS,
  };
}

export function getFilteredTimeLineRequest(
  payload: ITimeLinePayLoad,
  responseCallback: any,
): Action {
  return {
    responseCallback,
    payload,
    type: GET_FILTERED_TIMELINE.REQUEST,
  };
}

export function getFilteredTimeLineSuccess(
  response: ITimeLine[],
  payload: ITimeLinePayLoad,
): Action {
  return {
    response,
    payload,
    type: GET_FILTERED_TIMELINE.SUCCESS,
  };
}

export function imprintBookmarkRequest(
  payload: any,
  responseCallback: any,
): Action {
  return {
    responseCallback,
    payload,
    type: IMPRINT_BOOKMARK.REQUEST,
  };
}

export function imprintBookmarkSuccess(
  response: any,
  isGlobal: boolean,
): Action {
  return {
    response,
    isGlobal,
    type: IMPRINT_BOOKMARK.SUCCESS,
  };
}

export function requestFollowRequest(
  payload: any,
  responseCallback: any,
): Action {
  return {
    payload,
    responseCallback,
    type: REQUEST_FOLLOW.REQUEST,
  };
}

export function requestFollowSuccess(response: any, isGlobal: boolean): Action {
  return {
    response,
    isGlobal,
    type: REQUEST_FOLLOW.SUCCESS,
  };
}

export function nextOfKinRequest(responseCallback: any): Action {
  return {
    responseCallback,
    type: GET_NEXT_OF_KIN.REQUEST,
  };
}

export function nextOfKinSuccess(response: any): Action {
  return {
    response,
    type: GET_NEXT_OF_KIN.SUCCESS,
  };
}

export function deleteNextOfKinRequest(
  payload: any,
  responseCallback: any,
): Action {
  return {
    payload,
    responseCallback,
    type: DELETE_NEXT_OF_KIN.REQUEST,
  };
}

export function deleteNextOfKinSuccess(response: any): Action {
  return {
    response,
    type: DELETE_NEXT_OF_KIN.SUCCESS,
  };
}

export function addNextOfKinRequest(
  payload: any,
  responseCallback: any,
): Action {
  return {
    payload,
    responseCallback,
    type: ADD_NEXT_OF_KIN.REQUEST,
  };
}

export function addNextOfKinSuccess(response: any): Action {
  return {
    response,
    type: ADD_NEXT_OF_KIN.SUCCESS,
  };
}

export function getSubscriptionPlanRequest(responseCallback: any): Action {
  return {
    responseCallback,
    type: GET_SUBSCRIPTION_PLANS.REQUEST,
  };
}

export function getSubscriptionPlanSuccess(response: any): Action {
  return {
    response,
    type: GET_SUBSCRIPTION_PLANS.SUCCESS,
  };
}

export function getSubscriptionPackageFeaturesRequest(
  responseCallback: any,
): Action {
  return {
    responseCallback,
    type: GET_SUBSCRIPTION_PACKAGE_FEATURES.REQUEST,
  };
}

export function getSubscriptionPackageFeaturesSuccess(response: any): Action {
  return {
    response,
    type: GET_SUBSCRIPTION_PACKAGE_FEATURES.SUCCESS,
  };
}

export function remindNextOfKinRequest(
  payload: any,
  responseCallback: any,
): Action {
  return {
    payload,
    responseCallback,
    type: REMIND_NEXT_OF_KIN.REQUEST,
  };
}

export function userTimelineRequest(
  payload: ITimeLinePayLoad,
  responseCallback: any,
): Action {
  return {
    responseCallback,
    payload,
    type: USER_TIMELINE.REQUEST,
  };
}

export function userTimelineSuccess(
  response: ITimeLine[],
  payload: ITimeLinePayLoad,
): Action {
  return {
    response,
    payload,
    type: USER_TIMELINE.SUCCESS,
  };
}

export function reportContentRequest(
  payload: IReportContentPayload,
  responseCallback: any,
): Action {
  return {
    payload,
    responseCallback,
    type: REPORT_CONTENT.REQUEST,
  };
}

export function remindLaterRequest(responseCallback: any): Action {
  return {
    responseCallback,
    type: REMIND_LATER.REQUEST,
  };
}

export function getViolationPolicies(
  payload: any,
  responseCallback: IViolationContent,
): Action {
  return {
    payload,
    responseCallback,
    type: VIOLATION_POLICIES.REQUEST,
  };
}

export function updateTimelineRequest(payload: any): Action {
  return {
    payload,
    type: UPDATE_TIME_LINE,
  };
}
