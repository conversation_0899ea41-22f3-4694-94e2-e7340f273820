import {
  IEditImagePayLoad,
  IProfileModeration,
  IProfileModerationResponse,
} from '../types';
import {
  ADD_EDUCATION,
  ADD_EMPLOYMENT,
  ADD_FAMILY,
  COMPLETE_PROFiLE,
  DELETE_EDUCATION,
  DELETE_EMPLOYMENT,
  EDIT_IMAGE,
  EDUCATION_DATA,
  EMPTY_INSTITUTE,
  EMPTY_USERS,
  GET_INSTITUTE,
  GET_OTHER_USER_DETAILS,
  GET_RELATION_ENUMS,
  UPDATED_PROFILE_OVERVIEW,
  UPDATE_CONTACT,
  UPDATE_EDUCATION,
  UPDATE_EMPLOYMENT,
  UPDATE_PROFILE_ABOUT,
  USER_SEARCH,
  M<PERSON>ERATION_PROFILE,
  DELETE_FAMILY,
  UPDATE_FAMILY,
  MODERATION_PROFILE_PHOTO,
} from './ActionTypes';

interface Action {
  type: string;
  payload?: any;
  response?: any;
  responseCallback?: any;
}

export function editImageRequest(
  payload: FormData,
  responseCallback: any,
): Action {
  return {
    payload,
    responseCallback,
    type: EDIT_IMAGE.REQUEST,
  };
}

export function editImageSuccess(
  payload: IEditImagePayLoad,
  responseCallback: any,
): Action {
  return {
    payload,
    responseCallback,
    type: EDIT_IMAGE.SUCCESS,
  };
}

export function completeProfileRequest(responseCallback: any): Action {
  return {
    responseCallback,
    type: COMPLETE_PROFiLE.REQUEST,
  };
}

export function completeProfileSuccess(response: any): Action {
  return {
    response,
    type: COMPLETE_PROFiLE.SUCCESS,
  };
}

export function updateProfileOverviewRequest(
  payload: any,
  responseCallback: any,
): Action {
  return {
    payload,
    responseCallback,
    type: UPDATED_PROFILE_OVERVIEW.REQUEST,
  };
}

export function updateProfileOverviewSuccess(responseCallback: any): Action {
  return {
    responseCallback,
    type: UPDATED_PROFILE_OVERVIEW.SUCCESS,
  };
}

export function updateProfileAboutRequest(
  payload: any,
  responseCallback: any,
): Action {
  return {
    payload,
    responseCallback,
    type: UPDATE_PROFILE_ABOUT.REQUEST,
  };
}

export function updateProfileAboutSuccess(responseCallback: any): Action {
  return {
    responseCallback,
    type: UPDATE_PROFILE_ABOUT.SUCCESS,
  };
}

export function getInstituteRequest(
  payload: any,
  responseCallback: any,
): Action {
  return {
    payload,
    responseCallback,
    type: GET_INSTITUTE.REQUEST,
  };
}

export function getInstituteSuccess(responseCallback: any): Action {
  return {
    responseCallback,
    type: GET_INSTITUTE.SUCCESS,
  };
}

export function emptyInstitute(): Action {
  return {
    type: EMPTY_INSTITUTE.SUCCESS,
  };
}

export function emptyUsers(): Action {
  return {
    type: EMPTY_USERS.SUCCESS,
  };
}

export function educationData(payload: any) {
  return {
    payload,
    type: EDUCATION_DATA.SUCCESS,
  };
}

export function updateEducationRequest(payload: any, responseCallback: any) {
  return {
    payload,
    responseCallback,
    type: UPDATE_EDUCATION.REQUEST,
  };
}

export function updateEducationSuccess(responseCallback: any) {
  return {
    responseCallback,
    type: UPDATE_EDUCATION.SUCCESS,
  };
}

export function updateFamilyRequest(payload: any, responseCallback: any) {
  return {
    payload,
    responseCallback,
    type: UPDATE_FAMILY.REQUEST,
  };
}

export function updateFamilySuccess(responseCallback: any) {
  return {
    responseCallback,
    type: UPDATE_FAMILY.SUCCESS,
  };
}

export function deleteEducationRequest(payload: any, responseCallback: any) {
  return {
    payload,
    responseCallback,
    type: DELETE_EDUCATION.REQUEST,
  };
}

export function deleteEducationSuccess(responseCallback: any) {
  return {
    responseCallback,
    type: DELETE_EDUCATION.SUCCESS,
  };
}

export function updateEmploymentRequest(payload: any, responseCallback: any) {
  return {
    payload,
    responseCallback,
    type: UPDATE_EMPLOYMENT.REQUEST,
  };
}

export function updateEmploymentSuccess(responseCallback: any) {
  return {
    responseCallback,
    type: UPDATE_EMPLOYMENT.SUCCESS,
  };
}

export function addEducationRequest(payload: any, responseCallback: any) {
  return {
    payload,
    responseCallback,
    type: ADD_EDUCATION.REQUEST,
  };
}

export function addEducationSuccess(responseCallback: any) {
  return {
    responseCallback,
    type: ADD_EDUCATION.SUCCESS,
  };
}

export function addEmploymentRequest(payload: any, responseCallback: any) {
  return {
    payload,
    responseCallback,
    type: ADD_EMPLOYMENT.REQUEST,
  };
}

export function addEmploymentSuccess(responseCallback: any) {
  return {
    responseCallback,
    type: ADD_EMPLOYMENT.SUCCESS,
  };
}

export function deleteEmploymentRequest(payload: any, responseCallback: any) {
  return {
    payload,
    responseCallback,
    type: DELETE_EMPLOYMENT.REQUEST,
  };
}

export function deleteEmploymentSuccess(responseCallback: any) {
  return {
    responseCallback,
    type: DELETE_EMPLOYMENT.SUCCESS,
  };
}

export function addFamilyRequest(payload: any, responseCallback: any) {
  return {
    payload,
    responseCallback,
    type: ADD_FAMILY.REQUEST,
  };
}

export function addFamilySuccess(responseCallback: any) {
  return {
    responseCallback,
    type: ADD_FAMILY.SUCCESS,
  };
}

export function deleteFamilyRequest(payload: any, responseCallback: any) {
  return {
    payload,
    responseCallback,
    type: DELETE_FAMILY.REQUEST,
  };
}

export function deleteFamilySuccess(payload: any, responseCallback: any) {
  return {
    payload,
    responseCallback,
    type: DELETE_FAMILY.SUCCESS,
  };
}

export function userSearchRequest(payload: any, responseCallback: any) {
  return {
    payload,
    responseCallback,
    type: USER_SEARCH.REQUEST,
  };
}

export function userSearchSuccess(responseCallback: any) {
  return {
    responseCallback,
    type: USER_SEARCH.SUCCESS,
  };
}

export function getRelationEnumRequest() {
  return {
    type: GET_RELATION_ENUMS.REQUEST,
  };
}

export function getRelationEnumSuccess(responseCallback: any) {
  return {
    responseCallback,
    type: GET_RELATION_ENUMS.SUCCESS,
  };
}

export function getOtherUserDetailsRequest(
  payload: any,
  responseCallback: any,
) {
  return {
    payload,
    responseCallback,
    type: GET_OTHER_USER_DETAILS.REQUEST,
  };
}

export function getOtherUserDetailsSuccess(responseCallback: any) {
  return {
    responseCallback,
    type: GET_OTHER_USER_DETAILS.SUCCESS,
  };
}

export function updateContanctRequest(payload: any, responseCallback: any) {
  return {
    payload,
    responseCallback,
    type: UPDATE_CONTACT.REQUEST,
  };
}

export function updateContactSuccess(responseCallback: any) {
  return {
    responseCallback,
    type: UPDATE_CONTACT.SUCCESS,
  };
}

export function profileModerationRequest(
  payload: IProfileModeration,
  responseCallback: any,
) {
  return {
    payload,
    responseCallback,
    type: MODERATION_PROFILE.REQUEST,
  };
}

export function profilePhotoModerationRequest(
  payload: IProfileModeration,
  responseCallback: IProfileModerationResponse,
) {
  return {
    payload,
    responseCallback,
    type: MODERATION_PROFILE_PHOTO.REQUEST,
  };
}
