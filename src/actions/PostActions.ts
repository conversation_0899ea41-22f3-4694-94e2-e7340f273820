import {IPostImprintPayload} from '../types';

import {
  USER_POST_IMPRINT,
  USER_UPDATE_IMPRINT,
  GET_HASHTAGS,
} from './ActionTypes';

interface Action {
  type: string;
  payload?: any;
  response?: any;
  responseCallback?: any;
  dynamicOBject?: any;
}

export function userPostImprintRequest(
  payload: IPostImprintPayload,
  responseCallback: any,
): Action {
  return {
    payload,
    responseCallback,
    type: USER_POST_IMPRINT.REQUEST,
  };
}

export function userUpdateImprintRequest(
  payload: any,
  responseCallback: any,
): Action {
  return {
    payload,
    responseCallback,
    type: USER_UPDATE_IMPRINT.REQUEST,
  };
}

export function getHashtagsList(payload: any, responseCallback: any): Action {
  return {
    payload,
    responseCallback,
    type: GET_HASHTAGS.REQUEST,
  };
}
