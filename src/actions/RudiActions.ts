import {SEND_BUTTON} from '../constants/AssetSVGConstants';
import {
  GET_RUDI_FILE,
  GET_RUDI_MESSAGE,
  SEND_RUDI_MESSAGE,
  UPLOAD_RUDI_FILE,
  DELETE_RUDI_FILE,
} from './ActionTypes';

interface Action {
  type: string;
  payload?: any;
  response?: any;
  responseCallback?: any;
}

export function getRudiMessagesRequest(
  payload: any,
  responseCallback: any,
): Action {
  return {
    payload,
    responseCallback,
    type: GET_RUDI_MESSAGE.REQUEST,
  };
}

export function getRudiMessagesSuccess(
  payload: any,
  responseCallback: any,
): Action {
  return {
    payload,
    responseCallback,
    type: GET_RUDI_MESSAGE.SUCCESS,
  };
}

export function getRudiFileRequest(responseCallback: any): Action {
  return {
    responseCallback,
    type: GET_RUDI_FILE.REQUEST,
  };
}

export function getRudiFileSuccess(responseCallback: any): Action {
  return {
    responseCallback,
    type: GET_RUDI_FILE.SUCCESS,
  };
}

export function sendRudiMessageRequest(
  payload: any,
  responseCallback: any,
): Action {
  return {
    payload,
    responseCallback,
    type: SEND_RUDI_MESSAGE.REQUEST,
  };
}

export function sendRudiMessageSuccess(responseCallback: any): Action {
  return {
    responseCallback,
    type: SEND_RUDI_MESSAGE.SUCCESS,
  };
}

export function uploadRudiFileRequest(
  payload: any,
  responseCallback: any,
): Action {
  return {
    payload,
    responseCallback,
    type: UPLOAD_RUDI_FILE.REQUEST,
  };
}

export function uploadRudiFileSuccess(responseCallback: any): Action {
  return {
    responseCallback,
    type: UPLOAD_RUDI_FILE.SUCCESS,
  };
}

export function deleteRudiFileRequest(
  payload: any,
  responseCallback: any,
): Action {
  return {
    payload,
    responseCallback,
    type: DELETE_RUDI_FILE.REQUEST,
  };
}
