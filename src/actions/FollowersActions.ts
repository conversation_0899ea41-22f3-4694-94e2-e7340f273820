import {IRespondFollower, IUnFollow, IBlockEntity} from '../types';
import {
  USER_FOLLOWERS,
  GET_FRIEND_REQUEST_SENDER,
  RESPONSE_TO_FOLLOWERS,
  UN_FOLLOW,
  BLOCK_IMPRINT,
  BLOCK_USER,
  B<PERSON><PERSON>K_MESSAGE,
} from './ActionTypes';

interface Action {
  type: string;
  payload?: any;
  response?: any;
  responseCallback?: any;
}

export function getUserFriendsRequest(responseCallback: any): Action {
  return {
    responseCallback,
    type: USER_FOLLOWERS.REQUEST,
  };
}

export function getUserFriendsSuccess(response: any): Action {
  return {
    response,
    type: USER_FOLLOWERS.SUCCESS,
  };
}

export function getFriendsRequestSender(responseCallback: any): Action {
  return {
    responseCallback,
    type: GET_FRIEND_REQUEST_SENDER.REQUEST,
  };
}

export function getFriendsRequestSenderSuccess(response: any): Action {
  return {
    response,
    type: GET_FRIEND_REQUEST_SENDER.SUCCESS,
  };
}

export function respondToFollowers(
  payload: IRespondFollower,
  responseCallback: any,
): Action {
  return {
    payload,
    responseCallback,
    type: RESPONSE_TO_FOLLOWERS.REQUEST,
  };
}
export function respondToFollowersSuccess(response: any): Action {
  return {
    response,
    type: RESPONSE_TO_FOLLOWERS.SUCCESS,
  };
}

export function unFollowRequest(
  payload: IUnFollow,
  responseCallback: any,
): Action {
  return {
    payload,
    responseCallback,
    type: UN_FOLLOW.REQUEST,
  };
}

export function unFollowSuccess(response: any): Action {
  return {
    response,
    type: UN_FOLLOW.SUCCESS,
  };
}

export function blockImprintRequest(
  payload: IBlockEntity,
  responseCallback: any,
): Action {
  return {
    payload,
    responseCallback,
    type: BLOCK_IMPRINT.REQUEST,
  };
}
export function blockUserRequest(
  payload: IBlockEntity,
  responseCallback: any,
): Action {
  return {
    payload,
    responseCallback,
    type: BLOCK_USER.REQUEST,
  };
}

export function blockMessageRequest(
  payload: IBlockEntity,
  responseCallback: any,
): Action {
  return {
    payload,
    responseCallback,
    type: BLOCK_MESSAGE.REQUEST,
  };
}
