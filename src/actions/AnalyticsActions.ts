import {
  GET_TIME_OPTIONS,
  GET_USER_PORTFOLIO,
  GET_VALUE_ANALYTICS,
  GET_VALUE_PORTFOLIO,
} from './ActionTypes';

interface Action {
  type: string;
  payload?: any;
  response?: any;
  responseCallback?: any;
}

export function getValueAnalyticsRequest(
  payload: any,
  responseCallback: any,
): Action {
  return {
    payload,
    responseCallback,
    type: GET_VALUE_ANALYTICS.REQUEST,
  };
}

export function getValueAnalyticsSuccess(response: any): Action {
  return {
    response,
    type: GET_VALUE_ANALYTICS.SUCCESS,
  };
}

export function getValuePortfolioRequest(
  payload: any,
  responseCallback: any,
): Action {
  return {
    payload,
    responseCallback,
    type: GET_VALUE_PORTFOLIO.REQUEST,
  };
}

export function getValuePortfolioSuccess(response: any): Action {
  return {
    response,
    type: GET_VALUE_PORTFOLIO.SUCCESS,
  };
}

export function getTimeOptionsRequest(
  payload: any,
  responseCallback: any,
): Action {
  return {
    payload,
    responseCallback,
    type: GET_TIME_OPTIONS.REQUEST,
  };
}

export function getTimeOptionsSuccess(response: any): Action {
  return {
    response,
    type: GET_TIME_OPTIONS.SUCCESS,
  };
}

export function getUserPortFolioRequest(responseCallback: any): Action {
  return {
    responseCallback,
    type: GET_USER_PORTFOLIO.REQUEST,
  };
}
