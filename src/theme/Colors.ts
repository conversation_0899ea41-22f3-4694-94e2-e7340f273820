import {Platform} from 'react-native';
const green = '#188544';
const green1 = '#41A642';
const lightGreen = '#41A6421A';
const white = '#FFFFFF';
const toggleWhite = '#f4f3f4';

const whit1 = '#C4C4C4';
const white2 = '#C3C3C3';
const red = '#FF3E2F';
const blue = '#0078FF';
const lightBlue = '#6396E0';
const lightBlue1 = '#6396E01A';
const black = '#000000';
const black1 = '#101418';
const black2 = '#4D5562';
const black3 = '#393C4C';
const black4 = '#636674';

//const gray = '#B6B6B6';
const lightGray = '#C2CCD6';
const lightGray1 = '#F7F7F7';
const lightGray2 = '#E1EBF9';

const textLight = '#626883';
const uploadGray = '#E6EAEE';
const textGray = '#6C727F';
const leftChatBubble = '#F2F4F5';
const rightChatBubble = '#E7ECEE';

const leftChatText = '#303437';
const rightChatText = '#101418';
const textInputBorder = '#C8D2DA';

const symbolBackground = '#EBEBEB';

const gray4 = 'D2D5DB';

const shoutOutBackground = '#70D3C3';

const gray = '#E8E8E8';
const gray2 = '#E5E6EB';
const gray3 = '#EDF0F3';

const graphLines = '#E3E3E3';

const ageBracketAmber = '#EEBD40';
const ageBracketGreen = '#008000';

const transparent = 'rgba(0,0,0,0)';
const handle = '#C9CCD1';
const borderColor = '#E8E8E8';
const articleBorder = '#E8EAED';

const successBorder = '#C2E999';

const background = {
  home: '#E7ECEE',
  publishActiveColor: '#101418',
  publishInactiveColor: '#E4E6EB',
  primary: '#198544',
  secondary: '#000000',
  tertiary: '#FFFFFF',
  quaternary: '#FF3E2F',
  accent: '#0078FF',
  errorToast: '#F6DFDF',
  article: '#F5F5F5',
  success: '#F8FFEF',
};
const text = {
  publishTextInActiveColor: '#BCC0C4',
  leaveImprintColor: '#668099',
  titleColor: '#393C4C',
  homeTitleColor: '#101418',
  gray: '#4D5562',
  white: '#FFFFFF',
  black: '#000000',
  placeHolderTextColor: '#626883',
  activeBorderColor: '#000',
  primary: '#0E4DA4',
  secondary: '#0E4DA4',
  modalText: '#545963',
};

const symbolColors = {
  life: '#009E65',
  safety: '#B14DE7',
  love: '#EE1F2B',
  laughter: '#FFFC72',
  respect: '#366BFF',
  purpose: '#CEC4B0',
};

const tabsTextColor = {
  activeTabColor: '#393C4C',
  inActiveTabColor: '#668099',
  inActiveFriendList: '#999EB3',
};
const borderColors = {
  activeBorderColor: '#E8E8E8',
  dividerColor: '#4D5562',
  incorrectBorderColor: '#CA0404',
};
const itemColors = {
  titleColor: '#393C4C',
  itemBorderColor: '#D8E4E4',
  itemBackgroundColor: '#F3F6F6',
  subTitleColor: '#626883',
};
const ChatColors = {
  unreadItem: '#D2DCDF',
};

const SubscriptionsItem = {
  goldColor: '#C1900E',
  silverColor: '#BDBEC0',
};

const BlackButton = '#101418';

const presetColors = {
  primary: ['#febb5b', '#f24645'],
  secondary: ['#f24645', '#febb5b'],
  instagram: [
    'rgb(106, 57, 171)',
    'rgb(151, 52, 160)',
    'rgb(197, 57, 92)',
    'rgb(231, 166, 73)',
    'rgb(181, 70, 92)',
  ],
  firefox: [
    'rgb(236, 190, 55)',
    'rgb(215, 110, 51)',
    'rgb(181, 63, 49)',
    'rgb(192, 71, 45)',
  ],
  sunrise: [
    'rgb(92, 160, 186)',
    'rgb(106, 166, 186)',
    'rgb(142, 191, 186)',
    'rgb(172, 211, 186)',
    'rgb(239, 235, 186)',
    'rgb(212, 222, 206)',
    'rgb(187, 216, 200)',
    'rgb(152, 197, 190)',
    'rgb(100, 173, 186)',
  ],
};

const symbolSelectedColors = {
  life: '#E9F9E4',
  safety: '#F2E4F9',
  love: '#F9E8E4',
  laughter: '#F9F7E4',
  respect: '#E4EAF9',
  purpose: '#F9F1E4',
};

const KIN_STATUS_COLORS = {
  REJECTED_BACKGROUND: '#FFEBEE',
  REJECTED_TEXT: '#F65044',
  PENDING_BACKGROUND: '#FFF3E0',
  PENDING_TEXT: '#FF9800',
  ACCEPTED_BACKGROUND: '#C2E999',
  ACCEPTED_TEXT: '#009E65',
};

const navbar = {
  rightTitleColor: '#101418',
  background: transparent,
  text: text.activeBorderColor,
};
const toggleColors = {
  inActiveColor: '#767577',
  activeColor: '#545963',
};
const dateColors = [];
const border = '#f2f2f2';
const separator = '#f2f2f2';

const windowTint = 'rgba(0, 0, 0, 0.4)';
const windowTintWhite = 'rgba(255, 255, 255, 0.1)';

const colorsArray1 = [green];

export default {
  red,
  green,
  blue,
  white,
  whit1,
  black,
  twitter: '#41abe1',
  google: '#e94335',
  facebook: '#3b5998',
  info: '#19bfe5',
  warning: '#feb401',
  danger: '#ed1c4d',
  success: '#F9FFF0',
  colorsArray1,
  borderColors,
  white2,
  text,
  gray,
  lightGray,
  lightGray1,
  textLight,
  handle,
  background,
  BlackButton,
  borderColor,
  uploadGray,
  navbar,
  itemColors,
  tabsTextColor,
  black1,
  toggleColors,
  toggleWhite,
  articleBorder,
  successBorder,
  shoutOutBackground,
  gray2,
  gray3,
  gray4,
  graphLines,
  symbolColors,
  symbolSelectedColors,
  symbolBackground,
  ChatColors,
  leftChatBubble,
  rightChatBubble,
  leftChatText,
  rightChatText,
  textInputBorder,
  ageBracketAmber,
  ageBracketGreen,
  KIN_STATUS_COLORS,
  black2,
  textGray,
  black3,
  SubscriptionsItem,
  lightBlue,
  lightBlue1,
  green1,
  lightGreen,
  lightGray2,
  black4,
};
