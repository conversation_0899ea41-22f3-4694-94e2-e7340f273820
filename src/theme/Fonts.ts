// @flow

const type = {
  regular: 'Poppins-Regular',
  base: 'Poppins-Regular',
  medium: 'Poppins-Medium',
  bold: 'Poppins-Bold',
  extra_bold: 'Poppins-ExtraBold',
  semi_bold: 'Poppins-SemiBold',
  thin: 'Poppins-Thin',
  //sfu fonts
};

// Metrics.generatedFontSize(ios, android)

const size = {
  buttonText: 16,
  xxxxSmall: 10,
  xxxSmall: 11,
  xxSmall: 13,
  xSmall: 14,
  small: 15,
  normal: 16,
  medium: 18,
  Large: 20,
  large: 22,
  xLarge: 24,
  xxLarge: 30,
  xxxLarge: 36,
  xxxxLarge: 40,
};

export default {
  type,
  size,
};
