import {CHARITY_VOTING} from '../constants/StringConstants';

export interface Charity {
  id: string;
  name: string;
  description: string;
}

export interface CharityResult {
  id: string;
  name: string;
  votes: number;
  percentage: number;
  rank: number;
}

export interface VotingState {
  hasVoted: boolean;
  selectedCharityId: string | null;
  votingDay: boolean;
}

// Mock charity data
export const MOCK_CHARITIES: Charity[] = [
  {
    id: 'charity_1',
    name: CHARITY_VOTING.CHARITIES.CHARITY_1.NAME,
    description: CHARITY_VOTING.CHARITIES.CHARITY_1.DESCRIPTION,
  },
  {
    id: 'charity_2',
    name: CHARITY_VOTING.CHARITIES.CHARITY_2.NAME,
    description: CHARITY_VOTING.CHARITIES.CHARITY_2.DESCRIPTION,
  },
  {
    id: 'charity_3',
    name: CHARITY_VOTING.CHARITIES.CHARITY_3.NAME,
    description: CHARITY_VOTING.CHARITIES.CHARITY_3.DESCRIPTION,
  },
];

// Mock voting results (this would come from backend in real implementation)
export const MOCK_VOTING_RESULTS: CharityResult[] = [
  {
    id: 'charity_1',
    name: CHARITY_VOTING.CHARITIES.CHARITY_1.NAME,
    votes: 1250,
    percentage: 45.5,
    rank: 1,
  },
  {
    id: 'charity_2',
    name: CHARITY_VOTING.CHARITIES.CHARITY_2.NAME,
    votes: 980,
    percentage: 35.7,
    rank: 2,
  },
  {
    id: 'charity_3',
    name: CHARITY_VOTING.CHARITIES.CHARITY_3.NAME,
    votes: 515,
    percentage: 18.8,
    rank: 3,
  },
];

// Utility functions
export const isVotingDay = (): boolean => {
  // For demo purposes, let's make it voting day if it's the first 15 days of the month
  const today = new Date();
  const dayOfMonth = today.getDate();
  return dayOfMonth <= 15;
};

export const getCurrentWinner = (): string => {
  const sortedResults = [...MOCK_VOTING_RESULTS].sort((a, b) => b.votes - a.votes);
  return sortedResults[0]?.name || CHARITY_VOTING.CHARITIES.CHARITY_1.NAME;
};

export const getCharityById = (id: string): Charity | undefined => {
  return MOCK_CHARITIES.find(charity => charity.id === id);
};

export const simulateVote = (charityId: string): CharityResult[] => {
  // In a real app, this would make an API call
  // For now, we'll just return the mock results with a slight increase for the voted charity
  return MOCK_VOTING_RESULTS.map(result => {
    if (result.id === charityId) {
      return {
        ...result,
        votes: result.votes + 1,
        percentage: result.percentage + 0.1, // Slight increase
      };
    }
    return result;
  });
};

// Local storage keys for demo purposes
export const VOTING_STORAGE_KEYS = {
  HAS_VOTED: 'charity_voting_has_voted',
  SELECTED_CHARITY: 'charity_voting_selected_charity',
  VOTING_MONTH: 'charity_voting_month',
};

export const hasUserVotedThisMonth = (): boolean => {
  try {
    // In a real app, this would check with the backend
    // For demo, we'll use a simple localStorage check
    const currentMonth = new Date().getMonth();
    const votingMonth = localStorage.getItem(VOTING_STORAGE_KEYS.VOTING_MONTH);
    const hasVoted = localStorage.getItem(VOTING_STORAGE_KEYS.HAS_VOTED);
    
    if (votingMonth && hasVoted) {
      return parseInt(votingMonth) === currentMonth && hasVoted === 'true';
    }
    return false;
  } catch (error) {
    // If localStorage is not available (React Native), return false
    return false;
  }
};

export const markUserAsVoted = (charityId: string): void => {
  try {
    const currentMonth = new Date().getMonth();
    localStorage.setItem(VOTING_STORAGE_KEYS.HAS_VOTED, 'true');
    localStorage.setItem(VOTING_STORAGE_KEYS.SELECTED_CHARITY, charityId);
    localStorage.setItem(VOTING_STORAGE_KEYS.VOTING_MONTH, currentMonth.toString());
  } catch (error) {
    // If localStorage is not available (React Native), we'd use AsyncStorage
    console.log('Vote recorded for charity:', charityId);
  }
};

export const resetVotingForNewMonth = (): void => {
  try {
    localStorage.removeItem(VOTING_STORAGE_KEYS.HAS_VOTED);
    localStorage.removeItem(VOTING_STORAGE_KEYS.SELECTED_CHARITY);
    localStorage.removeItem(VOTING_STORAGE_KEYS.VOTING_MONTH);
  } catch (error) {
    console.log('Voting reset for new month');
  }
};
