import React from 'react';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import {Login, SignUp, SuggestedFriends} from '../containers';
import Onboarding from '../containers/Onboarding';
import Splash from '../components/Auth/Splash/Splash';
import Routes from '../constants/RouteConstants';
import ProfileSetup from '../containers/ProfileSetup';
import styles from './styles';
import QuizProfile from '../containers/QuizProfile';
import QuizFlow from '../containers/QuizFlow';
import OtpScreen from '../containers/OtpScreen';
import EmailScreen from '../containers/ResetPasswordFlow/EmailScreen';
import ResetPasswordScreen from '../containers/ResetPasswordFlow/ResetPasswordScreen';
import ReportScreen from '../components/ReportScreen';
const Stack = createNativeStackNavigator();

const AuthStack = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        contentStyle: styles.contentStyle,
        animation: 'simple_push',
        gestureEnabled: false,
      }}
      initialRouteName={Routes.SPLASH}>
      <Stack.Screen
        name={Routes.SPLASH}
        component={Splash}
        options={{
          headerShown: false,
        }}
      />

      <Stack.Screen
        name={Routes.ONBOARDING}
        component={Onboarding}
        options={{
          headerShown: false,
        }}
      />

      <Stack.Screen
        name={Routes.LOGIN}
        component={Login}
        options={{
          headerShown: false,
        }}
      />

      <Stack.Screen
        name={Routes.RESET_EMAIL_SCREEN}
        component={EmailScreen}
        options={{
          headerShown: false,
        }}
      />

      <Stack.Screen
        name={Routes.RESET_PASSWORD_SCREEN}
        component={ResetPasswordScreen}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name={Routes.OTP_SCREEN}
        component={OtpScreen}
        options={{
          headerShown: false,
        }}
      />

      <Stack.Screen
        name={Routes.PROFILE_SETUP}
        component={ProfileSetup}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name={Routes.SIGNUP}
        component={SignUp}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name={Routes.QUIZ_FLOW}
        component={QuizFlow}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name={Routes.QUIZ_PROFILE}
        component={QuizProfile}
        options={{
          headerShown: false,
        }}
      />

      <Stack.Screen
        name={Routes.SUGGESTED_FRIENDS}
        component={SuggestedFriends}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name={Routes.REPORT_SCREEN}
        component={ReportScreen}
        options={{
          headerShown: false,
        }}
      />
    </Stack.Navigator>
  );
};
export default AuthStack;
