import React from 'react';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import Routes from '../constants/RouteConstants';
import {TimeLineMediaView, SearchUsers, Home} from '../containers';
import UserProfile from '../containers/UserProfile';
import RecentChatList from '../containers/RecentChatList';
import CreateChat from '../containers/CreateChat';
import Chat from '../containers/Chat';
import ReportScreen from '../components/ReportScreen';
import RudiChat from '../components/RudiChat';
import Subscription from '../containers/Subscription';
import Entertainment from '../containers/Entertainment';
import EntertainmentQuiz from '../containers/EntertainmentQuiz';
import NewsScreen from '../containers/NewsScreen';
import AllFreinds from '../containers/Search/AllFreinds';

const Stack = createNativeStackNavigator();

const HomeStack = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        contentStyle: {backgroundColor: 'white'},
        animation: 'simple_push',
      }}
      initialRouteName={Routes.HOME}>
      <Stack.Screen
        name={Routes.HOME}
        component={Home}
        options={{
          gestureEnabled: false,
          headerShown: false,
        }}
      />

      <Stack.Screen
        name={Routes.REPORT_SCREEN}
        component={ReportScreen}
        options={{
          headerShown: false,
        }}
      />

      <Stack.Screen
        name={Routes.TIME_LINE_MEDIA_VIEW}
        component={TimeLineMediaView}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name={Routes.SEARCH}
        component={SearchUsers}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name={Routes.ALL_FRIENDS}
        component={AllFreinds}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name={Routes.USER_PROFILE}
        component={UserProfile}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name={Routes.CHAT_LIST}
        component={RecentChatList}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name={Routes.CREATE_CHAT}
        component={CreateChat}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name={Routes.CHAT}
        component={Chat}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name={Routes.RUDI_MESSAGE}
        component={RudiChat}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name={Routes.SUBSCRIPTION}
        component={Subscription}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name={Routes.ENTERTAINMENT_SCREEN}
        component={Entertainment}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name={Routes.ENTERTAINMENT_QUIZ}
        component={EntertainmentQuiz}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name={Routes.NEWS_SCREEN}
        component={NewsScreen}
        options={{headerShown: false}}
      />
    </Stack.Navigator>
  );
};
export default HomeStack;
