import React from 'react';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import Routes from '../constants/RouteConstants';
import {CheckIn, CreateImprint, PaymentScreen, ViewMedia} from '../containers';
import Subscription from '../containers/Subscription';

const Stack = createNativeStackNavigator();

const CreateImprintStack = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        contentStyle: {backgroundColor: 'white'},
        animation: 'simple_push',
      }}
      initialRouteName={Routes.CREATE_IMPRINT}>
      <Stack.Screen
        name={Routes.CREATE_IMPRINT}
        component={CreateImprint}
        options={{
          gestureEnabled: false,
          headerShown: false,
        }}
      />
      <Stack.Screen
        name={Routes.VIEW_MEDIA}
        component={ViewMedia}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name={Routes.CHECK_IN}
        component={CheckIn}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name={Routes.SUBSCRIPTION}
        component={Subscription}
        options={{headerShown: false}}
      />
    </Stack.Navigator>
  );
};
export default CreateImprintStack;
