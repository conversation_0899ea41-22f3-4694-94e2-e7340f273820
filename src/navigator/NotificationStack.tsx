import React from 'react';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import Routes from '../constants/RouteConstants';
import {TimeLineMediaView} from '../containers';
import Notification from '../containers/Notification';
import UserProfile from '../containers/UserProfile';
import Entertainment from '../containers/Entertainment';
import NewsScreen from '../containers/NewsScreen';
import ReportScreen from '../components/ReportScreen';

import RecentChatList from '../containers/RecentChatList';
import Chat from '../containers/Chat';

type NotificationStackParamList = {
  [Routes.NOTIFICATION]: undefined;
  [Routes.TIME_LINE_MEDIA_VIEW]: undefined;
  [Routes.USER_PROFILE]: undefined;
  [Routes.ENTERTAINMENT_SCREEN]: undefined;
  [Routes.NEWS_SCREEN]: undefined;
  [Routes.REPORT_SCREEN]: {
    url: string;
    title: string;
  };
};

const Stack = createNativeStackNavigator<NotificationStackParamList>();

const NotificationStack = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        contentStyle: {backgroundColor: 'white'},
        animation: 'simple_push',
      }}
      initialRouteName={Routes.NOTIFICATION}>
      <Stack.Screen
        name={Routes.NOTIFICATION}
        component={Notification}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name={Routes.TIME_LINE_MEDIA_VIEW}
        component={TimeLineMediaView}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name={Routes.USER_PROFILE}
        component={UserProfile}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name={Routes.ENTERTAINMENT_SCREEN}
        component={Entertainment}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name={Routes.NEWS_SCREEN}
        component={NewsScreen}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name={Routes.REPORT_SCREEN}
        component={ReportScreen}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name={Routes.CHAT_LIST}
        component={RecentChatList}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name={Routes.CHAT}
        component={Chat}
        options={{headerShown: false}}
      />
    </Stack.Navigator>
  );
};
export default NotificationStack;
