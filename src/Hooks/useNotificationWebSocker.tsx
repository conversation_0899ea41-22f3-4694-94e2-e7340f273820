import {useEffect, useState} from 'react';

interface INotificationWebSocket {
  socket: WebSocket | null;
}

export const useNotificationSocket = (
  getPubSubTokenRequest: (callback: (token: string) => void) => void,
  onNotificationMessage: (message: any) => void,
): INotificationWebSocket => {
  const [notificationSocket, setNotificationSocket] =
    useState<WebSocket | null>(null);

  useEffect(() => {
    getPubSubTokenRequest(token => {
      if (token) {
        console.log('Notification token:', token);
        const socket = new WebSocket(token);

        socket.onopen = () => {
          console.log('Notification socket opened');
          setNotificationSocket(socket);
        };

        socket.onmessage = event => {
          try {
            const parsedData = JSON.parse(event.data);
            onNotificationMessage(parsedData);
          } catch (error) {
            console.error('Failed to parse notification:', error);
          }
        };

        socket.onerror = error => {
          console.error('Notification socket error:', error);
        };

        socket.onclose = () => {
          setNotificationSocket(null);
        };
      }
    });
  }, []);

  return {socket: notificationSocket};
};
