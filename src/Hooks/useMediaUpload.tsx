import _ from 'lodash';
import {useState} from 'react';
import {useNavigation} from '@react-navigation/native';
import {BlobPayload, ImprintMedia, MediaIds, MediaInfo} from '../types';
import util from '../util';
import {IMPRINT_MEDIA_TYPES_MAP} from '../constants';
import {TOAST_MESSAGES} from '../constants/StringConstants';
import {showToastMsg} from '../components/Alert';

const useMediaUploadService = (
  userUpdateImprintRequest: (
    arg0: MediaIds[],
    arg1: (res: any) => void,
  ) => void,
  mentionList: any[],
) => {
  const [response, setResponse] = useState(null);
  const [error, setError] = useState<any>();
  const [loading, setLoading] = useState<boolean>(false);
  const navigation = useNavigation();

  const createImprint = async (
    MediaInfo: MediaInfo[],
    mediaList: ImprintMedia[],
  ) => {
    try {
      const uploadMedia = async () => {
        const MediaIds: MediaIds[] = [];
        if (mediaList.length) {
          setLoading(true);
          const results = await Promise.all(
            MediaInfo.map(async (media, i) => {
              const media_type = util.getType(mediaList[i].mime as string);

              const mediaIdObject: MediaIds = {
                id: media.id,
                isUploaded: true,
                type: media_type,
              };

              if (mentionList.length > 0) {
                mediaIdObject.mentions = mentionList;
              }
              MediaIds.push(mediaIdObject);

              const payload: BlobPayload = {
                uri:
                  media_type == IMPRINT_MEDIA_TYPES_MAP.image
                    ? mediaList[i].sourceURL ?? (mediaList[i].path as string)
                    : (mediaList[i].file as string),
                type: mediaList[i].mime,
                name: mediaList[i].filename,
              };

              const {uri} = payload;
              const blob = await (await fetch(uri)).blob();

              return fetch(media.presigned_url, {
                method: 'PUT',
                body: blob,
                headers: {
                  'Content-Type': mediaList[i].mime || '',
                  'x-ms-blob-type': 'BlockBlob',
                },
              });
            }),
          );
          setResponse(response);
          userUpdateImprintRequest(MediaIds, res => {
            if (res) {
              setLoading(false);
              navigation.goBack();
              showToastMsg(TOAST_MESSAGES.POST_SUCCESS, 'success', 5000, 'top');
            }
            setLoading(false);
          });
        }
      };
      await uploadMedia();
      return response;
    } catch (error) {
      setError(error);
      return error;
    }
  };

  return {createImprint, loading, error, response};
};

export default useMediaUploadService;
