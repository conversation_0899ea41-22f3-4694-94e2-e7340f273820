import _ from 'lodash';
import {connect} from 'react-redux';
import React, {useEffect, useRef, useState} from 'react';
import {SafeAreaView} from 'react-native-safe-area-context';
import {AppButton, ButtonView, Text} from '../../components/';
import {SPLASH_BG} from '../../constants/AssetSVGConstants';
import {BackHandler, View, AppState, AppStateStatus} from 'react-native';
import styles from './styles';
import {AppStyles, Colors, Metrics} from '../../theme';
import {OTP_SCREEN, TOAST_TYPE} from '../../constants/StringConstants';
import {OtpInput} from 'react-native-otp-entry';
import {AnimatedCircularProgress} from 'react-native-circular-progress';
import {navigationRef} from '../../services/RootNavigation';
import Routes from '../../constants/RouteConstants';
import {sendOtpRequest, validateOtpRequest} from '../../actions/UserActions';
import {showToastMsg} from '../../components/Alert';
import {OTP_ENUMS} from '../../types';

interface OtpScreenProps {
  route: {
    params: {
      email: string;
      purpose: string;
    };
  };
  sendOtpRequest: (payload: any, callback: (res: any) => void) => void;
  validateOtpRequest: (payload: any, callback: (res: any) => void) => void;
}

const OtpScreen: React.FC<OtpScreenProps> = ({
  route,
  sendOtpRequest,
  validateOtpRequest,
}) => {
  const [fill, setFill] = useState<string>('');
  const [sec, setSec] = useState(60); // Countdown timer (60 seconds)
  const [active, setActive] = useState(false); // For enabling/disabling Resend button
  const circularProgressRef = useRef<AnimatedCircularProgress>(null);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const appState = useRef<AppStateStatus>(AppState.currentState);
  const timestampRef = useRef<number | null>(null);
  const email = route.params?.email;
  const purpose = route.params?.purpose;

  const handleResend = () => {
    onSendOtpRequest();
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }
    setSec(60);
    timing();
    reAnimate();
  };

  useEffect(() => {
    const backAction = () => {
      return true;
    };
    const backHandler = BackHandler.addEventListener(
      'hardwareBackPress',
      backAction,
    );

    return () => backHandler.remove();
  }, []);

  const onValidateOtp = async (otp: string) => {
    const payload = {
      email: email.toLocaleLowerCase(),
      otp: otp,
      purpose: purpose,
    };
    validateOtpRequest(payload, res => {
      if (res) {
        purpose === OTP_ENUMS.RESET_PASSWORD
          ? navigationRef.current?.navigate(Routes.RESET_PASSWORD_SCREEN, {
              otp: otp,
              email: email,
            })
          : setTimeout(() => {
              showToastMsg(
                OTP_SCREEN.EMAIL_VERIFIED_SUCCESSFULLY,
                TOAST_TYPE.SUCCESS,
              );
              navigationRef.current?.navigate(Routes.LOGIN);
            }, 500);
      }
    });
  };

  const onSendOtpRequest = () => {
    const payload = {
      email: email,
      purpose: purpose,
    };
    sendOtpRequest(payload, '');
  };

  useEffect(() => {
    if (route.params.purpose === OTP_ENUMS.USER_VERIFICATION) {
      onSendOtpRequest();
    }
  }, []);

  const timing = () => {
    setActive(false); // Set button inactive when the timer starts
    intervalRef.current = setInterval(() => {
      setSec(prevSec => {
        if (prevSec <= 1) {
          clearInterval(intervalRef.current as NodeJS.Timeout);
          setActive(true); // Activate the button when timer reaches 0
          return 0;
        }
        return prevSec - 1;
      });
    }, 1000);
  };

  useEffect(() => {
    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      if (
        appState.current === 'active' &&
        nextAppState.match(/inactive|background/)
      ) {
        timestampRef.current = Date.now(); // Save timestamp when app goes to background
      } else if (
        appState.current.match(/inactive|background/) &&
        nextAppState === 'active'
      ) {
        if (timestampRef.current) {
          const elapsedTime = Math.floor(
            (Date.now() - timestampRef.current) / 1000,
          );
          setSec(prevSec => Math.max(prevSec - elapsedTime, 0)); // Update timer based on elapsed time
        }
      }
      appState.current = nextAppState;
    };

    const subscription = AppState.addEventListener(
      'change',
      handleAppStateChange,
    );

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
      subscription.remove();
    };
  }, []);

  const reAnimate = () => {
    circularProgressRef.current?.reAnimate(0, 100, 60000);
  };

  useEffect(() => {
    timing();
    reAnimate();
  }, []);

  const headerText = () => (
    <>
      <Text
        type="semi_bold"
        size="large"
        color={Colors.black}
        style={styles.headerText}>
        {OTP_SCREEN.OTP_VERIFICATION}
      </Text>
      <Text
        type="semi_bold"
        size="small"
        color={Colors.black}
        textAlign="left"
        style={{marginHorizontal: 20, marginVertical: 10}}>
        {OTP_SCREEN.ENTER_CODE}
        {email}
      </Text>
    </>
  );

  const footerText = () => (
    <View style={styles.footerTextContainer}>
      <View style={[AppStyles.flexRow, AppStyles.alignItemsCenter]}>
        <Text color={Colors.text.black} type="medium" size="xSmall">
          {OTP_SCREEN.DIDNT_RECEIVE_CODE}
        </Text>
        <ButtonView onPress={handleResend} disabled={!active}>
          <Text
            color={active ? Colors.text.black : Colors.textGray}
            type="semi_bold"
            textAlign="center"
            size="xSmall"
            style={AppStyles.mLeft5}>
            {OTP_SCREEN.RESEND}
          </Text>
        </ButtonView>
        <View style={AppStyles.mLeft10}>
          <AnimatedCircularProgress
            ref={circularProgressRef}
            size={50}
            style={styles.circularStyle}
            width={2}
            duration={60000}
            fill={Number(fill)}
            tintColor={Colors.black4}
            backgroundColor={Colors.gray}>
            {() => (
              <Text size="xxxSmall" color={Colors.black}>
                {sec}s
              </Text>
            )}
          </AnimatedCircularProgress>
        </View>
      </View>
    </View>
  );

  return (
    <SafeAreaView>
      <View style={styles.container}>
        <SPLASH_BG />
      </View>
      {headerText()}
      <OtpInput
        numberOfDigits={6}
        focusColor={Colors.black}
        autoFocus={true}
        hideStick={true}
        blurOnFilled={true}
        disabled={false}
        type="numeric"
        secureTextEntry={false}
        focusStickBlinkingDuration={500}
        onTextChange={text => setFill(text)}
        // onFilled={async text => await onValidateOtp(Number(text))}
        onFilled={async text => await onValidateOtp(text)}
        textInputProps={{
          accessibilityLabel: 'One-Time Password',
        }}
        theme={{
          containerStyle: {
            marginVertical: Metrics.ratio(40),
            marginHorizontal: Metrics.ratio(20),
            alignSelf: 'center',
            width: Metrics.screenWidth - 80,
          },
        }}
      />

      {footerText()}
    </SafeAreaView>
  );
};

const mapStateToProps = () => ({});

const actions = {sendOtpRequest, validateOtpRequest};

export default connect(mapStateToProps, actions)(OtpScreen);
