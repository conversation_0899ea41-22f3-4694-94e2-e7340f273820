import React, {useState} from 'react';
import {View, ActivityIndicator, FlatList, Pressable} from 'react-native';
import {Avatar, CustomNavbar, SearchBar, Text} from '../../components';
import {Colors, AppStyles} from '../../theme';
import styles from './styles';
import {useNavigation} from '@react-navigation/native';
import {userSearchRequest} from '../../actions/UserActions';
import {ISearchPayload, SEARCH_USERS} from '../../types';
import {connect} from 'react-redux';
import {SafeAreaView} from 'react-native-safe-area-context';
import {PLACEHOLDER_IMAGE} from '../../constants';
import {SEARCH} from '../../constants/StringConstants';
import Routes from '../../constants/RouteConstants';

interface ActionsProps {
  userSearchRequest: (
    payload: ISearchPayload,
    callback: (res: any) => void,
  ) => void;
}

const SearchUsers: React.FC<ActionsProps> = ({userSearchRequest}) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [usersList, setUserList] = useState<SEARCH_USERS[]>([]);
  const [search, setSearchText] = useState<string>('');

  const navigation = useNavigation();

  const onSearchText = (searchText: string) => {
    setSearchText(searchText);
    setTimeout(() => {
      const payload: ISearchPayload = {
        displayName: searchText,
      };
      setLoading(true);
      userSearchRequest(payload, res => {
        if (res) {
          setUserList(res);
          setLoading(false);
        }
      });
    }, 100);
  };

  const navToUserProfile = (item: SEARCH_USERS) => {
    (navigation.navigate as (route: string, params: any) => void)(
      Routes.USER_PROFILE,
      {userName: item.userName},
    );
  };

  const RenderItem = ({item}: {item: SEARCH_USERS}) => {
    return (
      <Pressable
        style={styles.userinfoContainer}
        onPress={() => navToUserProfile(item)}>
        <Avatar
          image={item.avatarUrl ?? PLACEHOLDER_IMAGE}
          style={styles.imageContainer}
          imageStyle={styles.image}
        />
        <View style={AppStyles.alignStart}>
          <Text
            color={Colors.itemColors.titleColor}
            textAlign="center"
            style={AppStyles.mLeft10}
            type="bold"
            size={'buttonText'}>
            {item.displayName}
          </Text>
          <Text
            color={Colors.text.placeHolderTextColor}
            style={AppStyles.mLeft10}
            type="base"
            size={'xSmall'}>
            {item.userName ?? ''}
          </Text>
        </View>
      </Pressable>
    );
  };
  return (
    <SafeAreaView style={styles.container}>
      <CustomNavbar
        title={SEARCH.SEARCH}
        style={styles.navBarStyle}
        leftBtnPress={() => navigation.goBack()}
      />
      <SearchBar
        autoFocus={true}
        placeholder={SEARCH.SEARCH}
        onSearchText={text => {
          onSearchText(text);
        }}
        value={search}
      />
      <View style={styles.container}>
        {loading && (
          <ActivityIndicator
            size="small"
            color={Colors.black}
            style={AppStyles.mTop10}
          />
        )}

        <FlatList
          contentContainerStyle={AppStyles.flexGrow}
          showsVerticalScrollIndicator={false}
          data={usersList}
          renderItem={RenderItem}
          keyExtractor={(item, index) => item.id.toString()}
        />
      </View>
    </SafeAreaView>
  );
};

const mapStateToProps = () => ({});

const actions = {userSearchRequest};

export default connect(mapStateToProps, actions)(SearchUsers);
