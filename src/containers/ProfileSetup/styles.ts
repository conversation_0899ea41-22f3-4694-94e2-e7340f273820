import {StyleSheet} from 'react-native';
import {Colors, Fonts, Metrics} from '../../theme';

export default StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
  },
  doneButton: {
    paddingHorizontal: Metrics.ratio(100),
    marginTop: Metrics.ratio(20),
  },
  congratsText: {
    fontFamily: Fonts.type.regular,
    fontSize: Metrics.ratio(16),
    lineHeight: Metrics.ratio(24),
    textAlign: 'center',
    paddingTop: Metrics.ratio(16),
    color: Colors.text.modalText,
    paddingHorizontal: Metrics.ratio(32),
  },
  gif: {
    alignSelf: 'center',
    marginTop: -10,
    backgroundColor: 'transparent',
  },
  title: {
    fontFamily: Fonts.type.bold,
    fontSize: Metrics.ratio(22),
    lineHeight: Metrics.ratio(18),
    textAlign: 'center',
    zIndex: 999,
    color: Colors.black,
    paddingTop: Metrics.ratio(16),
  },
  cancelButton: {
    backgroundColor: Colors.white,
    paddingHorizontal: Metrics.ratio(100),
    marginTop: Metrics.ratio(20),
  },
});
