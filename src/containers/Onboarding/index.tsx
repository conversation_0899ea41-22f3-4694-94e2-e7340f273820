import React from 'react';
import {View, Text, TouchableOpacity, Platform} from 'react-native';
import Swiper from 'react-native-swiper';
import ImageTextComponent from '../../components/Onboarding/ImageText';
import {styles} from './styles';
import {
  ONBOARDING_1,
  ONBOARDING_2,
  ONBOARDING_3,
  SPLASH_BG,
} from '../../constants/AssetSVGConstants';
import {ONBOARDING} from '../../constants/StringConstants';
import {AppButton} from '../../components';
import {useNavigation} from '@react-navigation/native';
import Routes from '../../constants/RouteConstants';
import {setOnBoardingStatus} from '../../helpers/UserHelper';
import {Colors, Metrics} from '../../theme';

const Onboarding = () => {
  const navigation = useNavigation();

  const slides = [
    {
      index: 1,
      title: ONBOARDING.FIND_IMPRINTS,
      text: ONBOARDING.TEXT_ONE,
      image: <ONBOARDING_1 />,
    },
    {
      index: 2,
      title: ONBOARDING.MEET_AWESOME_PEOPLE,
      text: ONBOARDING.TEXT_TWO,
      image: <ONBOARDING_2 />,
    },
    {
      index: 3,
      title: ONBOARDING.IMPRINTS_WITH_FRIENDS,
      text: ONBOARDING.TEXT_THREE,
      image: <ONBOARDING_3 />,
    },
  ];

  const onSkipPressed = () => {
    setOnBoardingStatus(true);
    (navigation.navigate as (route: string) => void)(Routes.LOGIN);
  };

  const onCreateAccountPressed = () => {
    setOnBoardingStatus(true);
    (navigation.navigate as (route: string) => void)(Routes.SIGNUP);
  };

  const onSignInPressed = () => {
    setOnBoardingStatus(true);
    (navigation.navigate as (route: string) => void)(Routes.LOGIN);
  };

  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={styles.skipButtonContainer}
        onPress={onSkipPressed}>
        <Text style={styles.skipText}>{ONBOARDING.SKIP}</Text>
      </TouchableOpacity>
      <View style={{position: 'absolute', alignSelf: 'center'}}>
        <SPLASH_BG />
      </View>
      <Swiper
        loop={false}
        showsPagination={true}
        dotColor="transparent"
        dotStyle={styles.dot}
        containerStyle={
          Platform.OS === 'android'
            ? {height: Metrics.screenHeight * 0.82}
            : styles.containerStlye
        }
        activeDotColor="transparent"
        activeDotStyle={styles.activeDot}>
        {slides.map(slide => (
          <View key={slide.index}>
            <ImageTextComponent
              svgImage={slide.image}
              title={slide.title}
              text={slide.text}
            />
          </View>
        ))}
      </Swiper>
      <View>
        <AppButton
          text={ONBOARDING.CREATE_ACCOUNT}
          textColor={Colors.white}
          onPress={onCreateAccountPressed}
          buttonStye={styles.createAccountButtonStyle}
        />
        <AppButton
          text={ONBOARDING.SIGN_IN}
          textColor={Colors.black}
          onPress={onSignInPressed}
          buttonStye={styles.signInButtonStyle}
        />
      </View>
    </View>
  );
};

export default Onboarding;
