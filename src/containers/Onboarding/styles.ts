import {Dimensions, StyleSheet} from 'react-native';
import {Colors, Fonts, Metrics} from '../../theme';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  skipButtonContainer: {
    position: 'absolute',
    width: Dimensions.get('window').width * 0.25,
    height: Dimensions.get('window').height * 0.05,
    zIndex: 1,
    alignSelf: 'flex-end',
    alignItems: 'center',
    justifyContent: 'center',
    top: Dimensions.get('window').height * 0.06,
  },
  dot: {
    width: Metrics.ratio(5),
    height: Metrics.ratio(5),
    borderRadius: Metrics.ratio(5),
    marginHorizontal: Metrics.ratio(5),
    backgroundColor: Colors.lightGray,
  },
  activeDot: {
    width: Metrics.ratio(15),
    height: Metrics.ratio(5),
    borderRadius: Metrics.ratio(5),
    marginHorizontal: Metrics.ratio(5),
    backgroundColor: Colors.black,
  },
  skipText: {
    color: Colors.black,
    fontFamily: Fonts.type.semi_bold,
    fontSize: Metrics.ratio(16),
    textAlign: 'right',
  },
  signInButtonStyle: {
    backgroundColor: Colors.white,
    color: Colors.black,
  },
  createAccountButtonStyle: {
    marginBottom: 10,
  },
  containerStlye: {
    flex: 0,
    height: Metrics.screenHeight * 0.78,
  },
});
