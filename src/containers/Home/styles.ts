// @flow
import {Dimensions, StyleSheet} from 'react-native';
import {Colors, Fonts, Metrics} from '../../theme';
import {getStatusBarHeight} from 'react-native-iphone-x-helper';

export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.home,
    paddingTop: 0,
  },
  statusBar: {
    backgroundColor: Colors.white,
    height: getStatusBarHeight() + 30,
  },
  statusBarWithoutNotch: {
    backgroundColor: Colors.white,
    height: 30,
  },
  flastList: {
    paddingBottom: Metrics.screenHeight * 0.15,
  },
  pdfModal: {
    alignItems: 'center',
    justifyContent: 'center',
    width: Metrics.screenWidth,
    height: Metrics.screenHeight,
    backgroundColor: '#000000AB',
  },
  pdf: {
    height: Metrics.screenHeight * 0.8,
    width: Metrics.screenWidth - 40,
    borderRadius: 6,
  },
  modalParentContainer: {
    marginTop: 0,
  },
  modalContainer: {
    marginTop: Metrics.ratio(25),
    marginHorizontal: Metrics.ratio(40),
    flexDirection: 'row',
  },
  modalTextContainer: {
    paddingLeft: Metrics.ratio(20),
    flex: 1,
    justifyContent: 'center',
  },
  modalTextOne: {
    fontFamily: Fonts.type.semi_bold,
    fontSize: Metrics.ratio(13),
    color: Colors.text.titleColor,
  },
  modalTextTwo: {
    fontFamily: Fonts.type.medium,
    fontSize: Metrics.ratio(11),
    color: Colors.textLight,
  },
  modalOptionSeperator: {
    marginTop: Metrics.ratio(10),
    backgroundColor: Colors.lightGray,
    height: 0.5,
    width: Metrics.screenWidth - 40,
    alignSelf: 'center',
  },
  pdfStyle: {
    height: Metrics.screenHeight * 0.8,
    width: Metrics.screenWidth - 40,
    borderRadius: 6,
  },
  kinContainer: {
    height: Metrics.screenHeight,
    width: Metrics.screenWidth,
    backgroundColor: 'transparent',
    position: 'absolute',
    zIndex: 1000,
  },
});
