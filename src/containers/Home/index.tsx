import React, {useCallback, useEffect, useRef, useState} from 'react';
import {
  View,
  Animated,
  Modal,
  Linking,
  NativeSyntheticEvent,
  Alert,
} from 'react-native';
import {
  CustomNavbar,
  Loader,
  ReportModal,
  Tabs,
  ViolationModal,
} from '../../components';
import {
  ENTERTAINMENT_ICON,
  INBOX_ICON,
  NEWS_ICON,
  SEARCH_ICON,
} from '../../constants/AssetSVGConstants';
import styles from './styles';
import ImprintScoresList from '../../components/ImprintScoresList';
import TimeLineCard from '../../components/TimeLine/TimeLineCard';
import {
  resetTimeLine,
  globalTimeLineRequest,
  personalTimeLineRequest,
  postReactionRequest,
  imprintVerificationRequest,
  imprintShoutoutRequest,
  imprintBookmarkRequest,
  requestFollowRequest,
  deleteImprintRequest,
  deleteImprintSuccess,
  postReactionSuccess,
  reportContentRequest,
  updateTimelineRequest,
} from '../../actions/TimelineActions';
import {blockImprintRequest} from '../../actions/FollowersActions';
import {userInfoRequest, userLogout} from '../../actions/UserActions';

import {connect} from 'react-redux';
import {
  useFocusEffect,
  useIsFocused,
  useNavigation,
} from '@react-navigation/native';
import {
  ITimeLine,
  ImprintMedia,
  PostReactionPayload,
  UserImprintInteractions,
  UserState,
  ReactionType,
  IDeleteImprintPayload,
  IBlockEntity,
  IReportContentPayload,
  REPORT_ENUMS,
  ImprintNotification,
  NotificationTypeEnum,
} from '../../types';
import Routes from '../../constants/RouteConstants';
import util, {easeInEaseOut, isUserSubscribed} from '../../util';
import {FlatList, TouchableOpacity} from 'react-native-gesture-handler';
import {showAlertMsg, showToastMsg} from '../../components/Alert';
import {
  COMMON,
  HOME,
  IMPRINT_MEDIA,
  IMPRINT_TIMELINE,
  REPORT_CONTENT,
  TOAST_MESSAGES,
  TOAST_TYPE,
} from '../../constants/StringConstants';
import {AnimatedFlashList} from '@shopify/flash-list';
import {hasNotch} from 'react-native-device-info';
import RenderModal from '../../components/Common/MoreOption';
import NextOfKin from '../../components/NextOfKin';
import FileViewer from '../../components/FileViewer';
import AudioRecorderPlayer from 'react-native-audio-recorder-player';
import ReportBadgeModal from '../../components/ReportContent/ReportBadgeModal';
import {getPubSubTokenRequest} from '../../actions/NotificationActions';
import {useNotificationSocket} from '../../Hooks/useNotificationWebSocker';
import {TOAST_VISIBILITY_TIMEOUT} from '../../constants';
import {VotingThanksModal} from '../../components/VotingThanks';
const audioRecorderPlayer = new AudioRecorderPlayer();

interface Props {
  userLogout: () => void;
  getPubSubTokenRequest: (callback: (res: any) => void) => void;
  resetTimeLine: () => void;
  reportContentRequest: (
    payload: IReportContentPayload,
    callback: (res: any) => void,
  ) => void;

  globalTimeLineRequest: (payload: any, callback: (res: any) => void) => void;
  deleteImprintRequest: (
    payload: IDeleteImprintPayload,
    callback: (res: any) => void,
  ) => void;

  personalTimeLineRequest: (payload: any, callback: (res: any) => void) => void;
  postReactionRequest: (payload: any, callback: (res: any) => void) => void;
  imprintShoutoutRequest: (payload: any, callback: (res: any) => void) => void;
  imprintVerificationRequest: (
    payload: any,
    callback: (res: any) => void,
  ) => void;
  imprintBookmarkRequest: (payload: any, callback: (res: any) => void) => void;
  requestFollowRequest: (payload: any, callback: (res: any) => void) => void;
  userInfoRequest: (response: (res: any) => void) => void;
  blockImprintRequest: (
    payload: IBlockEntity,
    callback: (res: any) => void,
  ) => void;

  deleteImprintSuccess: (response: (res: any) => void) => void;
  updateTimelineRequest: (payload: any) => void;
  globalTimeLine: any;
  personalTimeLine: any;
  user: UserState;
  route: any;
}

const Home: React.FC<Props> = ({
  getPubSubTokenRequest,
  resetTimeLine,
  globalTimeLineRequest,
  personalTimeLineRequest,
  postReactionRequest,
  imprintVerificationRequest,
  imprintShoutoutRequest,
  imprintBookmarkRequest,
  requestFollowRequest,
  userInfoRequest,
  deleteImprintRequest,
  blockImprintRequest,
  deleteImprintSuccess,
  reportContentRequest,
  updateTimelineRequest,
  userLogout,
  globalTimeLine,
  personalTimeLine,
  user,
  route,
}) => {
  const [isPersonal, setPersonal] = useState<boolean>(false);
  const [scores, setScores] = useState<string[]>([]);
  const [page, setPage] = useState<number>(1);
  const navigation = useNavigation();
  const animatedValue = new Animated.Value(0);
  const [personalMorePage, setpersonalMorePage] = useState<boolean>(true);
  const [globalMorePages, setglobalMorePages] = useState<boolean>(true);
  const [pdfURL, setPdfURL] = useState<string>('');
  const [visibleModal, setVisibleModal] = useState(false);
  let scrolled = useRef(false).current;
  const isFocused = useIsFocused();
  const [followId, setFollowId] = useState<string>('');
  const [imprintData, setImprintData] = useState<ITimeLine>();
  const flatListRef = useRef<FlatList>(null);
  const [offset, SetOffset] = React.useState(0);

  const [votingModal, setVotingModal] = React.useState(false);

  const [loading, setLoading] = useState<boolean>(false);

  const [showKin, setShowKin] = useState<boolean>(false);
  const [showReportModal, setShowReportModal] = useState<boolean>(false);
  const [showViolation, setShowViolation] = useState<boolean>(false);
  const [showBadgeModal, setBadgeModal] = useState<boolean>(false);

  const [ageGroup, setAgeGroup] = useState<string>('');
  const [reportType, setReportType] = useState<string>('');

  const [blockRecording, setBlockRecording] = useState<boolean>(false);
  const activeAudioRef = useRef<{onStopPlay?: () => void} | null>(null);
  const [isNavigatingToSubscription, setIsNavigatingToSubscription] =
    useState<boolean>(false);

  useNotificationSocket(
    getPubSubTokenRequest,
    (notification: ImprintNotification) => {
      if (
        [NotificationTypeEnum.IMPRINT_LIVE].includes(
          notification.category as NotificationTypeEnum,
        )
      ) {
        setTimeout(() => {
          updateTimelineRequest({
            id: notification.imprintId,
            isGlobal: !isPersonal,
          });
        }, 1500);
      }
      if (
        [
          NotificationTypeEnum.ACCOUNT_SUSPENDED,
          NotificationTypeEnum.USER_ACCOUNT_SUSPENDED_YEARLY,
          NotificationTypeEnum.SUBSCRIPTION_CANCELLED_USER_BANNED,
        ].includes(notification.category as NotificationTypeEnum)
      ) {
        userInfoRequest(() => {});
        setTimeout(() => {
          util.handleUserLogout(
            userLogout,
            navigation.navigate(Routes.LOGIN as never),
          );
        }, 5000);
      }
      if (
        [
          NotificationTypeEnum.MESSAGING_RESTRICTED_ALL,
          NotificationTypeEnum.MESSAGING_RESTRICTED_UNDER18,
        ].includes(notification.category as NotificationTypeEnum)
      ) {
        userInfoRequest(() => {});
      }
    },
  );

  const startAnimation = () => {
    Animated.timing(animatedValue, {
      toValue: 1,
      duration: 500,
      useNativeDriver: true,
    }).start();
  };

  const endAnimation = () => {
    Animated.timing(animatedValue, {
      toValue: 0,
      duration: 500,
      useNativeDriver: true,
    }).start();
  };

  useEffect(() => {
    if (isFocused) {
      getTimeLineData();
      userInfoRequest(() => {});
    }
  }, [page, isPersonal, scores, isFocused]);

  useEffect(() => {
    const parentNav = navigation.getParent();

    if (parentNav) {
      const unsubscribe = parentNav.addListener('tabPress', e => {
        let wait = new Promise(resolve => setTimeout(resolve, 100));
        wait.then(() => {
          flatListRef.current?.scrollToIndex({
            animated: true,
            index: 0,
          });
        });
      });
      getTimeLineData();
      return () => {
        if (unsubscribe) {
          unsubscribe();
        }
      };
    }
  }, [navigation, isPersonal]);

  useEffect(() => {
    if (route?.params?.newPostPublished) {
      setPage(1);
      setglobalMorePages(true);
      setpersonalMorePage(true);
      setTimeout(() => {
        getTimeLineData();
        navigation.setParams({newPostPublished: false} as any);
      }, 500);
    }
  }, [route?.params?.newPostPublished]);

  useFocusEffect(
    useCallback(() => {
      setPage(1);
      setglobalMorePages(true);
      setpersonalMorePage(true);

      getTimeLineData();
    }, []),
  );

  const getTimeLineData = () => {
    userInfoRequest(() => {});
    if (!isPersonal) {
      const payload = {page, scores: scores.length ? scores : []};
      requestAnimationFrame(() => {
        if (globalMorePages) {
          globalTimeLineRequest(payload, (res: ITimeLine[]) => {
            if (res != null && res.length < 10) {
              setglobalMorePages(false);
            }
            endAnimation();
          });
        }
      });
    } else {
      const payload = {page, scores};
      requestAnimationFrame(() => {
        if (personalMorePage) {
          personalTimeLineRequest(payload, (res: ITimeLine[]) => {
            if (res && res.length < 10) {
              setpersonalMorePage(false);
            }
            endAnimation();
          });
        }
      });
    }
  };

  const togglePersonal = (isPersonalSelected: boolean) => {
    if (isPersonalSelected === isPersonal) return;
    setPage(1);
    scrolled = false;
    setglobalMorePages(true);
    setpersonalMorePage(true);
    setScores([]);
    resetTimeLine();
    easeInEaseOut();
    setPersonal(!isPersonal);
    audioRecorderPlayer.stopPlayer();
  };

  const handleReactionClicked = useCallback(
    (
      reaction?: string,
      userImprintInteraction?: UserImprintInteractions,
      item?: ITimeLine,
    ) => {
      if (reaction === IMPRINT_TIMELINE.BOOKMARK) {
        const payload = {
          imprintId: item?.id,
          isBookmark: !userImprintInteraction?.isBookmarked,
          isGlobal: !isPersonal,
        };
        imprintBookmarkRequest(payload, () => {});
      } else {
        if (!item && !userImprintInteraction) return;

        const payload: PostReactionPayload = {
          isHide: false,
          imprintId: item?.id,
          email: user.userInfo.email,
          reaction:
            userImprintInteraction?.reaction === reaction
              ? userImprintInteraction?.reaction
              : reaction === ReactionType.SHOUT_OUT ||
                reaction === IMPRINT_TIMELINE.FLAG ||
                reaction === IMPRINT_TIMELINE.BOOKMARK
              ? userImprintInteraction?.reaction
              : reaction,
          isRedFlagged:
            reaction === IMPRINT_TIMELINE.FLAG
              ? !userImprintInteraction?.isRedFlagged
              : userImprintInteraction?.isRedFlagged ?? false,
        };

        if (userImprintInteraction?.reaction === reaction) {
          payload.reaction = null;
        }

        payload.isGlobal = !isPersonal;

        if (reaction === ReactionType.SHOUT_OUT) {
          util.shoutOutAlert(() => {
            const shoutoutPayload = {
              imprintId: item?.id,
            };
            imprintShoutoutRequest(shoutoutPayload, () => {});
          });
        } else if (reaction === IMPRINT_TIMELINE.FLAG) {
          setImprintData(item);

          item?.userId === user.data.userId
            ? setBadgeModal(true)
            : setShowReportModal(true);
          // setBlockAlert(true);
        } else {
          if (payload.isRedFlagged) {
            delete payload.isRedFlagged;
          }
          postReactionRequest(payload, () => {});
        }
      }
    },
    [
      postReactionRequest,
      imprintShoutoutRequest,
      user?.data?.userId,
      isPersonal,
    ],
  );

  const handleMediaClicked = useCallback((item?: ITimeLine) => {
    if (!item) return;
    if (
      item?.imprintMedias.length == 1 &&
      item?.imprintMedias[0]?.type == IMPRINT_MEDIA.DOCUMENT
    ) {
      setPdfURL(item?.imprintMedias[0]?.url || '');
    } else {
      (navigation.navigate as (route: string, params: any) => void)(
        Routes.TIME_LINE_MEDIA_VIEW,
        {item},
      );
    }
  }, []);

  const handleScoreClick = (scoreName: string) => {
    if (!isUserSubscribed(user as UserState)) {
      showToastMsg(
        TOAST_MESSAGES.SUBSCRIPTION_ERROR,
        TOAST_TYPE.DEFAULT,
        TOAST_VISIBILITY_TIMEOUT,
        'top',
      );
      return;
    }
    const updatedScores = scores.includes(scoreName)
      ? scores.filter(score => score !== scoreName)
      : [...scores, scoreName];
    setpersonalMorePage(true);
    setglobalMorePages(true);
    setPage(1);
    setScores(updatedScores);
  };

  const onMoreOptionClick = (imprint?: ITimeLine) => {
    if (!isUserSubscribed(user)) {
      showToastMsg(
        TOAST_MESSAGES.SUBSCRIPTION_ERROR,
        TOAST_TYPE.DEFAULT,
        TOAST_VISIBILITY_TIMEOUT,
        'top',
      );
      return;
    }

    setFollowId(imprint?.userId || '');

    setImprintData(imprint);

    setVisibleModal(true);
  };

  const handleVerifyClick = (imprint?: ITimeLine) => {
    if (!imprint) return;

    if (!isUserSubscribed(user as UserState)) {
      showToastMsg(
        TOAST_MESSAGES.SUBSCRIPTION_ERROR,
        TOAST_TYPE.DEFAULT,
        TOAST_VISIBILITY_TIMEOUT,
        'top',
      );
      return;
    }

    if (
      imprint?.verificationRequest &&
      imprint?.verificationRequest?.verificationStatus ===
        IMPRINT_TIMELINE.REQUESTED
    ) {
      showToastMsg(TOAST_MESSAGES.ALREADY_REQUESET);
    } else if (
      imprint?.verificationRequest?.verificationStatus ===
      IMPRINT_TIMELINE.FAILED
    ) {
      showToastMsg(TOAST_MESSAGES.FAILED);
    } else if (
      imprint?.verificationRequest?.verificationStatus ===
      IMPRINT_TIMELINE.VERIFIED
    ) {
      showToastMsg(TOAST_MESSAGES.POST_VERIFIED);
    } else if (
      imprint?.verificationRequest?.verificationStatus ===
      IMPRINT_TIMELINE.REQUEST_DENIED
    ) {
      showToastMsg(TOAST_MESSAGES.REQUEST_DENIED);
    } else {
      showAlertMsg(HOME.ARTICLE_TITLE, HOME.ARTICLE_DETAIlS, [
        {
          text: COMMON.YES,
          onPress: () => {
            const payload = {
              id: imprint?.id,
              isGlobal: !isPersonal,
            };

            imprintVerificationRequest(payload, (res: any) => {
              if (res) {
                showToastMsg(
                  TOAST_MESSAGES.VERIFICATION_REQUEST_SENT,
                  TOAST_TYPE.SUCCESS,
                );
              }
            });
          },
          style: 'default',
        },
        {
          text: COMMON.NO,
          onPress: () => {},
          style: 'cancel',
        },
      ]);
    }
  };

  const renderPDF = (pdfURL: string) => {
    return (
      <Modal
        animationType="fade"
        transparent={true}
        visible={pdfURL !== ''}
        onRequestClose={() => {
          setPdfURL('');
        }}>
        <TouchableOpacity
          onPressOut={() => setPdfURL('')}
          style={styles.pdfModal}>
          <View style={styles.pdfStyle}>
            <FileViewer pdfURL={pdfURL} />
          </View>
        </TouchableOpacity>
      </Modal>
    );
  };

  const hideModal = () => {
    setVisibleModal(!visibleModal);
  };

  const handleOnPressArticle = (article: ImprintMedia) => {
    if (!isUserSubscribed(user as UserState)) {
      showToastMsg(
        TOAST_MESSAGES.SUBSCRIPTION_ERROR,
        TOAST_TYPE.DEFAULT,
        TOAST_VISIBILITY_TIMEOUT,
        'top',
      );
      return;
    } else {
      Alert.alert(HOME.DISCLAIMER, HOME.DISCLAIMER_TEXT, [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Redirect',
          onPress: () => Linking.openURL(article.url ?? ''),
        },
      ]);
    }

    // Linking.openURL(article.url ?? '');
  };

  const onUserNameClick = (userName?: string) => {
    if (userName !== user?.userInfo?.userName) {
      (navigation.navigate as (route: string, params: any) => void)(
        Routes.USER_PROFILE,
        {userName},
      );
    } else {
      (navigation.navigate as (route: string) => void)(Routes.USER_PROFILE);
    }
  };

  const handleCrossClicked = () => {
    setShowKin(false);
  };

  const handleStopPlay = () => {
    if (activeAudioRef.current?.onStopPlay) {
      activeAudioRef.current.onStopPlay();
    }
  };

  useEffect(() => {
    return () => {
      // Cleanup when the Home component unmounts
      handleStopPlay();
    };
  }, [!isFocused]);

  const onFollowPress = (userId: string) => {
    const payload = {
      to: userId,
      isGlobal: !isPersonal,
    };
    requestFollowRequest(payload, (res: any) => {
      if (res) {
        showToastMsg(TOAST_MESSAGES.FOLLOW_REQUEST_SENT, TOAST_TYPE.SUCCESS);
      }
      setVisibleModal(false);
    });
  };

  const onDeletePress = () => {
    setLoading(true);
    setVisibleModal(false);

    const payload: IDeleteImprintPayload = {
      imprintId: imprintData?.id || '',
    };
    deleteImprintRequest(payload, res => {
      if (res) {
        showToastMsg(
          TOAST_MESSAGES.DELETE_IMPRINT_MESSAGE,
          TOAST_TYPE.SUCCESS,
          3000,
          'top',
        );
        setLoading(false);
      }
    });
    setLoading(false);
  };

  const onBlockPress = () => {
    const payload: IBlockEntity = {
      follower: imprintData?.userId || '',
      status: true,
    };
    setVisibleModal(false);
    setTimeout(() => {
      setBlockRecording(true);
      blockImprintRequest(payload, res => {
        if (res) {
          const obj = {
            userId: imprintData?.userId || '',
          };
          deleteImprintSuccess(obj);
          setVisibleModal(false);
          setBlockRecording(false);
          showToastMsg(
            TOAST_MESSAGES.BLOCK_IMPRINT_MESSAGE,
            TOAST_TYPE.SUCCESS,
          );
        }
      });
    }, 500);

    setBlockRecording(false);
    setVisibleModal(false);
  };

  return (
    <View style={styles.container}>
      {showReportModal && (
        <ReportModal
          reportType={REPORT_CONTENT.IMPRINT}
          crossClicked={() => setShowReportModal(false)}
          handleSubmitClick={(ageGroup, option) => {
            setAgeGroup(ageGroup);
            setReportType(option);
            setShowReportModal(false);
            setShowViolation(true);
          }}
        />
      )}
      {showViolation && (
        <ViolationModal
          crossClicked={() => setShowViolation(false)}
          handleSubmitClick={ageGroup => {
            setShowViolation(false);
            const payload: IReportContentPayload = {
              id:
                reportType === REPORT_ENUMS.POST
                  ? imprintData?.id || ''
                  : user.userInfo.userId,
              type: reportType,
              potential_violations: ageGroup,
              imprintId: imprintData?.id,
              userId: imprintData?.userId,
              isGlobal: !isPersonal,
            };

            reportContentRequest(payload, res => {
              if (res) {
                showToastMsg(
                  reportType === REPORT_ENUMS.POST
                    ? TOAST_MESSAGES.REPORT_SENT
                    : TOAST_MESSAGES.REPORT_USER,
                  TOAST_TYPE.SUCCESS,
                  2000,
                  'top',
                );
              }
            });
          }}
          ageGroup={ageGroup}
        />
      )}

      {showBadgeModal && (
        <ReportBadgeModal
          crossClicked={() => setBadgeModal(false)}
          handleSubmitClick={ageGroup => {
            setShowViolation(false);
            const payload: IReportContentPayload = {
              id: imprintData?.id,
              type: 'post',
              potential_violations: ageGroup,
              imprintId: imprintData?.id,
              userId: imprintData?.userId,
              isGlobal: !isPersonal,
            };

            reportContentRequest(payload, res => {
              if (res) {
                showToastMsg(
                  TOAST_MESSAGES.REPORT_VALUE_BADGE,
                  TOAST_TYPE.SUCCESS,
                  2000,
                  'top',
                );
                setBadgeModal(false);
              }
            });
          }}
          ageGroup={ageGroup}
        />
      )}
      {showKin ? (
        <View style={styles.kinContainer}>
          <NextOfKin
            crossClicked={handleCrossClicked}
            ownEmail={user.userInfo.email}
          />
        </View>
      ) : (
        <></>
      )}
      <View
        style={
          hasNotch() ? styles.statusBar : styles.statusBarWithoutNotch
        }></View>
      <CustomNavbar
        hasTitleWIthImage
        titleSize={18}
        hasBack={false}
        hasMultiRight={true}
        rightBtnImage={<INBOX_ICON />}
        rightBtnImage1={<ENTERTAINMENT_ICON />}
        rightBtnImage2={<NEWS_ICON />}
        rightBtnImage3={<SEARCH_ICON />}
        rightBtnPress={() =>
          !isUserSubscribed(user)
            ? (navigation.navigate as (route: string) => void)(
                Routes.SUBSCRIPTION,
              )
            : (navigation.navigate as (route: string) => void)(Routes.CHAT_LIST)
        }
        rightBtnPress3={() =>
          !isUserSubscribed(user)
            ? (navigation.navigate as (route: string) => void)(
                Routes.SUBSCRIPTION,
              )
            : (navigation.navigate as (route: string) => void)(Routes.SEARCH)
        }
        rightBtnPress1={() =>
          !isUserSubscribed(user)
            ? (navigation.navigate as (route: string) => void)(
                Routes.SUBSCRIPTION,
              )
            : (navigation.navigate as (route: string) => void)(
                Routes.ENTERTAINMENT_SCREEN,
              )
        }
        rightBtnPress2={() =>
          !isUserSubscribed(user)
            ? (navigation.navigate as (route: string) => void)(
                Routes.SUBSCRIPTION,
              )
            : (navigation.navigate as (route: string) => void)(
                Routes.NEWS_SCREEN,
              )
        }
      />
      {/* comment for future used/*}
      {/* <LeaveImprint
        onLeaveBoxPress={() => {
          onLeaveImprintPress();
        }}
      /> */}
      {pdfURL ? renderPDF(pdfURL) : null}
      <Tabs
        onPersonalPress={() => {
          setVotingModal(true);
          !isUserSubscribed(user)
            ? (navigation.navigate as (route: string) => void)(
                Routes.SUBSCRIPTION,
              )
            : togglePersonal(true);
        }}
        onGlobalPress={() => togglePersonal(false)}
        isPersonalSelect={isPersonal}
      />
      <ImprintScoresList
        user={user}
        selectedScores={scores}
        isPersonal={isPersonal}
        onScoresPress={handleScoreClick}
      />
      <AnimatedFlashList
        refreshing={loading}
        onRefresh={getTimeLineData}
        //  bounces={false}
        ref={flatListRef}
        data={
          isPersonal
            ? personalTimeLine
              ? personalTimeLine
              : []
            : globalTimeLine
            ? globalTimeLine
            : []
        }
        keyExtractor={(item: {id: string}, index) => item?.id + index}
        contentContainerStyle={
          (!personalMorePage && isPersonal) || (!isPersonal && !globalMorePages)
            ? styles.flastList
            : {backgroundColor: 'transparent'}
        }
        estimatedItemSize={447}
        onScroll={(event: NativeSyntheticEvent<any>) => {
          scrolled = true;

          var currentOffset = event.nativeEvent.contentOffset.y;
          if (currentOffset <= 0) {
            navigation.setParams({showTab: true} as any);
          } else {
            if (currentOffset > offset) {
              // Scrolling down
              navigation.setParams({showTab: false} as any);

              if (!isUserSubscribed(user)) {
                if (!isNavigatingToSubscription) {
                  setIsNavigatingToSubscription(true);

                  setTimeout(() => {
                    navigation.navigate(Routes.SUBSCRIPTION as never);

                    setTimeout(() => {
                      setIsNavigatingToSubscription(false);
                    }, 100);
                  }, 1000);
                }
              }
            } else {
              // Scrolling up
              navigation.setParams({showTab: true} as any);
            }

            SetOffset(currentOffset);
          }
        }}
        onEndReached={() => {
          {
            (!personalMorePage && isPersonal) ||
            (!isPersonal && !globalMorePages)
              ? null
              : startAnimation();
          }
          if (scrolled) {
            setPage(page + 1);
          }
        }}
        renderItem={({item}) => (
          <Animated.View
            style={{
              opacity: animatedValue.interpolate({
                inputRange: [0.4, 1],
                outputRange: [1, 0.4],
              }),
            }}>
            <TimeLineCard
              handleReactionClicked={(userImprintInteraction, reaction) =>
                handleReactionClicked(
                  reaction,
                  userImprintInteraction,
                  item as any,
                )
              }
              handleMediaClicked={() => handleMediaClicked(item as ITimeLine)}
              handleVerifyClick={handleVerifyClick}
              handleMoreOptionClick={onMoreOptionClick}
              onPressArticle={handleOnPressArticle}
              onUserNameClick={onUserNameClick}
              item={item as ITimeLine}
              isGlobal={!isPersonal}
              user={user}
              ref={ref => {
                if (ref && ref.onStopPlay) {
                  activeAudioRef.current = ref;
                }
              }}
            />
          </Animated.View>
        )}
        extraData={handleReactionClicked}
      />

      <RenderModal
        hideModal={hideModal}
        visible={visibleModal}
        onFollowPress={onFollowPress}
        userId={followId}
        onDeletePress={onDeletePress}
        isSelfUser={user.userInfo.userId}
        onBlockPress={onBlockPress}
        imprintData={imprintData}
      />

      <Loader loading={blockRecording} />
      <VotingThanksModal
        visible={votingModal}
        onClose={() => setVotingModal(false)}
      />
    </View>
  );
};

const mapStateToProps = (state: any) => ({
  globalTimeLine: state.timeline.globalTimeLine,
  personalTimeLine: state.timeline.personalTimeLine,
  user: state.user,
});

export default connect(mapStateToProps, {
  resetTimeLine,
  userLogout,
  globalTimeLineRequest,
  personalTimeLineRequest,
  postReactionRequest,
  imprintVerificationRequest,
  imprintShoutoutRequest,
  imprintBookmarkRequest,
  requestFollowRequest,
  userInfoRequest,
  deleteImprintRequest,
  blockImprintRequest,
  deleteImprintSuccess,
  reportContentRequest,
  postReactionSuccess,
  getPubSubTokenRequest,
  updateTimelineRequest,
})(Home);
