// @flow
import {StyleSheet} from 'react-native';
import {Colors, Metrics} from '../../theme';

export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.gray3,
    paddingBottom: Metrics.ratio(10),
    paddingLeft: Metrics.ratio(5),

    // needed below code in future may be improvement
    //  height: Metrics.screenHeight * 0.15,
    //  marginVertical: Metrics.doubleBaseMargin,
    //  paddingTop: Metrics.doubleBaseMargin,
  },
  gridItem: {
    flexWrap: 'wrap',
    alignSelf: 'center',
    overflow: 'hidden',
    width: Metrics.ratio(120),
    height: Metrics.ratio(120),
    marginHorizontal: Metrics.smallMargin,
    marginVertical: Metrics.smallMargin,
    borderRadius: Metrics.ratio(8),
  },
  images: {
    height: Metrics.ratio(120),
    width: Metrics.ratio(120),
  },
  thumbnailIcon: {
    alignSelf: 'center',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'absolute',
    bottom: (Metrics.ratio(60) ?? 0) - (Metrics.smallMargin ?? 0),
  },
});
