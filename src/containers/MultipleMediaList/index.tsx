import React, {useState, useEffect} from 'react';
import {View, FlatList} from 'react-native';
import {connect} from 'react-redux';
import {ButtonView} from '../../components';
import styles from './styles';
import EmptyStateText from '../../components/EmptyStateComponent';
import {
  userMedia_CheckInRequest,
  userTagMediaRequest,
} from '../../actions/UserActions.ts';
import {IMedia_CheckIn, ITagMedia, ImprintMedia} from '../../types';
import MediaTabs from '../../components/MediaTabs/index.tsx';
import {IMPRINT_MEDIA, PHOTOS} from '../../constants/StringConstants.ts';
import {useNavigation} from '@react-navigation/native';
import Routes from '../../constants/RouteConstants.ts';
import {PLAY_ICON} from '../../constants/AssetSVGConstants.ts';
import AppStyles from '../../theme/AppStyles.ts';
import Image from 'react-native-fast-image';

interface MediaProps {
  userMedia_CheckInRequest: (
    payload: IMedia_CheckIn,
    callback: (res: any) => void,
  ) => void;
  userTagMediaRequest: (
    payload: ITagMedia,
    callback: (res: any) => void,
  ) => void;

  mediaType?: string;
}

const MultipleMediaList: React.FC<MediaProps> = ({
  userMedia_CheckInRequest,
  userTagMediaRequest,
  mediaType = IMPRINT_MEDIA.IMAGE,
}) => {
  const [PhotosList, setPhotosList] = useState<ImprintMedia[]>([]);
  const [loading, setLoading] = useState(false);
  const [UserPhotos, setUserPhotos] = useState(true);
  const [tagPhotosList, setTagPhotosList] = useState<ImprintMedia[]>([]);
  const [tagPage, setTagPage] = useState(1);

  const navigation = useNavigation();
  useEffect(() => {
    getAllPhotos();
  }, []);

  const getAllPhotos = () => {
    const payload: IMedia_CheckIn = {
      checkin: false,
    };
    setLoading(true);
    userMedia_CheckInRequest(payload, res => {
      setLoading(false);

      const newMedia = res
        ?.map((obj: {imprintMedias: any}) => obj.imprintMedias)
        .flat()
        .filter(
          (media: ImprintMedia) => media.isUploaded && media.type === mediaType,
        );
      setPhotosList(newMedia);
    });
  };

  useEffect(() => {
    if (!UserPhotos) {
      onTagPress();
    }
  }, [tagPage]);

  const renderGRidItem = ({item, index}: any) => {
    return (
      <ButtonView
        onPress={() =>
          (navigation.navigate as (route: string, {}) => void)(
            Routes.MULTIPLE_MEDIA,
            {
              selectedItem: item,
              mediaList: UserPhotos ? PhotosList : tagPhotosList,
            },
          )
        }
        style={styles.gridItem}>
        <Image
          source={{
            uri: item.type === IMPRINT_MEDIA.IMAGE ? item.url : item.thumbUrl,
          }}
          style={styles.images}
          resizeMode={'cover'}
          onLoad={() => {}}
        />

        <View style={styles.thumbnailIcon}>
          {mediaType == IMPRINT_MEDIA.VIDEO && (
            <PLAY_ICON width={30} height={30} />
          )}
        </View>
      </ButtonView>
    );
  };

  const _renderEmptyComponent = (isLoading: boolean) => {
    if (isLoading) {
      return null;
    }
    return <EmptyStateText />;
  };

  const onTagPress = () => {
    setLoading(true);
    setUserPhotos(false);

    const payload: ITagMedia = {
      type: mediaType.toLowerCase(),
      page: tagPage.toString(),
    };
    userTagMediaRequest(payload, res => {
      if (res) {
        setLoading(false);

        const newMedia = res
          ?.map((obj: {imprintMedias: any}) => obj.imprintMedias)
          .flat()
          .filter(
            (media: ImprintMedia) =>
              media.isUploaded && media.type === mediaType,
          );
        setTagPhotosList(newMedia);
      }
    });
  };

  return (
    <View style={styles.container}>
      <MediaTabs
        onTab1Press={() => {
          setUserPhotos(true);
        }}
        onTab2Press={() => onTagPress()}
        tab1Text={
          mediaType == IMPRINT_MEDIA.IMAGE
            ? PHOTOS.YOUR_PHOTOS
            : PHOTOS.YOUR_VIDEOS
        }
        tab2Text={
          mediaType == IMPRINT_MEDIA.IMAGE
            ? PHOTOS.TAG_PHOTOS
            : PHOTOS.TAG_VIDEOS
        }
        isActive={UserPhotos}
      />
      <FlatList
        scrollEnabled={false}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={[AppStyles.alignStart]}
        data={UserPhotos ? PhotosList : tagPhotosList}
        renderItem={renderGRidItem}
        refreshing={loading}
        numColumns={3}
        ListEmptyComponent={() => _renderEmptyComponent(loading)}
        onRefresh={getAllPhotos}
      />
    </View>
  );
};

const mapStateToProps = ({}) => ({});

const actions = {userMedia_CheckInRequest, userTagMediaRequest};

export default connect(mapStateToProps, actions)(MultipleMediaList);
