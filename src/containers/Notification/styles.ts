// @flow
import {StyleSheet} from 'react-native';
import {Colors, Metrics} from '../../theme';
import {getStatusBarHeight} from 'react-native-iphone-x-helper';

export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.home,
  },
  statusBar: {
    backgroundColor: Colors.white,
    height: getStatusBarHeight() + 30,
  },
  statusBarWithoutNotch: {
    backgroundColor: Colors.white,
    height: Metrics.ratio(30),
  },
  flashList: {
    flex: 1,
    marginTop: Metrics.ratio(20),
  },
});
