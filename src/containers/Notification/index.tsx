// @flow
import React, {useEffect} from 'react';
import {BackHandler, Pressable, View} from 'react-native';
import {CustomNavbar, Text} from '../../components';
import styles from './styles';
import {AppStyles, Colors, Fonts} from '../../theme';
import {hasNotch} from 'react-native-device-info';
import NotificationCard from '../../components/NotificationCard';
import {connect} from 'react-redux';
import {
  getNotificationRequest,
  readNotificationRequest,
  getImprintByIdRequest,
  updateVerificationRequest,
  getTransparencyDataRequest,
} from '../../actions/NotificationActions';
import {userRespondToTagMedia} from '../../actions/UserActions';
import {FlashList} from '@shopify/flash-list';
import {
  IRespondFollower,
  IRespondTag,
  ITransparencyPayload,
  ImprintNotification,
  NotificationTypeEnum,
  VISITED_SCREEN,
} from '../../types';
import {useIsFocused, useNavigation} from '@react-navigation/native';
import Routes from '../../constants/RouteConstants';
import {respondToFollowers} from '../../actions/FollowersActions';
import {
  NOTIFICATION_TYPE,
  TOAST_MESSAGES,
  TOAST_TYPE,
} from '../../constants/StringConstants';
import {BADGE_TYPES, ENTERTAINMENTS_OPTIONS} from '../../constants';
import {TOAST_VISIBILITY_TIMEOUT} from '../../constants';
import {
  remindLaterRequest,
  remindNextOfKinRequest,
} from '../../actions/TimelineActions';
import {showToastMsg} from '../../components/Alert';

interface NotificationProps {
  notifications: ImprintNotification[];
  getTransparencyDataRequest: (
    payload: ITransparencyPayload,
    callback: (res: any) => void,
  ) => void;
  getNotificationRequest: (payload: any, callback: (res: any) => void) => void;
  remindLaterRequest: (callback: (res: any) => void) => void;
  readNotificationRequest: (payload: any, callback: (res: any) => void) => void;
  getImprintByIdRequest: (payload: any, callback: (res: any) => void) => void;
  respondToFollowers: (payload: any, callback: (res: any) => void) => void;
  updateVerificationRequest: (
    payload: any,
    callback: (res: any) => void,
  ) => void;
  userRespondToTagMedia: (
    payload: IRespondTag,
    callback: (res: any) => void,
  ) => void;
  remindNextOfKinRequest: (payload: any, callback: (res: any) => void) => void;
}

const Notification: React.FC<NotificationProps> = ({
  getNotificationRequest,
  readNotificationRequest,
  getImprintByIdRequest,
  respondToFollowers,
  updateVerificationRequest,
  userRespondToTagMedia,
  getTransparencyDataRequest,
  notifications,
  remindLaterRequest,
  remindNextOfKinRequest,
}) => {
  const [page, setPage] = React.useState(1);
  const [showAllNotifications, setShowAllNotifications] = React.useState(false);

  const [morePage, setMorePage] = React.useState(true);
  const navigation = useNavigation();
  const [refresh, setRefresh] = React.useState(false);

  const isFocued = useIsFocused();

  useEffect(() => {
    const backAction = () => {
      return true; // Disable the default back button behavior
    };

    const backHandler = BackHandler.addEventListener(
      'hardwareBackPress',
      backAction,
    );

    return () => backHandler.remove(); // Clean up the event listener
  }, []);

  useEffect(() => {
    if (!morePage) {
      return;
    }
    const payload = {
      page: page,
    };
    getNotificationRequest(payload, (res: ImprintNotification[]) => {
      if (res.length < 10) {
        setRefresh(false);
        setMorePage(false);
      } else {
        setRefresh(false);
      }
    });
  }, [page, refresh]);

  useEffect(() => {
    if (isFocued) {
      const payload = {
        page: page,
      };
      getNotificationRequest(payload, (res: ImprintNotification[]) => {
        if (res.length < 10) {
          setMorePage(false);
          setRefresh(false);
        }
      });
    } else {
      setPage(1);
      setMorePage(true);
      setRefresh(false);
    }
  }, [isFocued, refresh]);

  const onItemClicked = (item: ImprintNotification) => {
    const payload = {
      id: item.imprintId !== null ? item.imprintId : item.metaId,
    };

    const typeTemplate = item.subNotifications[0].typeTemplate.type;

    if (
      [
        NotificationTypeEnum.HAPPY,
        NotificationTypeEnum.ANGRY,
        NotificationTypeEnum.SAD,
        NotificationTypeEnum.LOVE,
        NotificationTypeEnum.LIKE,
        NotificationTypeEnum.DISLIKE,
        NotificationTypeEnum.LAUGHTER,
        NotificationTypeEnum.RESPECT,
        NotificationTypeEnum.SMILE,
        NotificationTypeEnum.TEAR,
        NotificationTypeEnum.ANGRY,
        NotificationTypeEnum.SHOCK,
        NotificationTypeEnum.CARE,
        NotificationTypeEnum.SPEAK_UP,
        NotificationTypeEnum.ARTICLE_VERIFICATION_REQUESTED,
        NotificationTypeEnum.ARTICLE_VERIFICATION_SUCCESS,
        NotificationTypeEnum.ARTICLE_VERIFICATION_FAILURE,
        NotificationTypeEnum.ARTICLE_VERIFICATION_REJECTED,
        NotificationTypeEnum.ARTICLE_VERIFICATION_ACCEPTED,
        NotificationTypeEnum.IMPRINT_REJECTED_FOR_ALL_USERS,
        NotificationTypeEnum.IMPRINT_REJECTED_FOR_ALL_UNDER_18POLICY,
        NotificationTypeEnum.IMPRINT_LIVE,
        NotificationTypeEnum.UPDATED_VALUE_BADGE_ASSIGNED,
        NotificationTypeEnum.VALUE_BADGE_ASSIGNED_LAUGHTER,
        NotificationTypeEnum.CONTENT_MODERATED,
        NotificationTypeEnum.USER_REPORTED_IMPRINT_REJECTED_FOR_UNDER_18_USERS,
        NotificationTypeEnum.IMPRINT_REJECTED_FOR_UNDER_18_USERS,
        NotificationTypeEnum.USER_REPORTED_IMPRINT_LIVE,
        NotificationTypeEnum.USER_REPORTED_IMPRINT_DELETED,
        NotificationTypeEnum.INVESTIGATION_UPHELD_DECISION,
        NotificationTypeEnum.INVESTIGATION_CONTENT_VIOLATES_GUIDELINES,
        NotificationTypeEnum.INVESTIGATION_CONTENT_VIOLATES_GUIDELINES_UNDER_18,
        NotificationTypeEnum.USER_ACCOUNT_SUSPENDED_YEARLY,
        NotificationTypeEnum.USER_ACCOUNT_SUSPENDED,
        NotificationTypeEnum.CONTENT_REMOVAL,
        NotificationTypeEnum.SUBSCRIPTION_CANCELLED_USER_BANNED,
        ...BADGE_TYPES,
      ].includes(typeTemplate as NotificationTypeEnum)
    ) {
      getImprintByIdRequest(payload, (res: any) => {
        if (res) {
          setPage(1);
          setMorePage(true);
          (navigation.navigate as (route: string, params: any) => void)(
            Routes.TIME_LINE_MEDIA_VIEW,
            {item: res, type: VISITED_SCREEN.NOTIFICATION},
          );
        }
      });
    } else if (
      [
        NotificationTypeEnum.FOLLOW_REQUEST_ACCEPT,
        NotificationTypeEnum.FOLLOW_REQUEST_REJECT,
        NotificationTypeEnum.FOLLOW_REQUEST,
        NotificationTypeEnum.INVESTIGATION_REPORTED_USER_IS_A_RISK,
      ].includes(typeTemplate as NotificationTypeEnum)
    ) {
      setPage(1);
      setMorePage(true);
      (navigation.navigate as (route: string, params: any) => void)(
        Routes.USER_PROFILE,
        {
          userName:
            typeTemplate ===
            NotificationTypeEnum.INVESTIGATION_REPORTED_USER_IS_A_RISK
              ? item.metaData.userName
              : item.subNotifications[0].user.userName,
        },
      );
    } else if (
      [
        NotificationTypeEnum.IMPRINT_MENTION,
        NotificationTypeEnum.IMPRINT_MEDIA_MENTION,
      ].includes(typeTemplate as NotificationTypeEnum)
    ) {
      getImprintByIdRequest(payload, (res: any) => {
        if (res) {
          setPage(1);
          setMorePage(true);
          (navigation.navigate as (route: string, params: any) => void)(
            Routes.TIME_LINE_MEDIA_VIEW,
            {
              item: res,
              type: NOTIFICATION_TYPE.MENTION_NOTIFICATION,
              imprintId: item.imprintId,
              notificationId: item.id,
            },
          );
        }
      });
    } else if (
      [
        NotificationTypeEnum.CHECKOUT_GOOD_DEEDS,
        NotificationTypeEnum.CHECKOUT_QUOTE_JOKE,
      ].includes(typeTemplate as NotificationTypeEnum)
    ) {
      const selectedOption =
        typeTemplate === NotificationTypeEnum.CHECKOUT_GOOD_DEEDS
          ? ENTERTAINMENTS_OPTIONS[0]
          : ENTERTAINMENTS_OPTIONS[1];

      (navigation.navigate as (route: string, params: any) => void)(
        Routes.ENTERTAINMENT_SCREEN,
        {tab: selectedOption},
      );
    } else if (
      [NotificationTypeEnum.DAILY_NEWS].includes(
        typeTemplate as NotificationTypeEnum,
      )
    ) {
      (navigation.navigate as (route: string) => void)(Routes.NEWS_SCREEN);
    } else if (
      [
        NotificationTypeEnum.MESSAGING_RESTRICTED_UNDER18,
        NotificationTypeEnum.MESSAGING_RESTRICTED_ALL,
      ].includes(typeTemplate as NotificationTypeEnum)
    ) {
      (navigation.navigate as (route: string) => void)(Routes.CHAT_LIST);
    }
    if (item.isRead) {
      return;
    }

    const notificationPayload = {id: [item.id]};

    readNotificationRequest(notificationPayload, () => {});
  };

  const onConfirmClick = (item: ImprintNotification) => {
    if (
      item.subNotifications[0].typeTemplate.type ===
      NotificationTypeEnum.FOLLOW_REQUEST
    ) {
      const payload: IRespondFollower = {
        isAccepted: true,
        requestID: item.metaId ?? '',
      };

      respondToFollowers(payload, (res: any) => {
        if (res) {
          setPage(1);
          setMorePage(true);
          (navigation.navigate as (route: string, params: any) => void)(
            Routes.USER_PROFILE,
            {userName: item.subNotifications[0]?.user?.userName},
          );
        }
      });
    } else if (
      item.subNotifications[0].typeTemplate.type ===
      NotificationTypeEnum.ARTICLE_VERIFICATION_REQUESTED
    ) {
      const payload = {
        id: item.imprintId,
        verify: true,
      };

      updateVerificationRequest(payload, (res: any) => {
        if (res) {
          const imprintPayload = {id: item.imprintId};
          getImprintByIdRequest(imprintPayload, (res: any) => {
            if (res) {
              setPage(1);
              setMorePage(true);
              (navigation.navigate as (route: string, params: any) => void)(
                Routes.TIME_LINE_MEDIA_VIEW,
                {item: res, type: VISITED_SCREEN.NOTIFICATION},
              );
            }
          });
        }
      });
    } else if (
      item.subNotifications[0].typeTemplate.type ===
        NotificationTypeEnum.IMPRINT_MEDIA_MENTION ||
      item.subNotifications[0].typeTemplate.type ===
        NotificationTypeEnum.IMPRINT_MENTION
    ) {
      const payload: IRespondTag = {
        imprintId: item.imprintId,
        approve: true,
        notificationId: item.id,
      };
      userRespondToTagMedia(payload, res => {
        if (res) {
          const imprintPayload = {id: item.imprintId};

          getImprintByIdRequest(imprintPayload, (res: any) => {
            if (res) {
              setPage(1);
              setMorePage(true);
              (navigation.navigate as (route: string, params: any) => void)(
                Routes.TIME_LINE_MEDIA_VIEW,
                {item: res, type: VISITED_SCREEN.NOTIFICATION},
              );
            }
          });
        }
      });
    } else if (
      item.subNotifications[0].typeTemplate.type ===
      NotificationTypeEnum.NOK_REJECTED
    ) {
      showToastMsg('Coming Soon');
    } else if (
      item.subNotifications[0].typeTemplate.type ===
      NotificationTypeEnum.NOK_REMINDER
    ) {
      remindNextOfKinRequest({}, (res: any) => {
        if (res) {
          showToastMsg(
            TOAST_MESSAGES.REMIND_MESSAGE,
            TOAST_TYPE.SUCCESS,
            TOAST_VISIBILITY_TIMEOUT,
            'Top',
          );
        }
      });
    }

    if (item.isRead) {
      return;
    }

    const notificationPayload = {id: [item.id]};

    readNotificationRequest(notificationPayload, () => {});
  };

  const onDeclineClick = (item: any) => {
    if (
      item.subNotifications[0].typeTemplate.type ===
      NotificationTypeEnum.FOLLOW_REQUEST
    ) {
      const payload: IRespondFollower = {
        isAccepted: false,
        requestID: item.metaId ?? '',
      };

      respondToFollowers(payload, (res: any) => {
        if (res) {
          setPage(1);
          setMorePage(true);
          (navigation.navigate as (route: string, params: any) => void)(
            Routes.USER_PROFILE,
            {userName: item.subNotifications[0].user.userName},
          );
        }
      });
    } else if (
      item.subNotifications[0].typeTemplate.type ===
      NotificationTypeEnum.ARTICLE_VERIFICATION_REQUESTED
    ) {
      const payload = {
        id: item.imprintId,
        verify: false,
      };

      updateVerificationRequest(payload, (res: any) => {
        if (res) {
          const imprintPayload = {id: item.imprintId};
          getImprintByIdRequest(imprintPayload, (res: any) => {
            if (res) {
              setPage(1);
              setMorePage(true);
              (navigation.navigate as (route: string, params: any) => void)(
                Routes.TIME_LINE_MEDIA_VIEW,
                {item: res, type: VISITED_SCREEN.NOTIFICATION},
              );
            }
          });
        }
      });
    } else if (
      item.subNotifications[0].typeTemplate.type ===
        NotificationTypeEnum.IMPRINT_MEDIA_MENTION ||
      item.subNotifications[0].typeTemplate.type ===
        NotificationTypeEnum.IMPRINT_MENTION
    ) {
      const payload: IRespondTag = {
        imprintId: item.imprintId,
        approve: false,
        notificationId: item.id,
      };
      userRespondToTagMedia(payload, res => {
        if (res) {
          getNotificationRequest({page: 1}, (res: ImprintNotification[]) => {});
        }
      });
    } else if (
      item.subNotifications[0].typeTemplate.type ===
        NotificationTypeEnum.NOK_REJECTED ||
      item.subNotifications[0].typeTemplate.type ===
        NotificationTypeEnum.NOK_REMINDER
    ) {
      remindLaterRequest((res: any) => {
        if (res) {
          showToastMsg(
            TOAST_MESSAGES.REMIND_LATER_MESSAGE,
            TOAST_TYPE.SUCCESS,
            TOAST_VISIBILITY_TIMEOUT,
            'Top',
          );
        }
      });
    }

    if (item.isRead) {
      return;
    }

    const notificationPayload = {id: [item.id]};

    readNotificationRequest(notificationPayload, () => {});
  };
  const handleShowEarlier = () => {
    setShowAllNotifications(true);
  };

  const onClickMore = (item: ImprintNotification) => {
    const payload: ITransparencyPayload = {
      content_id: item.metaId ?? '',
      complex_type: item?.metaData?.type ?? 'post',
    };
    getTransparencyDataRequest(payload, (res: any) => {
      (navigation.navigate as (route: string, params: any) => void)(
        Routes.REPORT_SCREEN,
        {url: res.redirectUrl, title: ''},
      );
    });
  };

  return (
    <View style={styles.container}>
      <View
        style={
          hasNotch() ? styles.statusBar : styles.statusBarWithoutNotch
        }></View>
      <CustomNavbar
        title="Notification"
        titleSize={Fonts.size.large}
        alignTitleLeft
        hasBack={false}
      />
      <View style={styles.flashList}>
        <FlashList
          onRefresh={() => setRefresh(true)}
          refreshing={refresh}
          onEndReached={
            showAllNotifications ? () => setPage(page + 1) : undefined
          }
          data={notifications}
          extraData={showAllNotifications}
          contentContainerStyle={{paddingBottom: 150}}
          style={!morePage ? {paddingBottom: 200} : {}}
          estimatedItemSize={110}
          renderItem={({item, index}) => {
            if (!showAllNotifications && index >= 5) return null;

            return (
              <>
                <NotificationCard
                  notification={item}
                  onItemClicked={onItemClicked}
                  onAccept={onConfirmClick}
                  onDecline={onDeclineClick}
                  onClickMore={onClickMore}
                />
                {!showAllNotifications && index === 4 && (
                  <Pressable
                    onPress={handleShowEarlier}
                    style={AppStyles.alignSelfCenter}>
                    <Text
                      type="semi_bold"
                      color={Colors.black}
                      style={[AppStyles.mBottom20, AppStyles.mLeft20]}>
                      {NOTIFICATION_TYPE.VIEW_ALL}
                    </Text>
                  </Pressable>
                )}
              </>
            );
          }}
        />
      </View>
    </View>
  );
};
const mapStateToProps = (state: any) => ({
  notifications: state.notification.notifications,
});

export default connect(mapStateToProps, {
  getNotificationRequest,
  readNotificationRequest,
  getImprintByIdRequest,
  respondToFollowers,
  updateVerificationRequest,
  userRespondToTagMedia,
  getTransparencyDataRequest,
  remindLaterRequest,
  remindNextOfKinRequest,
})(Notification);
