// @flow

import React, {useEffect, useRef, useState} from 'react';
import {
  View,
  StyleSheet,
  PermissionsAndroid,
  Platform,
  Alert,
  Linking,
} from 'react-native';
import {CustomNavbar, AppButton, Loader} from '../../components';
import {LOCATION_ICON} from '../../constants/AssetSVGConstants';
import {Colors} from '../../theme';
import _ from 'lodash';
import {IPostImprintPayload, ImprintCheckIn} from '../../types';
import {useNavigation, useNavigationState} from '@react-navigation/native';
import MapView, {<PERSON><PERSON>, PROVIDER_GOOGLE} from 'react-native-maps';
import {GooglePlacesAutocomplete} from 'react-native-google-places-autocomplete';
import {captureRef} from 'react-native-view-shot';
import Routes from '../../constants/RouteConstants';
import {GOOGLE_MAPS_ID} from '../../config/AppConfig';
import Geolocation from '@react-native-community/geolocation';
import geolocationConfig from '../../config/geoLocationConfig';
import {LATITUDE_DELTA, LONGITUDE_DELTA} from '../../constants';
import {CREATE_IMPRINT} from '../../constants/StringConstants';
import util from '../../util';
import styles from './styles';
import {connect} from 'react-redux';
import {userPostImprintRequest} from '../../actions/PostActions';
declare const navigator: any;
navigator.geolocation = require('@react-native-community/geolocation');

interface checkInProps {
  userPostImprintRequest: (
    payload: IPostImprintPayload,
    callback: (res: any) => void,
  ) => void;
  route: {
    params: {
      checkInData: ImprintCheckIn;
      userName?: string;
    };
  };
}

const CheckIn: React.FC<checkInProps> = ({userPostImprintRequest, route}) => {
  const [regionCoords, setRegion] = useState({lat: 37.7749, lng: -122.4194});
  const [marker, setMarker] = useState({lat: 0, lng: 0});
  const [place, setPlace] = useState('');
  const [loading, setLoading] = useState<boolean>(false);

  const [isCheckIn, setChecKIn] = useState<boolean>(false);

  const mapRef = useRef<MapView>(null);

  Geolocation.setRNConfiguration(geolocationConfig);

  const navigation = useNavigation();
  const screenStack = useNavigationState(state => state.routes[0].name);

  const onPlaceSelected = (data: any, details: any) => {
    const location = data.address_components
      ? data.address_components[0].short_name
      : data.structured_formatting?.main_text ?? data.name;

    setRegion(details.geometry.location);
    setMarker(details.geometry.location);
    setPlace(location);

    setChecKIn(true);
  };

  const requestLocationPermission = async () => {
    if (Platform.OS === 'ios') {
      // iOS automatically prompts for location permission when using Geolocation API
      return true;
    }

    try {
      const granted = await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.ACCESS_COARSE_LOCATION,
        {
          title: 'Location Permission',
          message:
            'This app needs access to your location to provide location-based services.',
          buttonNeutral: 'Ask Me Later',
          buttonNegative: 'Cancel',
          buttonPositive: 'OK',
        },
      );

      if (granted === PermissionsAndroid.RESULTS.GRANTED) {
        return true;
      } else {
        Alert.alert(
          'Location Permission Denied',
          'You need to enable location permissions in settings to use this feature.',
          [{text: 'OK'}],
        );
        return false;
      }
    } catch (err) {
      console.warn(err);
      return false;
    }
  };

  const onCheckIn = () => {
    captureRef(mapRef, {
      format: 'jpg',
      quality: 0.8,
    }).then(
      uri => {
        setTimeout(() => {
          const checkInData: ImprintCheckIn = {
            isCheckIn: true,
            lat: marker.lat.toString(),
            long: marker.lng.toString(),
            place: place,
            date: new Date(),
            checkInImage: uri,
          };
          (navigation.navigate as (route: string, params: any) => void)(
            Routes.CREATE_IMPRINT,
            {
              checkInData: checkInData,
            },
          );
        }, 500);
      },
      error => console.error('Oops, snapshot failed', error),
    );
  };

  const CheckInDirectly = () => {
    const checkInPayload: IPostImprintPayload = {
      isCheckIn: true,
      date: new Date(),
      place: place,
      lat: marker.lat.toString(),
      long: marker.lng.toString(),
      mediaQuantity: 0,
      isGlobal: false,
    };
    setLoading(true);
    userPostImprintRequest(checkInPayload, (res: any) => {
      if (res) {
        setLoading(false);
        setTimeout(() => {
          (navigation.navigate as (route: string) => void)(Routes.USER_PROFILE);
        }, 500);
      }
      setLoading(false);
    });
  };

  useEffect(() => {
    const getCoordinates = async () => {
      try {
        const coordinates = (await util.getCoordinates()) as {
          latitude: number;
          longitude: number;
        };
        setRegion({
          lat: coordinates.latitude,
          lng: coordinates.longitude,
        });
      } catch (error) {
        console.error('Error getting coordinates:', error);
        if ((error as {code: number}).code === 1) {
          Alert.alert(
            CREATE_IMPRINT.LOCATION_PERMISSION_DENIED,
            CREATE_IMPRINT.LOCATION_PERMISSION_DENIED_MESSAGE,
            [
              {text: 'OK', onPress: () => Linking.openSettings()},
              {text: 'cancel', onPress: () => navigation.goBack()},
            ],
          );
        }
        if ((error as {code: number}).code === 2) {
          // Location provider unavailable
          Alert.alert(
            'Location is Off',
            'Please turn on device location (GPS) to use this feature.',
            [],
          );
        }
      }
    };

    getCoordinates();
  }, []);

  useEffect(() => {
    requestLocationPermission();
  }, []);

  return (
    <View style={StyleSheet.absoluteFill}>
      <MapView
        ref={mapRef}
        provider={PROVIDER_GOOGLE}
        style={StyleSheet.absoluteFillObject} // remove if not using Google Maps
        region={{
          latitude: regionCoords.lat,
          longitude: regionCoords.lng,
          latitudeDelta: LATITUDE_DELTA,
          longitudeDelta: LONGITUDE_DELTA,
        }}
        rotateEnabled={false}
        showsUserLocation={true}>
        <Marker
          coordinate={{latitude: marker.lat, longitude: marker.lng}}
          description={place}
          title={place}
        />
      </MapView>

      <View style={styles.searchContainer}>
        <GooglePlacesAutocomplete
          enableHighAccuracyLocation={true}
          currentLocation={true}
          styles={{
            textInputContainer: styles.googlePlacesStyle,
            listView: styles.googlePlacesList,
          }}
          placeholder={CREATE_IMPRINT.PLACES_PLACEHOLDER}
          query={{
            key: GOOGLE_MAPS_ID,
            language: 'en', // language of the results
          }}
          GooglePlacesDetailsQuery={{
            fields: 'geometry',
          }}
          fetchDetails={true}
          onPress={onPlaceSelected}
          onFail={error => console.error('ERRORs', error)}
          requestUrl={{
            url: 'https://cors-anywhere.herokuapp.com/https://maps.googleapis.com/maps/api',
            useOnPlatform: 'web',
          }}
          nearbyPlacesAPI="GooglePlacesSearch"
          renderLeftButton={() => <LOCATION_ICON />}
          timeout={5000}
          currentLocationLabel={CREATE_IMPRINT.USE_CURRENT_LOCATION}
          textInputProps={{
            placeholderTextColor: Colors.tabsTextColor.activeTabColor,
            color: Colors.black,
          }}
        />

        <AppButton
          disabled={!isCheckIn}
          buttonStye={[
            styles.button,
            isCheckIn ? styles.activeButton : styles.inActiveButton,
          ]}
          text={CREATE_IMPRINT.ON_CHECK_IN}
          textColor={
            isCheckIn ? Colors.white : Colors.text.publishTextInActiveColor
          }
          onPress={() =>
            screenStack === Routes.CREATE_IMPRINT || Routes.PROFILE
              ? onCheckIn()
              : CheckInDirectly()
          }
        />
      </View>
      <CustomNavbar
        style={styles.navBarStyle}
        title={route.params?.userName}
        titleColor={Colors.text.black}
        hasBack={true}
        leftBtnPress={() => navigation.goBack()}
      />
      <Loader loading={loading} />
    </View>
  );
};

const mapStateToProps = () => ({});

const actions = {
  userPostImprintRequest,
};

export default connect(mapStateToProps, actions)(CheckIn);
