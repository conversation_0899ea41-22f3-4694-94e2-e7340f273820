// @flow
import {StyleSheet} from 'react-native';
import {Colors, Metrics} from '../../theme';

export default StyleSheet.create({
  container: {
    flex: 1,
  },
  searchContainer: {
    position: 'absolute',
    width: Metrics.screenWidth,
    top: Metrics.ratio(100),
    bottom: Metrics.ratio(30),
  },
  googlePlacesStyle: {
    paddingHorizontal: Metrics.ratio(20),
    marginHorizontal: Metrics.doubleBaseMargin,
    backgroundColor: Colors.white,
    borderRadius: Metrics.ratio(20),
    alignItems: 'center',
  },
  navBarStyle: {
    height: Metrics.ratio(100),
    backgroundColor: 'transparent',
  },
  button: {
    marginBottom: Metrics.ratio(10),
    borderWidth: Metrics.ratio(1),
  },
  activeButton: {backgroundColor: Colors.background.publishActiveColor},
  inActiveButton: {backgroundColor: Colors.background.publishInactiveColor},
  googlePlacesList: {
    marginBottom: Metrics.screenHeight * 0.3,
  },
});
