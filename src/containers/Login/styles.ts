// @flow
import {StyleSheet} from 'react-native';
import {Colors, Metrics, AppStyles} from '../../theme';

export default StyleSheet.create({
  container: {
    position: 'absolute',
    alignSelf: 'center',
  },
  headerText: {
    color: '#000',
    marginTop: Metrics.ratio(40),
    marginBottom: Metrics.ratio(30),
    marginLeft: Metrics.ratio(24),
  },
  emailInputContainer: {
    marginHorizontal: Metrics.ratio(30),
    marginVertical: 20,
  },
  passwordInputContainer: {
    marginHorizontal: Metrics.ratio(30),
    marginVertical: 10,
  },
  forgotContainer: {
    alignSelf: 'flex-end',
    marginVertical: Metrics.ratio(20),
    marginRight: Metrics.ratio(40),
  },
  socialLoginContainer: {
    backgroundColor: 'white',
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    width: 48,
    height: 48,
    shadowColor: '#000',
    shadowOpacity: 0.1,
    shadowRadius: 6,
    elevation: 3,
  },
  footerTextContainer: {
    marginTop: Metrics.ratio(40),
    justifyContent: 'flex-end',
    alignItems: 'center',
  },
  button: {
    marginHorizontal: Metrics.ratio(30),

    backgroundColor: 'white',
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    width: 48,
    height: 48,
    shadowColor: '#000',
    shadowOpacity: 0.1,
    shadowRadius: 6,
    elevation: 3,
  },
});
