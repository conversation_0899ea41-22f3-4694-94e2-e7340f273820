import _ from 'lodash';
import {connect, useSelector} from 'react-redux';
import React, {useRef, useState} from 'react';
import {SafeAreaView} from 'react-native-safe-area-context';
import {
  ILoginPayload,
  ISocialPayload,
  OTP_ENUMS,
  QUIZ_STATUS,
  RootState,
  SubscriptionStatus,
} from '../../types';
import {
  AppButton,
  ButtonView,
  Text,
  TextInput,
  DividerWithText,
  Loader,
  AgeVerificationModal,
} from '../../components/';
import {
  APPLE_LOGIN,
  EMAIL_ICON,
  EYE_CLOSE,
  EYE_OPEN,
  GOOGLE_LOGIN,
  LOCK_ICON,
  SPLASH_BG,
} from '../../constants/AssetSVGConstants';
import {
  View,
  TextInput as RNTextInput,
  TouchableOpacity,
  Platform,
} from 'react-native';
import styles from './styles';
import {AppStyles, Colors} from '../../theme';
import {
  userSignInRequest,
  userGoogleLoginRequest,
  userInfoRequest,
  userAppleLoginRequest,
  startYotiSessionRequest,
  userLogout,
  updateDobRequest,
} from '../../actions/UserActions';
import YotiService from '../../services/YotiService';
import {useNavigation} from '@react-navigation/native';
import Routes from '../../constants/RouteConstants';
import {SIGN_IN, TOAST_MESSAGES} from '../../constants/StringConstants';
import Util from '../../util';
import {showToastMsg} from '../../components/Alert';
import {
  GoogleSignin,
  statusCodes,
} from '@react-native-google-signin/google-signin';

import {appleAuth} from '@invertase/react-native-apple-authentication';

import util from '../../util';

interface ActionsProps {
  userLogout: () => void;

  userSignInRequest: (
    payload: ILoginPayload,
    callback: (res: any) => void,
  ) => void;
  userGoogleLoginRequest: (
    payload: ISocialPayload,
    response: (res: any) => void,
  ) => void;
  userAppleLoginRequest: (
    payload: ISocialPayload,
    response: (res: any) => void,
  ) => void;
  userInfoRequest: (response: (res: any) => void) => void;
  user: RootState;
  updateDobRequest: (payload: any, callback: (res: any) => void) => void;
  startYotiSessionRequest: (callback: (res: any) => void) => void;
}

const Login: React.FC<ActionsProps> = ({
  userSignInRequest,
  userGoogleLoginRequest,
  userInfoRequest,
  userAppleLoginRequest,
  updateDobRequest,
  startYotiSessionRequest,
}) => {
  const [secureText, setSecureText] = useState<boolean>(true);
  const [loading, setLoading] = useState<boolean>(false);
  const [email, setEmail] = useState<string>('<EMAIL>');
  const [password, setPassword] = useState<string>('1234Asdf!');
  const emailRef = useRef<RNTextInput>(null);
  const passwordRef = useRef<RNTextInput>(null);
  const [showAgeVerificationModal, setShowAgeVerificationModal] =
    useState(false);

  const accountStatus = useSelector((state: any) => state.user);

  const {
    systemActionForImprint,
    systemActionForChat,
    systemActionForUserReport,
  } = accountStatus?.userInfo || {};
  const actions = [
    systemActionForImprint,
    systemActionForChat,
    systemActionForUserReport,
  ];

  const violation = actions.find(
    item => item?.actionType === 'SUSPEND' || item?.actionType === 'BANNED',
  );
  const navigation = useNavigation();

  const _validateForm = () => {
    if (_.isEmpty(email)) {
      showToastMsg(TOAST_MESSAGES.EMAIL_REQUIRED);
      return false;
    } else if (!Util.isEmailValid(email)) {
      showToastMsg(TOAST_MESSAGES.VALID_EMAIL);
      return false;
    } else if (_.isEmpty(password)) {
      showToastMsg(TOAST_MESSAGES.PASSWORD_REQUIRED);
      return false;
    } else {
      return true;
    }
  };

  const onAppleButtonPress = async () => {
    try {
      setLoading(true);
      const appleAuthRequestResponse = await appleAuth.performRequest({
        requestedOperation: appleAuth.Operation.LOGIN,
        requestedScopes: [appleAuth.Scope.EMAIL, appleAuth.Scope.FULL_NAME],
      });

      const {fullName, identityToken} = appleAuthRequestResponse;

      const extraInfo = {
        displayName: fullName,
        avatarUrl: '',
        isGoogleLogin: true,
      };
      const payload: ISocialPayload = {
        idToken: identityToken as string,
      };

      userAppleLoginRequest(payload, (res: any) => {
        setLoading(true);
        if (res) {
          setTimeout(() => {
            userInfoRequest(res => {
              if (res) {
                if (res.isOnboarded === true && res.ageVerified === false) {
                  setTimeout(() => {
                    setShowAgeVerificationModal(true);
                  }, 500);
                  return;
                }
                if (
                  res.isOnboarded &&
                  res.onboardingQuizStatus === QUIZ_STATUS.COMPLETED
                ) {
                  if (
                    ![
                      SubscriptionStatus.Inactive,
                      SubscriptionStatus.Incomplete,
                      SubscriptionStatus.Canceled,
                    ].includes(res.subscriptionStatus)
                  ) {
                    // If the user has a valid subscription status, navigate to HOME_TABS
                    (navigation.navigate as (route: string) => void)(
                      Routes.HOME_TABS,
                    );
                  } else {
                    util.resetToUserProfile(navigation);
                  }
                } else {
                  if (extraInfo.avatarUrl === null) {
                    (
                      navigation.navigate as (
                        route: string,
                        params: any,
                      ) => void
                    )(Routes.PROFILE_SETUP, {personalData: res});
                  } else if (res.profile.dob === null) {
                    (
                      navigation.navigate as (
                        route: string,
                        params: any,
                      ) => void
                    )(Routes.PROFILE_SETUP, {personalData: extraInfo});
                  } else if (
                    res.isOnboarded === true &&
                    res.ageVerified === false
                  ) {
                    setTimeout(() => {
                      setShowAgeVerificationModal(true);
                    }, 500);
                    return;
                  } else if (
                    res.onboardingQuizStatus === QUIZ_STATUS.NOT_ATTEMPTED
                  ) {
                    (navigation.navigate as (route: string) => void)(
                      Routes.QUIZ_FLOW,
                    );
                  } else {
                    return (
                      navigation.navigate as (
                        route: string,
                        params: any,
                      ) => void
                    )(Routes.PROFILE_SETUP, {personalData: extraInfo});
                  }
                }
              } else {
                showToastMsg('Something went wrong ');
              }
            });
          }, 1000);
          setLoading(false);
        }
        setLoading(false);
      });
    } catch (error) {
      setLoading(false);

      console.warn('Apple Sign-in Error', error);
    }
  };

  const _onsubmitForm = () => {
    if (_validateForm()) {
      setLoading(true);
      const payload = {
        email: email.toLowerCase(),
        password: password,
      };

      userSignInRequest(payload, (res: any) => {
        if (res) {
          if (res.isEmailVerified === false) {
            (
              navigation.navigate as (
                route: string,
                param: {email: string; purpose: string},
              ) => void
            )(Routes.OTP_SCREEN, {
              email: email,
              purpose: OTP_ENUMS.USER_VERIFICATION,
            });
          } else {
            setTimeout(() => {
              userInfoRequest(res => {
                if (res) {
                  if (res.isOnboarded === true && res.ageVerified === false) {
                    setTimeout(() => {
                      setShowAgeVerificationModal(true);
                    }, 500);
                    return;
                  }
                  if (
                    res.systemActionForImprint ||
                    res.systemActionForUserReport
                  ) {
                    setLoading(false);
                    return;
                  } else {
                    if (
                      res.isOnboarded &&
                      res.onboardingQuizStatus === QUIZ_STATUS.COMPLETED
                    ) {
                      if (
                        ![
                          SubscriptionStatus.Inactive,
                          SubscriptionStatus.Incomplete,
                          SubscriptionStatus.Canceled,
                        ].includes(res.subscriptionStatus)
                      ) {
                        (navigation.navigate as (route: string) => void)(
                          Routes.HOME_TABS,
                        );
                      } else {
                        (navigation.navigate as (route: string) => void)(
                          Routes.HOME_TABS,
                        );
                      }
                    } else {
                      if (res.isOnboarded === false) {
                        (
                          navigation.navigate as (
                            route: string,
                            params: any,
                          ) => void
                        )(Routes.PROFILE_SETUP, {personalData: res});
                      } else if (
                        res.isOnboarded === true &&
                        res.ageVerified === false
                      ) {
                        setTimeout(() => {
                          setShowAgeVerificationModal(true);
                        }, 500);
                        return;
                      } else if (
                        res.onboardingQuizStatus === QUIZ_STATUS.NOT_ATTEMPTED
                      ) {
                        (navigation.navigate as (route: string) => void)(
                          Routes.QUIZ_FLOW,
                        );
                      } else {
                        return (
                          navigation.navigate as (
                            route: string,
                            params: any,
                          ) => void
                        )(Routes.PROFILE_SETUP, {personalData: ''});
                      }
                    }
                  }
                }
              });
            }, 100);
          }

          setLoading(false);
        }
        setLoading(false);
      });
    }
  };

  const headerText = () => {
    return (
      <Text
        type="semi_bold"
        size={'large'}
        color={Colors.black}
        style={styles.headerText}>
        Sign In
      </Text>
    );
  };

  const footerText = () => {
    const navigation = useNavigation();

    return (
      <View style={styles.footerTextContainer}>
        <View style={AppStyles.flexRow}>
          <Text color={Colors.text.gray} type="medium" size={'xSmall'}>
            {SIGN_IN.NEW_IMPRINT}
          </Text>
          <ButtonView
            onPress={() =>
              (navigation.navigate as (route: string) => void)(Routes.SIGNUP)
            }>
            <Text
              color={Colors.text.black}
              type="semi_bold"
              textAlign="center"
              size={'xSmall'}
              style={AppStyles.mLeft5}>
              {SIGN_IN.SIGN_UP}
            </Text>
          </ButtonView>
        </View>
      </View>
    );
  };

  const onGooglePress = async () => {
    try {
      await GoogleSignin.hasPlayServices({
        // Check if device has Google Play Services installed
        // Always resolves to true on iOS
        showPlayServicesUpdateDialog: true,
      });
      const userInfo = await GoogleSignin.signIn();
      const payload: ISocialPayload = {
        idToken: userInfo.data?.idToken as string,
      };
      const extraInfo = {
        displayName: userInfo.data?.user?.givenName,
        avatarUrl: userInfo.data?.user?.photo,
        isGoogleLogin: true,
      };
      userGoogleLoginRequest(payload, (res: any) => {
        setLoading(true);
        if (res) {
          setTimeout(() => {
            userInfoRequest(res => {
              if (res) {
                if (res.isOnboarded === true && res.ageVerified === false) {
                  setTimeout(() => {
                    setShowAgeVerificationModal(true);
                  }, 500);
                  return;
                }
                if (
                  res.isOnboarded &&
                  res.onboardingQuizStatus === QUIZ_STATUS.COMPLETED
                ) {
                  if (
                    ![
                      SubscriptionStatus.Inactive,
                      SubscriptionStatus.Incomplete,
                      SubscriptionStatus.Canceled,
                    ].includes(res.subscriptionStatus)
                  ) {
                    // If the user has a valid subscription status, navigate to HOME_TABS
                    (navigation.navigate as (route: string) => void)(
                      Routes.HOME_TABS,
                    );
                  } else {
                    util.resetToUserProfile(navigation);
                  }

                  // (navigation.navigate as (route: string) => void)(
                  //   Routes.HOME_TABS,
                  // );
                } else {
                  if (extraInfo.avatarUrl === null) {
                    (
                      navigation.navigate as (
                        route: string,
                        params: any,
                      ) => void
                    )(Routes.PROFILE_SETUP, {personalData: res});
                  } else if (res.profile.dob === null) {
                    (
                      navigation.navigate as (
                        route: string,
                        params: any,
                      ) => void
                    )(Routes.PROFILE_SETUP, {personalData: extraInfo});
                  } else if (
                    res.isOnboarded === true &&
                    res.ageVerified === false
                  ) {
                    setTimeout(() => {
                      setShowAgeVerificationModal(true);
                    }, 500);
                    return;
                  } else if (
                    res.onboardingQuizStatus === QUIZ_STATUS.NOT_ATTEMPTED
                  ) {
                    (navigation.navigate as (route: string) => void)(
                      Routes.QUIZ_FLOW,
                    );
                  } else {
                    return (
                      navigation.navigate as (
                        route: string,
                        params: any,
                      ) => void
                    )(Routes.PROFILE_SETUP, {personalData: extraInfo});
                  }
                }
              } else {
                showToastMsg('Something went wrong ');
              }
            });
          }, 1000);
          setLoading(false);
          // alert('login Success');
        }
        setLoading(false);
      });
    } catch (error: any) {
      if (error.code === statusCodes.SIGN_IN_CANCELLED) {
        alert('User cancelled the login flow !');
      } else if (error.code === statusCodes.IN_PROGRESS) {
        alert('Signin in progress');
      } else if (error.code === statusCodes.PLAY_SERVICES_NOT_AVAILABLE) {
        alert('Google play services not available or outdated !');
      } else {
        alert(error);
      }
    }
  };

  const handleAgeVerificationComplete = () => {
    setShowAgeVerificationModal(false);
    setLoading(false);
    util.handleUserInfo(
      userInfoRequest,
      navigation,
      setLoading,
      setShowAgeVerificationModal,
    );
  };

  return (
    <SafeAreaView>
      <View style={styles.container}>
        <SPLASH_BG />
      </View>
      <AgeVerificationModal
        visible={showAgeVerificationModal}
        onVerificationComplete={handleAgeVerificationComplete}
        onStartVerification={startYotiSessionRequest}
        userInfoRequest={userInfoRequest}
        logout={() => {
          util.handleUserLogout(userLogout, navigation);
          setShowAgeVerificationModal(false);
        }}
        updateDobRequest={updateDobRequest}
      />

      {headerText()}
      <TextInput
        ref={emailRef}
        containerStyle={styles.emailInputContainer}
        label={SIGN_IN.EMAIL_LABEL}
        placeholder={SIGN_IN.EMAIL_PLACEHOLDER}
        leftImage={<EMAIL_ICON />}
        onChangeText={val => setEmail(val)}
        backgroundColor="#FFF9F4"
        onSubmitEditing={() => passwordRef.current?.focus()}
      />
      <TextInput
        ref={passwordRef}
        containerStyle={styles.passwordInputContainer}
        label={SIGN_IN.PASSWORD_LABEL}
        placeholder={SIGN_IN.PASSWORD_PLACEHOLDER}
        leftImage={<LOCK_ICON />}
        rightImage={secureText ? <EYE_CLOSE /> : <EYE_OPEN />}
        onPress={() => setSecureText(!secureText)}
        backgroundColor="#FFF9F4"
        secureTextEntry={secureText}
        onChangeText={val => setPassword(val)}
      />
      <ButtonView
        style={styles.forgotContainer}
        onPress={() =>
          (navigation.navigate as (route: string) => void)(
            Routes.RESET_EMAIL_SCREEN,
          )
        }>
        <Text type="semi_bold" size={'normal'} color={Colors.text.black}>
          Forgot Password
        </Text>
      </ButtonView>
      <AppButton
        text={'Sign In'}
        onPress={_onsubmitForm}
        textColor={Colors.text.white}
      />
      <DividerWithText />
      <View
        style={{
          flexDirection: 'row',
          justifyContent: 'center',
        }}>
        <ButtonView style={styles.socialLoginContainer} onPress={onGooglePress}>
          <GOOGLE_LOGIN />
        </ButtonView>
        {Platform.OS === 'ios' && (
          <TouchableOpacity style={styles.button} onPress={onAppleButtonPress}>
            <APPLE_LOGIN />
          </TouchableOpacity>
        )}
      </View>
      {footerText()}
      <Loader loading={loading} />
    </SafeAreaView>
  );
};

const mapStateToProps = ({user}: RootState) => ({user});

const actions = {
  userSignInRequest,
  userGoogleLoginRequest,
  userInfoRequest,
  userAppleLoginRequest,
  userLogout,
  updateDobRequest,
  startYotiSessionRequest,
};

export default connect(mapStateToProps, actions)(Login);
