import React, {useState, useEffect, useCallback} from 'react';
import {Keyboard, Platform, View, Image} from 'react-native';
import {
  Bubble,
  BubbleProps,
  GiftedChat,
  IMessage,
  InputToolbar,
  InputToolbarProps,
  Send,
  SendProps,
  Time,
  TimeProps,
} from 'react-native-gifted-chat';
import {connect} from 'react-redux';
import {
  CustomNavbar,
  ReportModal,
  Text,
  ViolationModal,
} from '../../components';
import {AppStyles, Colors} from '../../theme';
import {hasNotch} from 'react-native-device-info';
import styles from './styles';
import {useFocusEffect, useNavigation} from '@react-navigation/native';
import {FLAG_RED, SEND_BUTTON} from '../../constants/AssetSVGConstants';
import {
  getConversationMessagesRequest,
  updateMessageReadStatus,
  getChatTokenRequest,
  flagMessageRequest,
  deleteMessageRequest,
} from '../../actions/ChatActions.ts';
import {blockMessageRequest} from '../../actions/FollowersActions';
import {
  CHAT_BLOCK_MAPPING,
  CONTENT_TYPE_MAPPING,
  EventScrollType,
  IBlockEntity,
  IDeleteMessage,
  IFlagMessagePayload,
  IGetMessagesPayload,
  IReportContentPayload,
  ITransparencyPayload,
  REPORT_ENUMS,
  RootState,
  UserState,
} from '../../types';
import {debounce} from 'lodash';
import util from '../../util';
import {showAlertMsg, showToastMsg} from '../../components/Alert/index.tsx';
import {
  CHAT_LIST,
  TOAST_MESSAGES,
  TOAST_TYPE,
} from '../../constants/StringConstants.ts';
import _ from 'lodash';
import {
  CHAT_MORE_FOR_OTHER_MESSAGES,
  CHAT_MORE_FOR_OWN_MESSAGES,
  gifMapping,
} from '../../constants/index.ts';
import GifSelector from '../../components/ChatComponents/GifSelector/index.tsx';
import {AvoidSoftInput} from 'react-native-avoid-softinput';
import {useWebSocket} from '../../Hooks/useChatWebSocket.tsx';
import {reportContentRequest} from '../../actions/TimelineActions.ts';
import Routes from '../../constants/RouteConstants.ts';
import {getTransparencyDataRequest} from '../../actions/NotificationActions.ts';

interface ChatProps {
  user: UserState;
  getConversationMessagesRequest: (
    payload: IGetMessagesPayload,
    callback: (res: any) => void,
  ) => void;
  blockMessageRequest: (
    payload: IBlockEntity,
    callback: (res: any) => void,
  ) => void;
  getChatTokenRequest: (callback: (res: any) => void) => void;
  updateMessageReadStatus: (payload: any, callback: (res: any) => void) => void;
  flagMessageRequest: (
    payload: IFlagMessagePayload,
    callback: (res: any) => void,
  ) => void;

  deleteMessageRequest: (
    payload: IDeleteMessage,
    callback: (res: any) => void,
  ) => void;
  reportContentRequest: (
    payload: IReportContentPayload,
    callback: (res: any) => void,
  ) => void;
  getTransparencyDataRequest: (
    payload: ITransparencyPayload,
    responseCallback: any,
  ) => void;

  route: {
    params: {
      conversationId: string;
      participantList: any[];
      isBlocked: boolean;
      title: string;
      canMessage: boolean;
    };
  };
}

const Chat: React.FC<ChatProps> = ({
  getConversationMessagesRequest,
  updateMessageReadStatus,
  getChatTokenRequest,
  deleteMessageRequest,
  reportContentRequest,
  getTransparencyDataRequest,
  route,
  user,
}) => {
  const navigation = useNavigation();
  const [messages, setMessages] = useState<IMessage[] | any[]>([]);
  const [page, setPage] = useState<number>(1);
  const [loading, setLoading] = useState(false);
  const [allDataLoaded, setAllDataLoaded] = useState(false);
  const [selectedMessage, setSelectedMessage] = useState<any>({});
  const [showGif, setShowGif] = useState<boolean>(false);
  const [showReportModal, setShowReportModal] = useState<boolean>(false);
  const [showViolation, setShowViolation] = useState<boolean>(false);
  const [ageGroup, setAgeGroup] = useState<string>('');
  const [reportType, setReportType] = useState<string>('');

  const [isBlocked] = useState<any>(route.params.isBlocked);

  const conversationID = route.params.conversationId;
  const participantList = route.params.participantList;

  const handleNewMessage = (receivedMessage: any) => {
    const OtherUser = participantList.find(
      participant => participant.id === receivedMessage.fromUserId,
    );

    const image =
      receivedMessage?.attachmentType === 'GIF' &&
      gifMapping[receivedMessage.attachmentUrl]
        ? Image.resolveAssetSource(gifMapping[receivedMessage.attachmentUrl])
            .uri
        : null;

    const decodedText = util
      .decodeContent(receivedMessage.message || '')
      .replace(/[^\x20-\x7E‘’”]/g, '');
    if (OtherUser) {
      const newMessage: any[] = [
        {
          _id: receivedMessage.messageId,
          text: decodedText || '',
          createdAt: new Date(),
          isFlagged: null,
          user: {
            _id: OtherUser.id,
            name: OtherUser.displayName,
            avatar: OtherUser.avatarUrl,
          },
          image: image,
        },
      ];

      setMessages(prevMessages => [...newMessage, ...prevMessages]);
    } else {
      const newMessage: any[] = [
        {
          _id: receivedMessage.messageId,
          text: decodedText || '',
          createdAt: new Date(),
          isFlagged: null,
          user: {
            _id: user.userInfo.userId,
            name: user.userInfo.userName,
            avatar: user.userInfo.avatarUrl,
          },
          image: image,
        },
      ];
      setMessages(prevMessages => [...newMessage, ...prevMessages]);
    }
  };

  useFocusEffect(
    useCallback(() => {
      if (Platform.OS === 'android') {
        AvoidSoftInput?.setAdjustResize();
      }
      return () => {
        if (Platform.OS === 'android') {
          AvoidSoftInput.setAdjustPan();
        }
      };
    }, []),
  );

  const {sendMessage, sendGif} = useWebSocket(
    getChatTokenRequest,
    conversationID,
    handleNewMessage,
  );

  useEffect(() => {
    getMessages();
  }, [page]);

  const getMessages = () => {
    if (loading && allDataLoaded) return;

    setLoading(true);

    const payload: IGetMessagesPayload = {
      convId: route.params.conversationId,
      page: page,
    };

    getConversationMessagesRequest(payload, res => {
      setLoading(false);

      if (res) {
        const updatedResponse: any[] = util.tranFormChatResponse(res);
        if (updatedResponse.length === 0) {
          setAllDataLoaded(true);
        } else {
          setMessages(prevMessages => [...prevMessages, ...updatedResponse]);
        }
      }
    });
  };

  useEffect(() => {
    const payload = {
      conversationId: route.params.conversationId,
    };
    updateMessageReadStatus(payload, () => {});
  }, []);

  const handleEndReached = debounce(() => {
    if (!loading && !allDataLoaded) {
      setPage(prevPage => prevPage + 1);
    } else {
      setLoading(false);
    }
  }, 300);

  const renderBubble = (props: any) => {
    const currentMessageIndex = messages.findIndex(
      message => message._id === props.currentMessage._id,
    );
    const showFlag =
      currentMessageIndex !== -1 &&
      messages[currentMessageIndex].isFlagged !== null &&
      props.currentMessage.user?._id !== user.userInfo.userId;
    return (
      <Bubble
        renderCustomView={() => {
          if (props.position === 'left' && showFlag) {
            return (
              <View style={styles.iconContainer}>
                <FLAG_RED width={10} height={10} />
              </View>
            );
          }
          return null;
        }}
        {...props}
        textStyle={{
          left: styles.textLeftStyle,
          right: styles.textRightStyle,
        }}
        customTextStyle={[
          styles.combineTextStyle,
          props.currentMessage.text === CHAT_LIST.MESSAGE_DELETED &&
            styles.deleted,
        ]}
        wrapperStyle={{
          right: [
            styles.chatBubbleRightStyle,
            props.currentMessage.image !== null && styles.transparentBubble,
          ],
          left: [
            styles.chatBubbleLeftStyle,
            props.currentMessage.image !== null && styles.transparentBubble,
          ],
        }}
      />
    );
  };

  const onSend = (messages: IMessage[]) => {
    sendMessage(messages[0].text);
  };

  const renderSend = (props: any) => {
    return (
      <Send {...props} containerStyle={styles.sendIcon}>
        <SEND_BUTTON height={17} width={17} />
      </Send>
    );
  };

  const onClickAppeal = () => {
    const payload: ITransparencyPayload = {
      complex_type: CONTENT_TYPE_MAPPING.CONVERSATION_MESSAGE,
      content_id: user.userInfo?.systemActionForChat?.violationContentId ?? '',
    };

    getTransparencyDataRequest(payload, (res: any) => {
      if (res) {
        (navigation.navigate as (route: string, params: any) => void)(
          Routes.REPORT_SCREEN,
          {url: res.redirectUrl, title: ''},
        );
      }
    });
  };

  const renderInputToolbar = (props: any) => {
    const canMessage = route.params?.canMessage;
    const actionType = user.userInfo?.systemActionForChat?.actionType;
    const isBanned = actionType === CHAT_BLOCK_MAPPING.BAN_CONVERSATION;

    console.log(user.userInfo?.systemActionForChat?.actionType);

    if (isBanned) {
      return (
        <Text textAlign="left" style={styles.bannedText} size={'xxSmall'}>
          {TOAST_MESSAGES.BLOCK_CHAT_MESSAGE}
          <Text
            onPress={onClickAppeal}
            textAlign="center"
            style={styles.underlineText}>
            {'\n'}
            {CHAT_LIST.CLICK_HERE}
          </Text>
        </Text>
      );
    }
    if (!canMessage) {
      return (
        <Text textAlign="left" style={styles.bannedText} size={'xxSmall'}>
          {TOAST_MESSAGES.BLOCK_UNDER_18_MESSAGE}
          <Text
            onPress={onClickAppeal}
            textAlign="center"
            style={styles.underlineText}>
            {'\n'}
            {CHAT_LIST.CLICK_HERE}
          </Text>
        </Text>
      );
    }

    if (isBlocked && participantList.length === 1) {
      return (
        <Text
          style={AppStyles.mBottom20}
          color={Colors.black}
          type="medium"
          textAlign="center"
          size={'xxSmall'}>
          {CHAT_LIST.BLOCK_CHAT_ERROR}
        </Text>
      );
    }
    return (
      <>
        {isBlocked && participantList.length > 1 ? (
          <Text
            color={Colors.black}
            type="medium"
            textAlign="center"
            size={'xxSmall'}
            style={AppStyles.marginVerticalBase}>
            {CHAT_LIST.GROUP_CHAT_WARNING}
          </Text>
        ) : null}
        <InputToolbar
          {...props}
          containerStyle={styles.inputContainer}
          placeholderTextColor={Colors.text.placeHolderTextColor}
        />
      </>
    );
  };

  const isCloseToTop = ({
    layoutMeasurement,
    contentOffset,
    contentSize,
  }: {
    layoutMeasurement: EventScrollType;
    contentOffset: {x: number; y: number};
    contentSize: EventScrollType;
  }): boolean => {
    return (
      contentSize.height - layoutMeasurement.height - 20 <= contentOffset.y
    );
  };

  const handleScroll = (nativeEvent: {
    layoutMeasurement: EventScrollType;
    contentOffset: {x: number; y: number};
    contentSize: EventScrollType;
  }) => {
    if (isCloseToTop(nativeEvent)) {
      handleEndReached();
    }
  };

  const renderTime = (props: TimeProps<IMessage>) => {
    return (
      <>
        <Time
          {...props}
          timeTextStyle={{
            left: [
              styles.leftTimeStyle,
              props.currentMessage.image !== null && {
                color: Colors.black,
              },
            ],
            right: [
              styles.rightTimeStyle,
              props.currentMessage.image !== null && {
                color: Colors.black,
              },
            ],
          }}
        />
      </>
    );
  };

  const onLongPress = (
    context: any,
    message: React.SetStateAction<any> | IMessage,
  ) => {
    Platform.OS == 'android' && Keyboard.dismiss();
    let options: any[] = [];
    if (user.userInfo.userId === message.user._id) {
      options = CHAT_MORE_FOR_OWN_MESSAGES;
    } else {
      options = CHAT_MORE_FOR_OTHER_MESSAGES;
    }

    const cancelButtonIndex = options.length - 1;

    context.actionSheet().showActionSheetWithOptions(
      {
        options,
        cancelButtonIndex,
      },
      (buttonIndex: any) => {
        const selectedOption = options[buttonIndex];

        switch (selectedOption) {
          case CHAT_LIST.REPORT_MESSAGE:
            setSelectedMessage(message);
            setTimeout(() => {
              return showAlertMsg(
                CHAT_LIST.REPORT_HEADING,
                CHAT_LIST.REPORT_HEADING_DETAIL,
                [
                  {
                    text: 'Yes',
                    onPress: () => handleReportModal(message),
                    style: 'default',
                  },
                  {
                    text: 'No',
                    onPress: () => {},
                    style: 'cancel',
                  },
                ],
              );
            }, 500);
            break;

          case CHAT_LIST.DELETE:
          case CHAT_LIST.DELETE_FOR_ME:
            setSelectedMessage(message);
            deleteMessage(message, false);
            break;

          case CHAT_LIST.DELETE_FOR_EVERY_ONE:
            deleteMessage(message, true);
            break;

          default:
            setShowReportModal(false);
            break;
        }
      },
    );
  };

  const handleReportModal = (message: any) => {
    setShowReportModal(true);
    setSelectedMessage(message);
  };

  const deleteMessage = (msg: IMessage, forAll: boolean) => {
    const payload: IDeleteMessage = {
      msgId: msg._id,
      forAll: forAll,
    };

    deleteMessageRequest(payload, res => {
      if (res) {
        const currentIndex = messages.findIndex(
          message => message._id === msg._id,
        );
        if (currentIndex !== -1) {
          const updatedMessageList = _.cloneDeep(messages);
          updatedMessageList[currentIndex].text = CHAT_LIST.MESSAGE_DELETED;
          if (msg.image) {
            updatedMessageList[currentIndex].image = null;
          }
          setMessages(updatedMessageList);
        }
      }
    });
  };

  return (
    <View style={styles.container}>
      <View
        style={
          hasNotch() ? styles.statusBar : styles.statusBarWithoutNotch
        }></View>
      {showReportModal && (
        <ReportModal
          reportType={REPORT_ENUMS.MESSAGE}
          crossClicked={() => setShowReportModal(false)}
          handleSubmitClick={(ageGroup, option) => {
            setAgeGroup(ageGroup);
            setReportType(
              option === REPORT_ENUMS.POST
                ? REPORT_ENUMS.CHAT
                : REPORT_ENUMS.PROFILE,
            );
            setShowReportModal(false);
            setShowViolation(true);
          }}
        />
      )}
      {showViolation && (
        <ViolationModal
          crossClicked={() => setShowViolation(false)}
          handleSubmitClick={ageGroup => {
            setShowViolation(false);
            const payload: IReportContentPayload = {
              id:
                reportType === REPORT_ENUMS.CHAT
                  ? selectedMessage?._id
                  : selectedMessage.user._id,
              type: reportType,
              potential_violations: ageGroup,
            };

            reportContentRequest(payload, res => {
              if (res) {
                const currentIndex = messages.findIndex(
                  message => message._id === selectedMessage?._id,
                );
                const updatedMessageList = _.cloneDeep(messages);
                if (reportType === REPORT_ENUMS.CHAT) {
                  updatedMessageList[currentIndex].isFlagged = res;
                  setMessages(updatedMessageList);
                }

                setShowViolation(false);
                showToastMsg(
                  reportType === REPORT_ENUMS.CHAT
                    ? TOAST_MESSAGES.REPORT_CHAT
                    : TOAST_MESSAGES.REPORT_USER,
                  TOAST_TYPE.SUCCESS,
                  2000,
                  'top',
                );

                // onFlagImprint(imprintData);
              }
              setShowViolation(false);
            });
          }}
          ageGroup={ageGroup}
        />
      )}
      <CustomNavbar
        leftBtnPress={() => {
          Keyboard.dismiss();
          setTimeout(() => {
            navigation.goBack();
          }, 100);
        }}
        title={
          route.params.title === null
            ? participantList
                .slice(0, 2)
                .map(item => item?.userName || '')
                .filter(userName => userName !== '')
                .join(', ')
            : route.params.title
        }
        hasMultiRight={true}
        style={styles.navBarColor}
      />

      <GiftedChat
        keyboardShouldPersistTaps="never"
        textInputProps={styles.textInputProps}
        listViewProps={{
          scrollEventThrottle: 100,
          onScroll: ({nativeEvent}: any) => {
            if (isCloseToTop(nativeEvent)) {
              Keyboard.dismiss();
              handleScroll(nativeEvent);
            }
          },

          onEndReachedThreshold: 0.1,
          onEndReached: handleEndReached,
        }}
        messages={messages}
        onSend={(messages: never[] | IMessage[]) => onSend(messages)}
        user={{
          _id: user.userInfo.userId,
          name: user.userInfo.displayName,
          avatar: user.userInfo.avatarUrl,
        }}
        inverted={true}
        renderSend={(props: SendProps<IMessage>) => renderSend(props)}
        renderInputToolbar={(props: InputToolbarProps<IMessage>) =>
          renderInputToolbar(props)
        }
        showAvatarForEveryMessage={true}
        alwaysShowSend
        renderUsernameOnMessage={false}
        renderBubble={(props: BubbleProps<IMessage>) => renderBubble(props)}
        showUserAvatar
        renderAvatarOnTop={false}
        renderTime={props => renderTime(props)}
        maxComposerHeight={80}
        onLongPress={(context: any, message: IMessage) => {
          setTimeout(() => {
            onLongPress(context, message);
          }, 500);
        }}
        extraData={messages}
        bottomOffset={Platform.OS === 'ios' ? -20 : 0}
        lightboxProps={{disabled: true}}
        renderActions={() => (
          <GifSelector
            showGif={showGif}
            setShowGif={setShowGif}
            sendGif={sendGif}
          />
        )}
      />
    </View>
  );
};

const mapStateToProps = (state: RootState) => ({
  user: state.user,
});
const actions = {
  getConversationMessagesRequest,
  updateMessageReadStatus,
  getChatTokenRequest,
  flagMessageRequest,
  deleteMessageRequest,
  blockMessageRequest,
  reportContentRequest,
  getTransparencyDataRequest,
};

export default connect(mapStateToProps, actions)(Chat);
