import {StyleSheet} from 'react-native';
import {Colors, Fonts, Metrics} from '../../theme';
import {getStatusBarHeight} from 'react-native-iphone-x-helper';

export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.home,
  },
  statusBar: {
    backgroundColor: Colors.background.home,
    height: getStatusBarHeight() + 30,
  },
  statusBarWithoutNotch: {
    backgroundColor: Colors.background.home,
    height: Metrics.ratio(30),
  },
  inputContainer: {
    paddingBottom: Metrics.ratio(5),
    marginBottom: Metrics.doubleBaseMargin,
    fontSize: Fonts.size.large,
    width: Metrics.screenWidth * 0.65,
    maxHeight: Metrics.ratio(100),
    shadowColor: Colors.black,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 10.84,
    elevation: 5,
    borderRadius: Metrics.ratio(75),
    borderColor: 'transparent',

    alignSelf: 'center',
  },
  textInputProps: {
    fontSize: Fonts.size.xSmall,
    color: Colors.text.placeHolderTextColor,
    maxHeight: Metrics.ratio(80),
    marginLeft: Metrics.ratio(25),
    marginRight: Metrics.ratio(25),
    textAlign: 'left',
  },
  navBarColor: {backgroundColor: Colors.background.home},
  sendIcon: {
    backgroundColor: Colors.white,
    height: Metrics.ratio(40),
    width: Metrics.ratio(40),
    borderRadius: Metrics.ratio(25),
    marginHorizontal: Metrics.ratio(12),
    alignItems: 'center',
    justifyContent: 'center',
    alignSelf: 'center',
    position: 'absolute',
    left: Metrics.screenWidth * 0.65,
  },
  textLeftStyle: {
    maxWidth: Metrics.screenWidth * 0.6,
    backgroundColor: Colors.black,
    color: Colors.white,
  },
  textRightStyle: {
    color: Colors.toggleColors.activeColor,
    marginHorizontal: Metrics.baseMargin,
  },
  combineTextStyle: {
    overflow: 'hidden',
    paddingHorizontal: Metrics.ratio(0),
  },
  chatBubbleRightStyle: {
    marginBottom: Metrics.ratio(10),
    backgroundColor: Colors.white,
    borderWidth: Metrics.ratio(0),
    borderColor: 'transparent',
    borderTopLeftRadius: Metrics.ratio(10),
    borderTopRightRadius: Metrics.ratio(6),
    borderBottomRightRadius: Metrics.ratio(6),
    borderBottomLeftRadius: Metrics.ratio(10),
    minWidth: Metrics.ratio(100),
  },
  chatBubbleLeftStyle: {
    overflow: 'visible',
    marginBottom: Metrics.ratio(10),
    backgroundColor: Colors.black,
    borderWidth: Metrics.ratio(0),
    borderColor: 'transparent',
    borderTopLeftRadius: Metrics.ratio(6),
    borderTopRightRadius: Metrics.ratio(10),
    borderBottomRightRadius: Metrics.ratio(10),
    borderBottomLeftRadius: Metrics.ratio(6),
    minWidth: Metrics.ratio(200),
  },
  leftTimeStyle: {
    textAlign: 'left',
    color: Colors.white,
    fontSize: Fonts.size.xxSmall,
  },
  rightTimeStyle: {
    color: Colors.toggleColors.activeColor,
    fontSize: Fonts.size.xxSmall,
  },

  deleted: {
    fontStyle: 'italic',
  },
  bannedText: {
    textAlign: 'center',
    color: Colors.black,
    paddingBottom: Metrics.baseMargin,
    marginHorizontal: Metrics.ratio(10),
  },
  underlineText: {textDecorationLine: 'underline', color: Colors.blue},
  transparentBubble: {
    backgroundColor: 'transparent',
  },

  iconContainer: {
    height: Metrics.ratio(20),
    width: Metrics.ratio(20),
    borderRadius: Metrics.ratio(10),
    backgroundColor: Colors.white,
    justifyContent: 'center',
    alignItems: 'center',
    shadowOffset: {
      width: 0,
      height: 12.857142448425293,
    },
    shadowOpacity: 0.33,
    shadowRadius: 25.714284896850586,
    elevation: 8,
    position: 'absolute',
    right: Metrics.ratio(-10),
    top: Metrics.ratio(10),
    zIndex: Metrics.ratio(10),
  },
});
