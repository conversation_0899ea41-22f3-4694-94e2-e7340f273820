// @flow
import React from 'react';
import PropTypes from 'prop-types';
import {View} from 'react-native';
import {CustomNavbar, Text} from '../../components';
import styles from './styles';
import {Colors} from '../../theme';

const Friend: React.FC = () => {
  return (
    <View style={styles.container}>
      <Text color={Colors.black}>Welcome To Friend Listing</Text>
    </View>
  );
};

export default Friend;
