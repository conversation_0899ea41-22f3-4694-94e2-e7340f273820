import React, {useCallback, useEffect, useState} from 'react';
import {
  View,
  FlatList,
  Animated,
  Linking,
  Modal,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import {
  ButtonView,
  EmptyStateComponent,
  ReportModal,
  Text,
  ViolationModal,
} from '../../components';
import {Colors, Metrics} from '../../theme';
import styles from './styles';
import {useNavigation} from '@react-navigation/native';
import {
  SEARCH_USERS,
  FollowerState,
  ITimeLine,
  UserImprintInteractions,
  PostReactionPayload,
  ReactionType,
  IBlockEntity,
  ImprintMedia,
  IDeleteImprintPayload,
  IFreeTextSearchPayload,
  IReportContentPayload,
  REPORT_ENUMS,
} from '../../types';
import {connect} from 'react-redux';
import {SafeAreaView} from 'react-native-safe-area-context';
import {
  CHAT_LIST,
  COMMON,
  HOME,
  IMPRINT_MEDIA,
  IMPRINT_TIMELINE,
  REPORT_CONTENT,
  SEARCH,
  TOAST_MESSAGES,
  TOAST_TYPE,
} from '../../constants/StringConstants';
import Routes from '../../constants/RouteConstants';
import ImprintScoresList from '../../components/ImprintScoresList';
import {
  getFreeTextSearchUsersRequest,
  getFreeTextImprintsRequest,
  getFreeTextImprintsSuccess,
  getFreeTextSearchUserSuccess,
} from '../../actions/SearchActions';

import SearchBarScreen from '../../components/SearchComponents/SearchScreenBar';
import SearchFriendsItem from '../../components/SearchComponents/SuggestedItem';
import {ScrollView} from 'react-native-gesture-handler';
import {FlashList} from '@shopify/flash-list';
import TimeLineCard from '../../components/TimeLine/TimeLineCard';
import {showAlertMsg, showToastMsg} from '../../components/Alert';
import {
  postReactionRequest,
  imprintVerificationRequest,
  imprintShoutoutRequest,
  imprintBookmarkRequest,
  requestFollowRequest,
  deleteImprintRequest,
  deleteImprintSuccess,
  reportContentRequest,
} from '../../actions/TimelineActions';
import {blockImprintRequest} from '../../actions/FollowersActions';
import RenderModal from '../../components/Common/MoreOption';

import util from '../../util';
import FileViewer from '../../components/FileViewer';
import ReportBadgeModal from '../../components/ReportContent/ReportBadgeModal';

interface ActionsProps {
  reportContentRequest: (
    payload: IReportContentPayload,
    callback: (res: any) => void,
  ) => void;

  getFreeTextSearchUsersRequest: (
    payload: IFreeTextSearchPayload,
    callback: (res: any) => void,
  ) => void;

  getFreeTextSearchUserSuccess: (callback: (res: any) => void) => void;

  getFreeTextImprintsRequest: (
    payload: IFreeTextSearchPayload,
    callback: (res: any) => void,
  ) => void;

  followFriendsRequest: (callback: (res: any) => void) => void;

  imprintVerificationRequest: (
    payload: any,
    callback: (res: any) => void,
  ) => void;
  postReactionRequest: (payload: any, callback: (res: any) => void) => void;
  imprintShoutoutRequest: (payload: any, callback: (res: any) => void) => void;
  imprintBookmarkRequest: (payload: any, callback: (res: any) => void) => void;
  blockImprintRequest: (
    payload: IBlockEntity,
    callback: (res: any) => void,
  ) => void;
  requestFollowRequest: (payload: any, callback: (res: any) => void) => void;
  deleteImprintRequest: (
    payload: IDeleteImprintPayload,
    callback: (res: any) => void,
  ) => void;

  followers: FollowerState;
  user: any;
  recentFriends: any;
  recentImprints: any;
}

const SearchUsers: React.FC<ActionsProps> = ({
  getFreeTextSearchUsersRequest,
  getFreeTextImprintsRequest,
  imprintVerificationRequest,
  getFreeTextSearchUserSuccess,
  recentFriends,
  recentImprints,
  postReactionRequest,
  imprintShoutoutRequest,
  imprintBookmarkRequest,
  blockImprintRequest,
  requestFollowRequest,
  deleteImprintRequest,
  reportContentRequest,

  user,
}) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [search, setSearchText] = useState<string>('');
  const [scores, setScores] = useState<string[]>([]);
  const [paylod, setPayload] = useState<PostReactionPayload>({});
  const [blockAlert, setBlockAlert] = useState<boolean>(false);
  const [imprintData, setImprintData] = useState<ITimeLine>();
  const [blockRecording, setBlockRecording] = useState<boolean>(false);
  const [followId, setFollowId] = useState<string>('');
  const [pdfURL, setPdfURL] = useState<string>('');
  const [page, setPage] = useState<number>(1);
  const [showReportModal, setShowReportModal] = useState<boolean>(false);
  const [showViolation, setShowViolation] = useState<boolean>(false);
  const [showBadgeModal, setBadgeModal] = useState<boolean>(false);

  const [ageGroup, setAgeGroup] = useState<string>('');
  const [reportType, setReportType] = useState<string>('');

  const [visibleModal, setVisibleModal] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(false);

  const [flagAlert, setFlagAlert] = useState<boolean>(false);

  const navigation = useNavigation();

  const onSearchText = (searchText: string) => {
    setSearchText(searchText);
    setTimeout(() => {
      const payload: IFreeTextSearchPayload = {
        query: searchText,
        scores: scores.length > 0 ? [scores[0]] : [],
        page: 1,
        limit: 10,
      };
      getFreeTextSearchUsersRequest(payload, res => {
        if (res) {
          let filteredRes = res;

          const containsOwnId = res.items.some(
            item => item?.id === user?.userInfo?.userId,
          );

          if (containsOwnId) {
            filteredRes = res.items.filter(
              item => item?.id !== user?.userInfo?.userId,
            );
          }
          getFreeTextSearchUserSuccess(filteredRes);
        } else {
          getFreeTextSearchUserSuccess(() => []);
        }
      });
      getFreeTextImprintsRequest(payload, res => {});
    }, 100);
  };

  const onClearSearch = () => {
    setScores([]);
    setSearchText('');
    setPage(1);

    setTimeout(() => {
      fetchList('');
      setPage(1);
    }, 500);
  };

  const fetchList = (query = '') => {
    try {
      setLoading(true);
      const payload = {
        page: 1,
        scores: scores,
        query,
        limit: 10,
      };
      getFreeTextSearchUsersRequest(payload, (data: any) => {
        setLoading(false);
        if (data) {
          getFreeTextSearchUserSuccess(data);
          getFreeTextImprintsSuccess(data.items, payload);
        }
      });
    } catch (error) {
      console.error('Error fetching users:', error);
    }
  };

  useEffect(() => {
    fetchList(search);
  }, [scores]);

  useEffect(() => {
    fetchImprints();
  }, [scores, page]);

  const fetchImprints = () => {
    setLoading(true);
    const payload = {
      page,
      scores: scores.length ? scores : [],
      query: search,
      limit: 10,
    };
    getFreeTextImprintsRequest(payload, (res: ITimeLine[]) => {
      setLoading(false);
    });
  };

  const navToUserProfile = (item: SEARCH_USERS) => {
    (navigation.navigate as (route: string, params: any) => void)(
      Routes.USER_PROFILE,
      {userName: item.userName},
    );
  };

  const handleScoreClick = (scoreName: string) => {
    const updatedScores = scores.includes(scoreName)
      ? scores.filter(score => score !== scoreName)
      : [...scores, scoreName];

    setScores(updatedScores);
    setPage(1);
  };

  const handleVerifyClick = (imprint?: ITimeLine) => {
    if (!imprint) return;
    if (
      imprint?.verificationRequest &&
      imprint?.verificationRequest?.verificationStatus ===
        IMPRINT_TIMELINE.REQUESTED
    ) {
      showToastMsg(TOAST_MESSAGES.ALREADY_REQUESET);
    } else if (
      imprint?.verificationRequest?.verificationStatus ===
      IMPRINT_TIMELINE.FAILED
    ) {
      showToastMsg(TOAST_MESSAGES.FAILED);
    } else if (
      imprint?.verificationRequest?.verificationStatus ===
      IMPRINT_TIMELINE.VERIFIED
    ) {
      showToastMsg(TOAST_MESSAGES.POST_VERIFIED);
    } else if (
      imprint?.verificationRequest?.verificationStatus ===
      IMPRINT_TIMELINE.REQUEST_DENIED
    ) {
      showToastMsg(TOAST_MESSAGES.REQUEST_DENIED);
    } else {
      showAlertMsg(HOME.ARTICLE_TITLE, HOME.ARTICLE_DETAIlS, [
        {
          text: COMMON.YES,
          onPress: () => {
            const payload = {
              id: imprint?.id,
              isGlobal: true,
            };

            imprintVerificationRequest(payload, (res: any) => {
              if (res) {
                showToastMsg(
                  TOAST_MESSAGES.VERIFICATION_REQUEST_SENT,
                  TOAST_TYPE.SUCCESS,
                );
              }
            });
          },
          style: 'default',
        },
        {
          text: COMMON.NO,
          onPress: () => {},
          style: 'cancel',
        },
      ]);
    }
  };
  const onMoreOptionClick = (imprint?: ITimeLine) => {
    setFollowId(imprint?.userId || '');

    setImprintData(imprint);

    setVisibleModal(true);
  };

  const renderItem = ({item, index}: {item: any; index: number}) => {
    return (
      <SearchFriendsItem
        index={index}
        item={item}
        onItemPress={() => {
          navToUserProfile(item);
        }}
        onFollowPress={() => onFollowPress(item.id)}
      />
    );
  };
  const onUserNameClick = (userName?: string) => {
    if (userName !== user?.userInfo?.userName) {
      (navigation.navigate as (route: string, params: any) => void)(
        Routes.USER_PROFILE,
        {userName},
      );
    } else {
      (navigation.navigate as (route: string) => void)(Routes.USER_PROFILE);
    }
  };

  const onFlagImprint = (item?: ITimeLine) => {
    let newPayload = paylod;
    newPayload.isGlobal = true;
    newPayload.imprintId = item?.id;
    newPayload.redFlagReason = null;
    newPayload.isRedFlagged = true;
    newPayload.email = user.userInfo.email;
    postReactionRequest(newPayload, (res: UserImprintInteractions) => {
      if (res) {
        setPayload({});
        (navigation.navigate as (route: string, params: any) => void)(
          Routes.REPORT_SCREEN,
          {url: res.redirectUrl, title: CHAT_LIST.REPORT},
        );
      }
      setIsModalVisible(false);
    });
  };

  const onBlockPress = () => {
    const payload: IBlockEntity = {
      follower: imprintData?.userId || '',
      status: true,
    };
    setVisibleModal(false);
    setTimeout(() => {
      setBlockRecording(true);
      blockImprintRequest(payload, res => {
        if (res) {
          const obj = {
            userId: imprintData?.userId || '',
          };
          deleteImprintSuccess(obj);
          setVisibleModal(false);
          setBlockRecording(false);
          showToastMsg(
            TOAST_MESSAGES.BLOCK_IMPRINT_MESSAGE,
            TOAST_TYPE.SUCCESS,
          );
        }
      });
    }, 500);

    setBlockRecording(false);
    setVisibleModal(false);
  };

  useEffect(() => {
    if (blockAlert) {
      showAlertMsg(
        IMPRINT_TIMELINE.BLOCK_HEADING,
        IMPRINT_TIMELINE.BLOCK_DETAIL,

        [
          {
            text: COMMON.YES,
            onPress: () => {
              setBlockAlert(false);
              setTimeout(() => {
                onBlockPress();
              }, 500);
              setFlagAlert(true);
            },
            style: 'default',
          },
          {
            text: COMMON.NO,
            onPress: () => {
              setBlockAlert(false);
              setTimeout(() => {
                setFlagAlert(true);
              }, 500);
            },
            style: 'cancel',
          },
        ],
      );
    }
  }, [blockAlert]);

  const hideModal = () => {
    setVisibleModal(!visibleModal);
  };
  const handleOnPressArticle = (article: ImprintMedia) => {
    Linking.openURL(article.url ?? '');
  };

  useEffect(() => {
    if (flagAlert) {
      showAlertMsg(
        IMPRINT_TIMELINE.REPORT_HEADING,
        IMPRINT_TIMELINE.REPORT_DETAIL,

        [
          {
            text: COMMON.YES,
            onPress: () => {
              setFlagAlert(false);
              onFlagImprint(imprintData);
            },
            style: 'default',
          },
          {
            text: COMMON.NO,
            onPress: () => {
              setFlagAlert(false);
              setBlockAlert(false);
            },
            style: 'common',
          },
        ],
      );
    }
  }, [flagAlert]);

  const handleReactionClicked = useCallback(
    (
      reaction?: string,
      userImprintInteraction?: UserImprintInteractions,
      item?: ITimeLine,
    ) => {
      if (reaction === IMPRINT_TIMELINE.BOOKMARK) {
        const payload = {
          imprintId: item?.id,
          isBookmark: !userImprintInteraction?.isBookmarked,
          isGlobal: true,
        };
        imprintBookmarkRequest(payload, (res: any) => {});
      } else {
        if (!item && !userImprintInteraction) return;

        const payload: PostReactionPayload = {
          isHide: false,
          imprintId: item?.id,
          email: user.userInfo.email,
          reaction:
            userImprintInteraction?.reaction === reaction
              ? userImprintInteraction?.reaction
              : reaction === ReactionType.SHOUT_OUT ||
                reaction === IMPRINT_TIMELINE.FLAG ||
                reaction === IMPRINT_TIMELINE.BOOKMARK
              ? userImprintInteraction?.reaction
              : reaction,
          isRedFlagged:
            reaction === IMPRINT_TIMELINE.FLAG
              ? !userImprintInteraction?.isRedFlagged
              : userImprintInteraction?.isRedFlagged ?? false,
        };

        if (userImprintInteraction?.reaction === reaction) {
          payload.reaction = null;
        }

        payload.isGlobal = true;

        if (reaction === ReactionType.SHOUT_OUT) {
          util.shoutOutAlert(() => {
            const shoutoutPayload = {
              imprintId: item?.id,
            };
            imprintShoutoutRequest(shoutoutPayload, (res: any) => {});
          });
        } else if (reaction === IMPRINT_TIMELINE.FLAG) {
          setImprintData(item);
          item?.userId === user.data.userId
            ? setBadgeModal(true)
            : setShowReportModal(true);
        } else {
          if (payload.isRedFlagged) {
            delete payload.isRedFlagged;
          }
          postReactionRequest(payload, (res: UserImprintInteractions) => {});
        }
      }
    },
    [postReactionRequest, imprintShoutoutRequest, user?.data?.userId],
  );

  const onFollowPress = (userId: string) => {
    const payload = {
      to: userId,
      isGlobal: true,
    };
    requestFollowRequest(payload, (res: any) => {
      if (res) {
        showToastMsg(TOAST_MESSAGES.FOLLOW_REQUEST_SENT, TOAST_TYPE.SUCCESS);
      }
      setVisibleModal(false);
    });
  };

  const handleMediaClicked = useCallback((item?: ITimeLine) => {
    if (!item) return;
    if (
      item?.imprintMedias.length == 1 &&
      item?.imprintMedias[0]?.type == IMPRINT_MEDIA.DOCUMENT
    ) {
      setPdfURL(item?.imprintMedias[0]?.url || '');
    } else {
      (navigation.navigate as (route: string, params: any) => void)(
        Routes.TIME_LINE_MEDIA_VIEW,
        {item},
      );
    }
  }, []);

  const onDeletePress = () => {
    setLoading(true);
    setVisibleModal(false);

    const payload: IDeleteImprintPayload = {
      imprintId: imprintData?.id || '',
    };
    deleteImprintRequest(payload, res => {
      if (res) {
        showToastMsg(TOAST_MESSAGES.DELETE_IMPRINT_MESSAGE, TOAST_TYPE.SUCCESS);
        setLoading(false);
      }
    });
    setLoading(false);
  };
  const renderPDF = (pdfURL: string) => {
    const source = {
      uri: pdfURL,
      cache: true,
    };

    return (
      <Modal
        animationType="fade"
        transparent={true}
        visible={pdfURL !== ''}
        onRequestClose={() => {
          setPdfURL('');
        }}>
        <TouchableOpacity
          onPressOut={() => setPdfURL('')}
          style={styles.pdfModal}>
          <View style={styles.pdfStyle}>
            <FileViewer pdfURL={pdfURL} />
          </View>
        </TouchableOpacity>
      </Modal>
    );
  };
  return (
    <SafeAreaView style={styles.container}>
      {showReportModal && (
        <ReportModal
          reportType={REPORT_CONTENT.IMPRINT}
          crossClicked={() => setShowReportModal(false)}
          handleSubmitClick={(ageGroup, option) => {
            setAgeGroup(ageGroup);
            setReportType(option);
            setShowReportModal(false);
            setShowViolation(true);
          }}
        />
      )}
      {showViolation && (
        <ViolationModal
          crossClicked={() => setShowViolation(false)}
          handleSubmitClick={ageGroup => {
            setShowViolation(false);
            const payload: IReportContentPayload = {
              id:
                reportType === REPORT_ENUMS.POST
                  ? imprintData?.id || ''
                  : user.userInfo.userId,
              type: reportType,
              potential_violations: ageGroup,
              imprintId: imprintData?.id,
              userId: imprintData?.userId,
              isGlobal: true,
            };

            reportContentRequest(payload, res => {
              if (res) {
                showToastMsg(
                  reportType === REPORT_ENUMS.POST
                    ? TOAST_MESSAGES.REPORT_SENT
                    : TOAST_MESSAGES.REPORT_USER,
                  TOAST_TYPE.SUCCESS,
                  2000,
                  'top',
                );
              }
            });
            console.log(payload);
          }}
          ageGroup={ageGroup}
        />
      )}

      {showBadgeModal && (
        <ReportBadgeModal
          crossClicked={() => setBadgeModal(false)}
          handleSubmitClick={ageGroup => {
            setShowViolation(false);
            const payload: IReportContentPayload = {
              id: imprintData?.id,
              type: 'post',
              potential_violations: ageGroup,
              imprintId: imprintData?.id,
              userId: imprintData?.userId,
              isGlobal: !false,
            };
            console.log('PAYLOAD', payload);

            reportContentRequest(payload, res => {
              if (res) {
                showToastMsg(
                  TOAST_MESSAGES.REPORT_VALUE_BADGE,
                  TOAST_TYPE.SUCCESS,
                  2000,
                  'top',
                );
                setBadgeModal(false);
                // onFlagImprint(imprintData);
              }
            });
            console.log(payload);
          }}
          ageGroup={ageGroup}
        />
      )}
      <SearchBarScreen
        backBtnPress={navigation.goBack}
        autoFocus={true}
        placeholder={SEARCH.SEARCH}
        onSearchText={text => {
          onSearchText(text);
        }}
        value={search}
        clearSearch={onClearSearch}
      />
      {pdfURL ? renderPDF(pdfURL) : null}

      <ScrollView
        bounces={false}
        overScrollMode="never"
        keyboardShouldPersistTaps="handled"
        onMomentumScrollEnd={() => setPage(page + 1)}
        scrollEventThrottle={16}>
        <ImprintScoresList
          mainStyle={styles.marginTop}
          exrtaStyle={{
            backgroundColor: Colors.gray,
          }}
          selectedScores={scores}
          isPersonal={false}
          onScoresPress={handleScoreClick}
        />
        {loading && <ActivityIndicator color={Colors.black} />}

        <View
          style={
            recentFriends?.length > 0
              ? styles.horizontalContainer
              : {padding: 0}
          }>
          {recentFriends?.length > 0 && (
            <Text
              color={Colors.tabsTextColor.activeTabColor}
              style={{marginLeft: Metrics.ratio(8)}}
              type="semi_bold">
              {search.length ? SEARCH.FRIENDS_ADD : SEARCH.RECENTLY_JOINED}
            </Text>
          )}
          <FlatList
            style={styles.grayColor}
            horizontal
            showsHorizontalScrollIndicator={false}
            data={recentFriends}
            renderItem={renderItem}
            keyExtractor={(item: ITimeLine, index) => item.id.toString()}
            extraData={recentFriends}
            ListEmptyComponent={() =>
              !loading && (
                <View style={styles.emptyContainer}>
                  <EmptyStateComponent text={SEARCH.NO_FRIENDS} />
                </View>
              )
            }
          />
          {recentFriends?.length > 0 && (
            <ButtonView
              onPress={() =>
                (navigation.navigate as (route: string, params: any) => void)(
                  Routes.ALL_FRIENDS,
                  {params: {query: search, scores: scores}},
                )
              }
              style={styles.horizontalFooter}>
              <Text
                size={'xxSmall'}
                alignSelf="center"
                justifyContent="center"
                color={Colors.black}>
                {SEARCH.VIEW_ALL}
              </Text>
            </ButtonView>
          )}
        </View>

        <View style={styles.container}>
          <FlashList
            contentContainerStyle={styles.verticalContainer}
            showsVerticalScrollIndicator={false}
            data={recentImprints}
            estimatedItemSize={447}
            ListHeaderComponent={() =>
              recentImprints.length ? (
                <Text
                  color={Colors.tabsTextColor.activeTabColor}
                  style={styles.infoText}
                  type="semi_bold">
                  {search.length
                    ? SEARCH.IMPRINT_INTEREST
                    : SEARCH.RECENT_IMPRINTS}
                </Text>
              ) : null
            }
            renderItem={({item}) => (
              <Animated.View>
                <TimeLineCard
                  handleReactionClicked={(userImprintInteraction, reaction) =>
                    handleReactionClicked(
                      reaction,
                      userImprintInteraction,
                      item as any,
                    )
                  }
                  handleMediaClicked={handleMediaClicked}
                  handleVerifyClick={handleVerifyClick}
                  handleMoreOptionClick={onMoreOptionClick}
                  onPressArticle={handleOnPressArticle}
                  onUserNameClick={onUserNameClick}
                  item={item as unknown as ITimeLine}
                  isGlobal={true}
                  user={user}
                />
              </Animated.View>
            )}
            keyExtractor={(item, index) => item.id.toString()}
          />
        </View>
        <RenderModal
          hideModal={hideModal}
          visible={visibleModal}
          onFollowPress={onFollowPress}
          userId={followId}
          onDeletePress={onDeletePress}
          isSelfUser={user.userInfo.userId}
          onBlockPress={onBlockPress}
          imprintData={imprintData}
        />
        {recentImprints.length === 0 && !loading && (
          <View style={styles.verticalEmptyContainer}>
            <EmptyStateComponent text={SEARCH.NO_IMPRINTS} />
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const mapStateToProps = (state: any) => ({
  user: state.user,
  followers: state.followers,
  recentFriends: state.search.recentFriendsForEmptySearch,
  recentImprints: state.search.recentImprintsForEmptySearch,
});
const actions = {
  getFreeTextSearchUsersRequest,
  getFreeTextSearchUserSuccess,
  getFreeTextImprintsRequest,
  imprintVerificationRequest,
  postReactionRequest,
  imprintShoutoutRequest,
  blockImprintRequest,
  imprintBookmarkRequest,
  requestFollowRequest,
  deleteImprintRequest,
  reportContentRequest,
};

export default connect(mapStateToProps, actions)(SearchUsers);
