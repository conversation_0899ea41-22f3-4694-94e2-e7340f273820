import React, {useEffect, useState} from 'react';
import {View, FlatList, Pressable} from 'react-native';
import {Avatar, CustomNavbar, Text} from '../../../components';
import {Colors, AppStyles} from '../../../theme';
import styles from './styles';
import {useNavigation} from '@react-navigation/native';
import {IFreeTextSearchPayload, SEARCH_USERS} from '../../../types';
import {connect} from 'react-redux';
import {SafeAreaView} from 'react-native-safe-area-context';
import {PLACEHOLDER_IMAGE} from '../../../constants';
import {SEARCH} from '../../../constants/StringConstants';
import Routes from '../../../constants/RouteConstants';
import {getFreeTextSearchUsersRequest} from '../../../actions/SearchActions';

interface ActionsProps {
  getFreeTextSearchUsersRequest: (
    payload: IFreeTextSearchPayload,
    callback: (res: any) => void,
  ) => void;
  route: {
    params: {
      query: string;
      scores: any;
    };
  };
}

const AllFriends: React.FC<ActionsProps> = ({
  getFreeTextSearchUsersRequest,
  route,
}) => {
  const [usersList, setUserList] = useState<SEARCH_USERS[]>([]);
  const [page, setPage] = useState<number>(1);
  const [isFetchingNextPage, setIsFetchingNextPage] = useState(false); // Prevents duplicate API calls

  const params = route.params.params;

  const navigation = useNavigation();

  const fetchUsers = (pageNumber: number) => {
    if (isFetchingNextPage) return; // Avoid multiple requests
    setIsFetchingNextPage(true);

    const payload: IFreeTextSearchPayload = {
      query: params.query,
      limit: 10,
      page: pageNumber,
      scores: params.scores,
    };

    getFreeTextSearchUsersRequest(payload, res => {
      if (res) {
        setUserList(prevList => {
          const newItems = res.items.filter(
            (newItem: {id: number}) =>
              !prevList.some(existingItem => existingItem.id === newItem.id),
          );

          return pageNumber === 1 ? newItems : [...prevList, ...newItems];
        });

        setIsFetchingNextPage(false);
      }
    });
  };

  useEffect(() => {
    fetchUsers(1);
  }, []);

  useEffect(() => {
    if (page > 1) fetchUsers(page);
  }, [page]);

  const navToUserProfile = (item: SEARCH_USERS) => {
    (navigation.navigate as (route: string, params: any) => void)(
      Routes.USER_PROFILE,
      {userName: item.userName},
    );
  };

  const RenderItem = ({item}: {item: SEARCH_USERS}) => {
    return (
      <Pressable
        style={styles.userinfoContainer}
        onPress={() => navToUserProfile(item)}>
        <Avatar
          image={item.avatarUrl ?? PLACEHOLDER_IMAGE}
          style={styles.imageContainer}
          imageStyle={styles.image}
        />
        <View style={AppStyles.alignStart}>
          <Text
            color={Colors.itemColors.titleColor}
            textAlign="center"
            style={AppStyles.mLeft10}
            type="bold"
            size={'buttonText'}>
            {item.displayName}
          </Text>
          <Text
            color={Colors.text.placeHolderTextColor}
            style={AppStyles.mLeft10}
            type="base"
            size={'xSmall'}>
            {item.userName ?? ''}
          </Text>
        </View>
      </Pressable>
    );
  };
  return (
    <SafeAreaView style={styles.container}>
      <CustomNavbar
        title={SEARCH.SEARCH}
        style={styles.navBarStyle}
        leftBtnPress={() => navigation.goBack()}
      />

      <View style={styles.container}>
        <FlatList
          contentContainerStyle={AppStyles.flexGrow}
          showsVerticalScrollIndicator={false}
          data={usersList}
          renderItem={RenderItem}
          keyExtractor={(item, index) => item.id + index.toString()}
          onMomentumScrollEnd={() => {
            if (!isFetchingNextPage && usersList.length >= 10) {
              setPage(prevPage => prevPage + 1);
            }
          }}
        />
      </View>
    </SafeAreaView>
  );
};

const mapStateToProps = () => ({});

const actions = {getFreeTextSearchUsersRequest};

export default connect(mapStateToProps, actions)(AllFriends);
