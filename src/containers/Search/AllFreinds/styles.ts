import {StyleSheet} from 'react-native';
import {Colors, Fonts, Metrics} from '../../../theme';

export default StyleSheet.create({
  container: {
    backgroundColor: Colors.gray,
    flex: 1,
  },
  userinfoContainer: {
    backgroundColor: Colors.white,
    paddingVertical: Metrics.baseMargin,
    marginVertical: Metrics.smallMargin,
    marginHorizontal: Metrics.baseMargin,
    borderRadius: Metrics.ratio(10),

    flexDirection: 'row',
    alignItems: 'center',
  },
  imageContainer: {
    marginLeft: Metrics.ratio(20),
    marginTop: Metrics.ratio(0),
    height: Metrics.ratio(45),
    width: Metrics.ratio(45),
  },
  image: {
    height: Metrics.ratio(45),
    width: Metrics.ratio(45),
  },
  navBarStyle: {backgroundColor: Colors.gray},
});
