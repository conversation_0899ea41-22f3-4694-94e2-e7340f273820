import {StyleSheet} from 'react-native';
import {Colors, Fonts, Metrics} from '../../theme';

export default StyleSheet.create({
  container: {
    backgroundColor: Colors.white,
  },

  pdfStyle: {
    height: Metrics.screenHeight * 0.8,
    width: Metrics.screenWidth - 40,
    borderRadius: 6,
  },
  pdfModal: {
    alignItems: 'center',
    justifyContent: 'center',
    width: Metrics.screenWidth,
    height: Metrics.screenHeight,
    backgroundColor: '#000000AB',
  },
  horizontalContainer: {
    padding: Metrics.ratio(16),
    backgroundColor: Colors.gray,
  },
  grayColor: {
    backgroundColor: Colors.gray,
  },
  emptyContainer: {
    backgroundColor: Colors.gray,

    width: Metrics.screenWidth,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: Metrics.ratio(20),
  },
  horizontalFooter: {
    backgroundColor: Colors.white,
    justifyContent: 'center',
    alignItems: 'center',
  },
  verticalContainer: {
    paddingHorizontal: Metrics.ratio(4),
    backgroundColor: Colors.background.home,
  },
  infoText: {
    marginLeft: Metrics.ratio(24),
    marginBottom: Metrics.ratio(20),
  },
  verticalEmptyContainer: {
    borderTopColor: Colors.lightGray1,
    borderTopWidth: 1,
    paddingHorizontal: Metrics.ratio(20),
    alignItems: 'center',
    backgroundColor: Colors.gray,
    height: 500,
  },
  marginTop: {marginTop: 8},
});
