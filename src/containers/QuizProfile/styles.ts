import {StyleSheet} from 'react-native';
import {Colors, Fonts, Metrics} from '../../theme';

export default StyleSheet.create({
  container: {
    height: Metrics.screenHeight * 0.6,
    backgroundColor: 'white',
    borderBottomLeftRadius: Metrics.ratio(21.5),
    borderBottomRightRadius: Metrics.ratio(21.5),
    paddingBottom: Metrics.ratio(16),
  },

  cover: {
    zIndex: -1,
    overflow: 'hidden',
    width: Metrics.ratio(Metrics.screenWidth - 48),
    height: Metrics.screenHeight * 0.6,
    alignSelf: 'center',
  },
  profileImage: {
    height: Metrics.ratio(140),
    width: Metrics.ratio(140),
    marginLeft: Metrics.ratio(24),
    marginTop: Metrics.ratio(-60),
    borderRadius: Metrics.ratio(100),
    borderWidth: Metrics.ratio(5),
    borderColor: 'white',
    justifyContent: 'center',
    alignItems: 'center',
  },
  plus: {
    width: Metrics.ratio(40),
    height: Metrics.ratio(40),
    backgroundColor: 'black',
    marginLeft: Metrics.ratio(120),
    borderRadius: Metrics.ratio(100),
    borderWidth: Metrics.ratio(4),
    borderColor: 'white',
    marginTop: Metrics.ratio(-50),
    alignItems: 'center',
    justifyContent: 'center',
  },
  name: {
    fontFamily: Fonts.type.semi_bold,
    fontSize: Metrics.ratio(26),
    color: Colors.text.titleColor,
    marginTop: Metrics.ratio(16),
    marginLeft: Metrics.ratio(24),
  },
  occupation: {
    fontFamily: Fonts.type.medium,
    fontSize: Metrics.ratio(16),
    color: Colors.textLight,
    marginLeft: Metrics.ratio(24),
  },
  title: {
    fontSize: Metrics.ratio(24),
    fontWeight: 'bold',
    marginBottom: Metrics.ratio(16),
  },
  image: {
    height: Metrics.ratio(130),
    width: Metrics.ratio(130),
    borderRadius: Metrics.ratio(100),
  },
  animatedView: {
    alignItems: 'center',
    flexDirection: 'row',
  },
  imageContainer: {
    flex: 1,
    alignItems: 'center',
    marginRight: Metrics.ratio(50),
  },
  textContainer: {
    width: Metrics.ratio(100),
    right: Metrics.ratio(5),
    position: 'absolute',
  },
  doneButton: {
    paddingHorizontal: Metrics.ratio(100),
    marginTop: Metrics.ratio(30),
  },
  titleStyle: {
    marginVertical: Metrics.ratio(20),
    marginHorizontal: Metrics.baseMargin,
  },
});
