// @flow
import {StyleSheet} from 'react-native';
import {Colors, Fonts, Metrics} from '../../theme';

export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.home,
    justifyContent: 'space-between',
  },
  topBarContainer: {
    paddingVertical: Metrics.ratio(10),
  },
  componentContainer: {
    bottom: 0,
  },
  modalParentContainer: {
    marginTop: 0,
  },
  modalContainer: {
    marginTop: Metrics.ratio(25),
    marginHorizontal: Metrics.ratio(40),
    flexDirection: 'row',
  },
  modalTextContainer: {
    paddingLeft: Metrics.ratio(20),
    flex: 1,
    justifyContent: 'center',
  },
  modalTextOne: {
    fontFamily: Fonts.type.semi_bold,
    fontSize: Metrics.ratio(13),
    color: Colors.text.titleColor,
  },
  modalTextTwo: {
    fontFamily: Fonts.type.medium,
    fontSize: Metrics.ratio(11),
    color: Colors.textLight,
  },
  modalOptionSeperator: {
    marginTop: Metrics.ratio(10),
    backgroundColor: Colors.lightGray,
    height: 0.5,
    width: Metrics.screenWidth - 40,
    alignSelf: 'center',
  },
  postTextContainer: {
    flex: 1,
    backgroundColor: 'white',
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: Metrics.ratio(20),
  },
  buttonContainer: {
    justifyContent: 'center',
    marginBottom: Metrics.ratio(10),
    flexDirection: 'row',
  },
});
