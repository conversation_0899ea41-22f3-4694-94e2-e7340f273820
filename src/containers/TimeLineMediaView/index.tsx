import React, {useCallback, useEffect, useState} from 'react';
import {<PERSON><PERSON>, Linking, SafeAreaView, View} from 'react-native';
import {useNavigation, useRoute} from '@react-navigation/native';
import {
  IBlockEntity,
  ImprintMedia,
  IRespondTag,
  ITimeLine,
  PostReactionPayload,
  UserImprintInteractions,
  UserState,
  VISITED_SCREEN,
} from '../../types';
import TopBar from '../../components/TimeLineMediaView/TopBar';
import UserDetails from '../../components/TimeLine/UserDetails';
import PostText from '../../components/TimeLine/PostText';
import {connect} from 'react-redux';
import Reactions from '../../components/TimeLine/Reactions';
import {ReactionType} from '../../types';
import {
  CHAT_LIST,
  COMMON,
  HOME,
  IMPRINT_MEDIA,
  IMPRINT_TIMELINE,
  TOAST_MESSAGES,
  TOAST_TYPE,
} from '../../constants/StringConstants';
import {
  postReactionRequest,
  imprintShoutoutRequest,
  imprintBookmarkRequest,
  requestFollowRequest,
  deleteImprintSuccess,
} from '../../actions/TimelineActions';
import RenderMedia from '../../components/TimeLineMediaView/MediaRender';
import styles from './styles';
import util from '../../util';
import {showAlertMsg, showToastMsg} from '../../components/Alert';
import RenderModal from '../../components/Common/MoreOption';
import {userRespondToTagMedia} from '../../actions/UserActions';
import AudioRecorderPlayer from 'react-native-audio-recorder-player';
import AudioPlayer from '../../components/CreateImprint/AudioPlayer';
import {AppStyles, Colors} from '../../theme';
import RelatedArticle from '../../components/TimeLine/RelatedArticle';
import {ScrollView} from 'react-native-gesture-handler';
import {blockImprintRequest} from '../../actions/FollowersActions';
import Routes from '../../constants/RouteConstants';

const audioRecorderPlayer = new AudioRecorderPlayer();

interface Props {
  user: UserState;
  postReactionRequest: (payload: any, callback: (res: any) => void) => void;
  imprintShoutoutRequest: (payload: any, callback: (res: any) => void) => void;
  imprintBookmarkRequest: (payload: any, callback: (res: any) => void) => void;
  requestFollowRequest: (payload: any, callback: (res: any) => void) => void;
  imprintVerificationRequest: (
    payload: any,
    callback: (res: any) => void,
  ) => void;
  userRespondToTagMedia: (
    payload: IRespondTag,
    callback: (res: any) => void,
  ) => void;
  deleteImprintSuccess: (response: (res: any) => void) => void;
  blockImprintRequest: (
    payload: IBlockEntity,
    callback: (res: any) => void,
  ) => void;
}

const TimeLineMediaView: React.FC<Props> = ({
  user,
  postReactionRequest,
  imprintBookmarkRequest,
  imprintVerificationRequest,
  requestFollowRequest,
  imprintShoutoutRequest,
  userRespondToTagMedia,
  deleteImprintSuccess,
  blockImprintRequest,
}) => {
  const navigation = useNavigation();
  const route = useRoute();
  const {
    item,
    type = '',
    imprintId = '',
    notificationId = '',
  } = route.params as {
    item: ITimeLine;
    type: string;
    imprintId: string;
    notificationId: string;
  };
  const isPersonal = user?.data?.userId === item?.userId;
  const [index, setIndex] = useState(0);
  const [imprintItem, setImprintItem] = useState<ITimeLine>(item);
  const [visibleModal, setVisibleModal] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [paylod, setPayload] = useState<PostReactionPayload>({});
  const [playingItemId, setPlayingItemId] = useState<string | null>(null);
  const [isPlaying, setIsPlaying] = useState<boolean>(false);
  const [currentPosition, setCurrentPosition] = useState(0); // Current position in the audio
  const [totalDuration, setTotalDuration] = useState(0); // Total duration of the audio
  const [waveData, setWaveData] = useState(
    Array(200).fill({
      value: Math.random() * 40 + 10,
      color: Colors.BlackButton,
    }),
  );
  const [activeAudioId, setActiveAudioId] = useState<string | null>(null);

  const [blockAlert, setBlockAlert] = useState<boolean>(false);
  const [flagAlert, setFlagAlert] = useState<boolean>(false);

  const articleItemsArray = item?.imprintMedias.filter(
    item => item.type === IMPRINT_MEDIA.ARTICLE,
  );

  const recentArticles = [...articleItemsArray].sort((a, b) => {
    const dateA = a.createdAt ? new Date(a.createdAt) : null;
    const dateB = b.createdAt ? new Date(b.createdAt) : null;
    if (dateA && dateB) {
      return dateB.getTime() - dateA.getTime();
    }
    return 0;
  });

  useEffect(() => {
    if (item) {
      setImprintItem(item);
    }
  }, [item]);

  const showTextInputModal = () => {
    setIsModalVisible(true);
  };

  useEffect(() => {
    if (Object.keys(paylod).length !== 0) {
      showTextInputModal();
    }
  }, [paylod]);

  const hideTextInputModal = () => {
    setIsModalVisible(false);
  };

  const onStartPlay = async (item, id) => {
    setIsPlaying(true);
    await audioRecorderPlayer.startPlayer(item);
    audioRecorderPlayer.setVolume(1.0);
    setActiveAudioId(id);
    audioRecorderPlayer.addPlayBackListener(e => {
      setTotalDuration(e.duration);
      setCurrentPosition(e.currentPosition);
      setWaveData(prevData => {
        return prevData.map(bar => {
          const randomHeight = Math.random() * 30 + 10;
          return {
            ...bar,
            value: randomHeight,
            color: '#4CAF50',
          };
        });
      });
      if (e.isFinished) {
        setWaveData(
          Array(200).fill({value: Math.random() * 40 + 10, color: '#C4C4C4'}),
        );
        onStopPlay();
        setCurrentPosition(0);
      }
      return;
    });
  };

  const onStopPlay = () => {
    setIsPlaying(false);
    setPlayingItemId(null);
    audioRecorderPlayer.stopPlayer();
    setWaveData(
      Array(200).fill({value: Math.random() * 40 + 10, color: '#C4C4C4'}),
    );
    setCurrentPosition(0);
  };

  const handleReactionClicked = useCallback(
    (
      reaction?: string,
      userImprintInteraction?: UserImprintInteractions,
      item?: ITimeLine,
    ) => {
      if (reaction === IMPRINT_TIMELINE.BOOKMARK) {
        const payload = {
          imprintId: item?.id,
          isBookmark: !userImprintInteraction?.isBookmarked,
          isGlobal: false,
        };
        imprintBookmarkRequest(payload, (res: any) => {
          if (res) {
            const index = item?.userImprintInteractions.findIndex(
              interaction => interaction.userId === user?.data?.userId,
            );
            if (index !== -1 && index !== undefined) {
              // Add a check for undefined index
              const tempItem = JSON.parse(JSON.stringify(item));
              tempItem.userImprintInteractions[index] = res;
              setImprintItem({...tempItem});
            } else {
              const tempItem = JSON.parse(JSON.stringify(item));
              tempItem.userImprintInteractions.push(res);
              setImprintItem({...tempItem});
            }
          }
        });
      } else {
        if (!item && !userImprintInteraction) return;
        const payload: PostReactionPayload = {
          isHide: false,
          imprintId: item?.id,
          userId: user?.data?.userId,
          reaction:
            userImprintInteraction?.reaction === reaction
              ? userImprintInteraction?.reaction
              : reaction === ReactionType.SHOUT_OUT ||
                reaction === IMPRINT_TIMELINE.FLAG ||
                reaction === IMPRINT_TIMELINE.BOOKMARK
              ? userImprintInteraction?.reaction
              : reaction,
          isRedFlagged:
            reaction === IMPRINT_TIMELINE.FLAG
              ? !userImprintInteraction?.isRedFlagged
              : userImprintInteraction?.isRedFlagged ?? false,
        };

        if (userImprintInteraction?.reaction === reaction) {
          payload.reaction = null;
        }

        payload.isGlobal = !isPersonal;

        if (item) {
          if (reaction === IMPRINT_TIMELINE.FLAG) {
            setPayload(payload);
            setBlockAlert(true);
          } else if (reaction === ReactionType.SHOUT_OUT) {
            util.shoutOutAlert(() => {
              const shoutoutPayload = {
                imprintId: item?.id,
              };
              imprintShoutoutRequest(shoutoutPayload, (res: any) => {});
            });
          } else {
            if (payload.isRedFlagged) {
              delete payload.isRedFlagged;
            }
            postReactionRequest(payload, (res: UserImprintInteractions) => {
              if (res) {
                const index = item.userImprintInteractions.findIndex(
                  interaction => interaction.userId === user?.data?.userId,
                );
                if (index !== -1) {
                  const tempItem = JSON.parse(JSON.stringify(item));
                  tempItem.userImprintInteractions[index] = res;
                  setImprintItem({...tempItem});
                } else {
                  const tempItem = JSON.parse(JSON.stringify(item));
                  tempItem.userImprintInteractions.push(res);
                  setImprintItem({...tempItem});
                }
              }
            });
          }
        }
      }
    },
    [user, postReactionRequest],
  );

  useEffect(() => {
    if (blockAlert) {
      showAlertMsg(
        IMPRINT_TIMELINE.BLOCK_HEADING,
        IMPRINT_TIMELINE.BLOCK_DETAIL,

        [
          {
            text: COMMON.YES,
            onPress: () => {
              setBlockAlert(false);
              setTimeout(() => {
                onBlockPress();
              }, 500);
              setFlagAlert(true);
            },
            style: 'default',
          },
          {
            text: COMMON.NO,
            onPress: () => {
              setBlockAlert(false);
              setTimeout(() => {
                setFlagAlert(true);
              }, 500);
            },
            style: 'cancel',
          },
        ],
      );
    }
  }, [blockAlert]);

  useEffect(() => {
    if (flagAlert) {
      showAlertMsg(
        IMPRINT_TIMELINE.REPORT_HEADING,
        IMPRINT_TIMELINE.REPORT_DETAIL,

        [
          {
            text: COMMON.YES,
            onPress: () => {
              setFlagAlert(false);
              onFlagImprint(item);
            },
            style: 'default',
          },
          {
            text: COMMON.NO,
            onPress: () => {
              setFlagAlert(false);
              setBlockAlert(false);
            },
            style: 'common',
          },
        ],
      );
    }
  }, [flagAlert]);

  const onFlagImprint = (item?: ITimeLine) => {
    let newPayload = paylod;
    newPayload.imprintId = item?.id;
    newPayload.redFlagReason = null;
    newPayload.isRedFlagged = true;
    newPayload.email = user.userInfo.email;
    postReactionRequest(newPayload, (res: UserImprintInteractions) => {
      if (res) {
        setPayload({});
        (navigation.navigate as (route: string, params: any) => void)(
          Routes.REPORT_SCREEN,
          {url: res.redirectUrl, title: CHAT_LIST.REPORT},
        );
      }
      setIsModalVisible(false);
    });
  };

  const onBlockPress = () => {
    const payload: IBlockEntity = {
      follower: item?.userId || '',
      status: true,
    };
    setVisibleModal(false);
    setTimeout(() => {
      blockImprintRequest(payload, res => {
        if (res) {
          const obj = {
            userId: item?.userId || '',
          };
          /* used will be fixed after backend dependency */
          //  deleteImprintSuccess(obj);
          setVisibleModal(false);
          showToastMsg(
            TOAST_MESSAGES.BLOCK_IMPRINT_MESSAGE,
            TOAST_TYPE.SUCCESS,
          );
        }
      });
    }, 500);

    setVisibleModal(false);
  };

  const onMorePress = () => {
    setVisibleModal(true);
  };

  const onClosePress = () => {
    navigation.goBack();
  };

  const onPressLeft = () => {
    if (item.imprintMedias.length - 1 > index) {
      setIndex(index + 1);
    } else {
      setIndex(0);
    }
  };

  const onPressRight = () => {
    if (index > 0) {
      setIndex(index - 1);
    } else {
      setIndex(item.imprintMedias.length - 1);
    }
  };

  const hideModal = () => {
    setVisibleModal(!visibleModal);
  };

  const onFollowPress = (userId: string) => {
    const payload = {
      to: userId,
      isGlobal: imprintItem.isGlobal,
    };
    requestFollowRequest(payload, (res: any) => {
      if (res) {
        const tempItem = JSON.parse(JSON.stringify(imprintItem));
        tempItem.followerRequestSent = true;
        setImprintItem({...tempItem});
        setIsModalVisible(false);
      }
    });
  };

  const onPressArticle = (item: ImprintMedia) => {
    Alert.alert(HOME.DISCLAIMER, HOME.DISCLAIMER_TEXT, [
      {
        text: 'Cancel',
        style: 'cancel',
      },
      {
        text: 'Redirect',
        onPress: () => Linking.openURL(item.url ?? ''),
      },
    ]);
  };

  const tagRespond = (approve: boolean) => {
    const payload: IRespondTag = {
      imprintId: imprintId,
      approve: approve,
      notificationId: notificationId,
    };

    userRespondToTagMedia(payload, (res: any) => {
      if (res) {
        setTimeout(() => {
          hideModal();
          navigation.goBack();
        }, 500);
      }
    });
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.topBarContainer}>
        <TopBar
          onMorePress={onMorePress}
          onClosePress={onClosePress}
          user={user}
          imprintItem={imprintItem}
        />
      </View>
      {type === 'notification' ? (
        <View style={{flex: 1}}>
          {imprintItem?.imprintMedias &&
          imprintItem.imprintMedias.length > 0 ? (
            imprintItem?.imprintMedias[0]?.type === IMPRINT_MEDIA.AUDIO ? (
              <View style={styles.postTextContainer}>
                <PostText text={item.description} isMediaView={true} />
                <AudioPlayer
                  onStartPlay={() =>
                    onStartPlay(imprintItem?.imprintMedias[0].url, item.id)
                  }
                  waveData={waveData}
                  recordedFilePath={imprintItem?.imprintMedias[0].url}
                  isPlying={isPlaying}
                  onStopPlay={onStopPlay}
                  totalDuration={totalDuration}
                  currentPosition={currentPosition}
                />
              </View>
            ) : imprintItem?.imprintMedias[0]?.type ===
              IMPRINT_MEDIA.ARTICLE ? (
              <View style={{justifyContent: 'center'}}>
                <ScrollView showsVerticalScrollIndicator={false}>
                  <PostText text={item.description} isMediaView={true} />

                  <RelatedArticle
                    articleData={articleItemsArray}
                    onPressArticle={item => onPressArticle(item)}
                  />
                  <RelatedArticle
                    onPressArticle={item => onPressArticle(item)}
                    articleData={recentArticles}
                    title={IMPRINT_TIMELINE.RECENT_ARTICLES}
                  />
                </ScrollView>
              </View>
            ) : (
              <>
                <PostText text={item.description} isMediaView={true} />

                <RenderMedia
                  item={imprintItem.imprintMedias[index]}
                  onPressLeft={onPressLeft}
                  onPressRight={onPressRight}
                  showHandle={
                    imprintItem?.imprintMedias?.length == 1 ? false : true
                  }
                />
              </>
            )
          ) : (
            imprintItem?.imprintMedias.length === 0 && (
              <View style={styles.postTextContainer}>
                <PostText text={item.description} isMediaView={true} />
              </View>
            )
          )}
        </View>
      ) : (
        <>
          {type !== VISITED_SCREEN.NOTIFICATION &&
          imprintItem?.imprintMedias &&
          imprintItem?.imprintMedias.length > 0 ? (
            <>
              <PostText text={item.description} isMediaView={true} />

              <RenderMedia
                item={imprintItem.imprintMedias[index]}
                onPressLeft={onPressLeft}
                onPressRight={onPressRight}
                showHandle={
                  imprintItem?.imprintMedias?.length == 1 ? false : true
                }
              />
            </>
          ) : (
            <View style={styles.postTextContainer}>
              <PostText text={item.description} isMediaView={true} />
            </View>
          )}
        </>
      )}

      {item.id !== null && (
        <View style={styles.componentContainer}>
          <View style={AppStyles.flexRow}>
            <Reactions
              userImprintInteractions={imprintItem.userImprintInteractions}
              handleClick={(userImprintInteraction, reaction, imprint) =>
                handleReactionClicked(reaction, userImprintInteraction, imprint)
              }
              imprintData={imprintItem}
              isMediaView={true}
            />
          </View>
          <UserDetails
            imprintData={item}
            handleVerifyClick={() => {}}
            isMediaView={true}
            user={user}
          />
        </View>
      )}

      <RenderModal
        type={type}
        hideModal={hideModal}
        visible={visibleModal}
        onFollowPress={onFollowPress}
        userId={imprintItem.userId}
        onAccept={() => tagRespond(true)}
        onDecline={() => tagRespond(false)}
      />
    </SafeAreaView>
  );
};

const mapStateToProps = (state: any) => ({
  user: state.user,
});

export default connect(mapStateToProps, {
  postReactionRequest,
  imprintBookmarkRequest,
  imprintShoutoutRequest,
  requestFollowRequest,
  userRespondToTagMedia,
  deleteImprintSuccess,
  blockImprintRequest,
})(TimeLineMediaView);
