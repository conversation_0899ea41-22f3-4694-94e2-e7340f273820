import React, {useCallback, useEffect, useMemo, useState} from 'react';
import {View, FlatList, ActivityIndicator} from 'react-native';
import {
  SearchBar,
  EmptyStateComponent,
  FriendItem,
  FriendTabs,
  BottomModal,
  Text,
  Loader,
} from '../../components';
import {AppStyles, Colors} from '../../theme';
import styles from './styles';
import {
  getUserFriendsRequest,
  respondToFollowers,
  getFriendsRequestSender,
  unFollowRequest,
  blockImprintRequest,
  blockUserRequest,
  blockMessageRequest,
} from '../../actions/FollowersActions';
import {
  FOLLOWERS,
  FollowerState,
  IBlockEntity,
  IRespondFollower,
  IUnFollow,
  RootState,
  TABS_ENUMS,
  UserState,
} from '../../types';
import {connect} from 'react-redux';
import {
  FRIENDS,
  TOAST_MESSAGES,
  TOAST_TYPE,
} from '../../constants/StringConstants';
import _ from 'lodash';
import {
  blockUserListingRequest,
  friendsSuggestionsRequest,
} from '../../actions/UserActions';
import {requestFollowRequest} from '../../actions/TimelineActions';
import {showToastMsg} from '../../components/Alert';
import FriendsSuggestionsItem from '../../components/SuggestedItem';

interface ActionsProps {
  respondToFollowers: (
    payload: IRespondFollower,
    callback: (res: any) => void,
  ) => void;
  getFriendsRequestSender: (callback: (res: any) => void) => void;
  blockUserListingRequest: (callback: (res: any) => void) => void;

  getUserFriendsRequest: (callback: (res: any) => void) => void;
  unFollowRequest: (payload: IUnFollow, callback: (res: any) => void) => void;
  blockImprintRequest: (
    payload: IBlockEntity,
    callback: (res: any) => void,
  ) => void;

  blockUserRequest: (
    payload: IBlockEntity,
    callback: (res: any) => void,
  ) => void;

  blockMessageRequest: (
    payload: IBlockEntity,
    callback: (res: any) => void,
  ) => void;
  requestFollowRequest: (payload: any, callback: (res: any) => void) => void;

  friendsSuggestionsRequest: (callback: (res: any) => void) => void;

  user: UserState;
  followers: FollowerState;
  onItemPress?: (item: any) => void;
}
const FriendHub: React.FC<ActionsProps> = ({
  getUserFriendsRequest,
  getFriendsRequestSender,
  respondToFollowers,
  unFollowRequest,
  blockImprintRequest,
  blockUserRequest,
  blockMessageRequest,
  blockUserListingRequest,
  friendsSuggestionsRequest,
  requestFollowRequest,

  onItemPress,
  followers,
}) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [selectedTab, setSelectedTab] = useState<
    | TABS_ENUMS.ALL
    | TABS_ENUMS.REQUESTED
    | TABS_ENUMS.BLOCKED
    | TABS_ENUMS.SUGGESTED
  >(TABS_ENUMS.ALL);

  const [renderTab, setRenderTab] = useState<typeof selectedTab>(
    TABS_ENUMS.ALL,
  );

  const [visibleModal, setVisibleModal] = useState<boolean>(false);
  const [selectedItem, setSelectedItem] = useState<FOLLOWERS>();
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredData, setFilteredData] = useState<FOLLOWERS[]>([]);
  const [originalFriendsData, setOriginalFriendsData] = useState<FOLLOWERS[]>(
    [],
  );
  const [originalRequestedUsersList, setOriginalRequestedUsersList] = useState<
    FOLLOWERS[]
  >([]);
  const [confirmLoadingState, setConfirmLoadingState] = useState<{
    [itemId: string]: boolean;
  }>({});
  const [deleteLoadingState, setDeleteLoadingState] = useState<{
    [itemId: string]: boolean;
  }>({});

  const [requestedUsersList, setRequestedUsersList] = useState<FOLLOWERS[]>([]);

  const [blockedUsersList, setBlockedUsersList] = useState<FOLLOWERS[]>([]);

  const [originalBlockedUsersList, setOriginalBlockedUsersList] = useState<
    FOLLOWERS[]
  >([]);

  const [suggestedUsersList, setSuggestedUsersList] = useState([]);

  const [followLoader, setFollowLoader] = useState(false);

  const hideModal = () => {
    setVisibleModal(false);
  };

  const getFriends = () => {
    setLoading(true);
    getUserFriendsRequest(res => {
      if (res) {
        setFilteredData(res);
        setLoading(false);
      }
      setLoading(false);
    });
  };

  const getFollowers = () => {
    getFriendsRequestSender(res => {
      if (res) {
        setRequestedUsersList(res);
      }
    });
  };

  const getBlockUsers = () => {
    blockUserListingRequest(res => {
      if (res) {
        setBlockedUsersList(res);
        setOriginalBlockedUsersList(res);
      }
    });
  };

  useEffect(() => {
    getFollowers();
    getBlockUsers();
    getFriends();
    getSuggestedUsers();
  }, []);

  useEffect(() => {
    setOriginalFriendsData(_.cloneDeep(followers.data));
    setOriginalRequestedUsersList(_.cloneDeep(followers.followRequestData));
  }, [followers.data, followers.followRequestData]);

  const pullToRefresh = () => {
    getFollowers();
    getFriends();
    getBlockUsers();
  };

  const onItemDetailPress = (item: FOLLOWERS) => {
    setSelectedItem(item);
    if (selectedTab === TABS_ENUMS.BLOCKED) {
      setSelectedItem({...item, isUserBlocked: true});
    }
    setVisibleModal(true);
  };

  const handleSearch = (text: string) => {
    setSearchQuery(text);
    const friendsData = _.cloneDeep(followers.data);
    const followersData = _.cloneDeep(followers.followRequestData);

    switch (selectedTab) {
      case TABS_ENUMS.ALL: {
        const searchData = friendsData.filter(item =>
          item.displayName.toLowerCase().includes(text.toLowerCase()),
        );
        setFilteredData(searchData);
        break;
      }
      case TABS_ENUMS.REQUESTED: {
        const searchData = followersData.filter(item =>
          item.fromUser.displayName?.toLowerCase().includes(text.toLowerCase()),
        );
        setRequestedUsersList(searchData);
        break;
      }
      case TABS_ENUMS.BLOCKED: {
        if (text === '') {
          setBlockedUsersList(originalBlockedUsersList);
        } else {
          const searchData = originalBlockedUsersList.filter(item =>
            item.displayName.toLowerCase().includes(text.toLowerCase()),
          );
          setBlockedUsersList(searchData);
        }
        break;
      }

      default:
        break;
    }
  };

  const getSuggestedUsers = (append = false) => {
    friendsSuggestionsRequest((res: any) => {
      if (res) {
        if (append) {
          setSuggestedUsersList(res);
        } else {
          setSuggestedUsersList(res);
        }
      }
    });
  };

  const onConfirmFriendPress = (item: any) => {
    setSelectedItem(item.id);

    const payload: IRespondFollower = {
      isAccepted: true,
      requestID: item.id,
    };
    setConfirmLoadingState(prevState => ({
      ...prevState,
      [item.id]: true,
    }));
    respondToFollowers(payload, (res: any) => {
      if (res) {
        getFollowers();
        getFriends();
        setConfirmLoadingState(prevState => ({
          ...prevState,
          [item.id]: false,
        }));
      }
      setConfirmLoadingState(prevState => ({
        ...prevState,
        [item.id]: false,
      }));
    });
  };

  const onDeletePress = (item: any) => {
    const payload: IRespondFollower = {
      isAccepted: false,
      requestID: item.id,
    };
    setDeleteLoadingState(prevState => ({
      ...prevState,
      [item.id]: true,
    }));
    respondToFollowers(payload, (res: any) => {
      if (res) {
        getFollowers();
        getFriends();
        setDeleteLoadingState(prevState => ({
          ...prevState,
          [item.id]: false,
        }));
      }
      setDeleteLoadingState(prevState => ({
        ...prevState,
        [item.id]: false,
      }));
    });
  };

  const removeFriend = () => {
    const payload: IUnFollow = {
      follower: selectedItem?.id,
    };
    unFollowRequest(payload, () => {
      getFriends();
      setVisibleModal(false);
    });
  };

  const blockImprint = () => {
    const payload: IBlockEntity = {
      follower: selectedItem?.id,
      status: !selectedItem?.blockedImprints ? true : false,
    };
    setVisibleModal(false);
    setTimeout(() => {
      setLoading(true);
      blockImprintRequest(payload, res => {
        if (res) {
          showToastMsg(
            TOAST_MESSAGES.USER_ACTIONS_SUCCESS,
            TOAST_TYPE.SUCCESS,
            2000,
            'top',
          );
          setTimeout(() => {
            setFilteredData(prevData =>
              prevData.map(item =>
                item.id === selectedItem?.id
                  ? {...item, blockedImprints: payload.status}
                  : item,
              ),
            );
          }, 200);
          //  getFriends();
          setVisibleModal(false);
          setLoading(false);
        }
      });
    }, 500);

    setLoading(false);
    setVisibleModal(false);
  };

  const onBlockUserPress = () => {
    const payload: IBlockEntity = {
      follower: selectedItem?.id,
      status:
        selectedTab === TABS_ENUMS.BLOCKED
          ? false
          : !selectedItem?.isUserBlocked,
    };
    setVisibleModal(false);
    setTimeout(() => {
      setLoading(true);
      blockUserRequest(payload, res => {
        if (res) {
          showToastMsg(
            TOAST_MESSAGES.USER_ACTIONS_SUCCESS,
            TOAST_TYPE.SUCCESS,
            2000,
            'top',
          );
          setTimeout(() => {
            setFilteredData(prevData =>
              prevData.map(item =>
                item.id === selectedItem?.id
                  ? {...item, isUserBlocked: payload.status}
                  : item,
              ),
            );
          }, 1000);

          getBlockUsers();
          getFriends();
          setVisibleModal(false);
          setLoading(false);
        }
      });
    }, 500);

    setLoading(false);
    setVisibleModal(false);
  };

  const onMessagePress = () => {
    const payload: IBlockEntity = {
      follower: selectedItem?.id,
      status: !selectedItem?.blockedMessages ? true : false,
    };
    setVisibleModal(false);
    setTimeout(() => {
      setLoading(true);
      blockMessageRequest(payload, (res: any) => {
        if (res) {
          getFriends();
          setVisibleModal(false);
          setLoading(false);
        }
      });
    }, 500);

    setLoading(false);
    setVisibleModal(false);
  };

  const RenderItem = ({item}: {item: FOLLOWERS | any}) => {
    const tabsWithMoreOptions = [
      TABS_ENUMS.ALL,
      TABS_ENUMS.BLOCKED,
      TABS_ENUMS.SUGGESTED,
    ];
    return (
      <FriendItem
        item={
          selectedTab === TABS_ENUMS.ALL ||
          selectedTab === TABS_ENUMS.BLOCKED ||
          selectedTab === TABS_ENUMS.SUGGESTED
            ? item
            : item.fromUser
        }
        isMoreOptionAvailable={tabsWithMoreOptions.includes(selectedTab)}
        isButton={
          (selectedTab !== TABS_ENUMS.ALL &&
            selectedTab !== TABS_ENUMS.BLOCKED) ||
          false
        }
        onItemPress={() => onItemPress && onItemPress(item)}
        moreOptionsPress={() => onItemDetailPress(item)}
        onConfirmPress={() =>
          selectedTab === TABS_ENUMS.REQUESTED
            ? onConfirmFriendPress(item)
            : undefined
        }
        onDeletePress={() => onDeletePress(item)}
        confirmLoading={confirmLoadingState[item.id] || false}
        deleteLoading={deleteLoadingState[item.id] || false}
      />
    );
  };

  const renderSuggestedItem = ({item}: {item: FOLLOWERS | any}) => {
    return (
      <FriendsSuggestionsItem
        item={item}
        navigateToProfile={onItemPress ? () => onItemPress(item) : undefined}
        onItemPress={() => {
          setFollowLoader(true);
          const payload = {
            to: item.id,
          };
          requestFollowRequest(payload, (res: any) => {
            if (res) {
              showToastMsg(
                TOAST_MESSAGES.FOLLOW_REQUEST_SENT,
                TOAST_TYPE.SUCCESS,
                2000,
                'top',
              );
              getSuggestedUsers(true);
              setFollowLoader(false);
            }
            setFollowLoader(false);
          });
        }}
      />
    );
  };

  const handleTabChange = (
    tab:
      | TABS_ENUMS.ALL
      | TABS_ENUMS.REQUESTED
      | TABS_ENUMS.BLOCKED
      | TABS_ENUMS.SUGGESTED,
  ) => {
    setSelectedTab(tab);
    setSearchQuery('');
    pullToRefresh();
    setLoading(true);

    setTimeout(() => {
      switch (tab) {
        case TABS_ENUMS.ALL:
          setFilteredData(originalFriendsData);

          break;
        case TABS_ENUMS.REQUESTED:
          setRequestedUsersList(originalRequestedUsersList);

          break;
        case TABS_ENUMS.BLOCKED:
          setBlockedUsersList(blockedUsersList);

          break;
        case TABS_ENUMS.SUGGESTED:
          break;
      }
      setRenderTab(tab);
      setLoading(false);
    }, 300);
  };

  const filteredSuggestedList = useMemo(() => {
    if (selectedTab !== TABS_ENUMS.SUGGESTED) return [];
    if (!searchQuery) return suggestedUsersList;

    return suggestedUsersList.filter((item: FOLLOWERS) =>
      item.displayName?.toLowerCase().includes(searchQuery.toLowerCase()),
    );
  }, [selectedTab, searchQuery, suggestedUsersList]);

  return (
    <View style={styles.container}>
      <FriendTabs
        onFriendsListPress={() => handleTabChange(TABS_ENUMS.ALL)}
        onRequestedFriendsPress={() => handleTabChange(TABS_ENUMS.REQUESTED)}
        onBlockedFriendsPress={() => handleTabChange(TABS_ENUMS.BLOCKED)}
        onSuggestedFriendsPress={() => handleTabChange(TABS_ENUMS.SUGGESTED)}
        selectedTab={selectedTab}
        friendsList={filteredData}
        requestedList={requestedUsersList}
        blockedList={blockedUsersList}
      />
      <View style={styles.container}>
        <SearchBar
          autoFocus={false}
          containerStyle={AppStyles.mTop10}
          onSearchText={handleSearch}
          value={searchQuery}
        />
        {selectedTab !== TABS_ENUMS.SUGGESTED && (
          <Text
            color={Colors.black}
            type="semi_bold"
            size={'normal'}
            style={styles.heading}>
            {selectedTab === TABS_ENUMS.ALL
              ? FRIENDS.PERSONAL_FRIENDS
              : selectedTab === TABS_ENUMS.BLOCKED
              ? FRIENDS.BLOCK_USER
              : FRIENDS.REQUEST}
          </Text>
        )}
        {selectedTab === TABS_ENUMS.SUGGESTED && loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" />
          </View>
        ) : (
          <FlatList
            key={`flatlist-${renderTab}-${
              selectedTab === TABS_ENUMS.SUGGESTED ? 'grid' : 'list'
            }`}
            scrollEnabled={false}
            ListEmptyComponent={
              !loading ? (
                <EmptyStateComponent text={FRIENDS.EMPTY_STATE} />
              ) : null
            }
            style={AppStyles.mTop10}
            refreshing={false}
            onRefresh={pullToRefresh}
            contentContainerStyle={[
              AppStyles.flexGrow,
              selectedTab === TABS_ENUMS.SUGGESTED && AppStyles.alignSelfCenter,
            ]}
            showsVerticalScrollIndicator={false}
            data={
              selectedTab === TABS_ENUMS.ALL
                ? filteredData
                : selectedTab === TABS_ENUMS.BLOCKED
                ? blockedUsersList
                : selectedTab === TABS_ENUMS.SUGGESTED
                ? filteredSuggestedList
                : requestedUsersList
            }
            renderItem={
              selectedTab === TABS_ENUMS.SUGGESTED
                ? renderSuggestedItem
                : RenderItem
            }
            numColumns={selectedTab === TABS_ENUMS.SUGGESTED ? 2 : 1}
            keyExtractor={item => item.id.toString()}
          />
        )}

        <Loader loading={followLoader || loading} />
      </View>
      <BottomModal
        isBlockUser={selectedTab === TABS_ENUMS.BLOCKED}
        hideModal={() => hideModal()}
        onMessagePress={() => onMessagePress()}
        onRemovePress={() => removeFriend()}
        onBlockPress={() => blockImprint()}
        visibleModal={visibleModal}
        selectedItem={selectedItem}
        onBlockUserPress={onBlockUserPress}
      />
    </View>
  );
};

const mapStateToProps = (state: RootState) => ({
  user: state.user,
  followers: state.followers,
});

const actions = {
  getUserFriendsRequest,
  getFriendsRequestSender,
  respondToFollowers,
  blockImprintRequest,
  unFollowRequest,
  blockUserRequest,
  blockMessageRequest,
  blockUserListingRequest,
  friendsSuggestionsRequest,
  requestFollowRequest,
};

export default connect(mapStateToProps, actions)(FriendHub);
