import {StyleSheet} from 'react-native';
import {Colors, Fonts, Metrics} from '../../theme';

export default StyleSheet.create({
  container: {
    marginVertical: Metrics.baseMargin,
    flex: 1,
    backgroundColor: Colors.background.home,
  },
  userinfoContainer: {
    backgroundColor: Colors.white,
    paddingVertical: Metrics.baseMargin,
    marginVertical: Metrics.smallMargin,
    marginHorizontal: Metrics.baseMargin,
    borderRadius: Metrics.ratio(10),
  },
  imageContainer: {
    marginLeft: Metrics.ratio(20),
    marginTop: Metrics.ratio(0),
    height: Metrics.ratio(45),
    width: Metrics.ratio(45),
  },
  image: {
    height: Metrics.ratio(45),
    width: Metrics.ratio(45),
  },
  navBarStyle: {backgroundColor: Colors.gray},

  button: {
    width: Metrics.ratio(100),
    backgroundColor: Colors.BlackButton,
    borderColor: Colors.black,
    borderWidth: 1,
    borderRadius: Metrics.ratio(6),
    marginHorizontal: 0,
  },
  deleteButton: {
    backgroundColor: Colors.gray2,
    marginHorizontal: Metrics.ratio(0),
    width: Metrics.ratio(100),
  },
  heading: {
    marginHorizontal: Metrics.ratio(20),
    marginTop: Metrics.baseMargin,
  },
  loadingContainer: {flex: 1, justifyContent: 'center', alignItems: 'center'},
});
