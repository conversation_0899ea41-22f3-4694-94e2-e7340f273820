import {StyleSheet} from 'react-native';
import {Colors, Metrics} from '../../theme';
import {getStatusBarHeight} from 'react-native-iphone-x-helper';

export default StyleSheet.create({
  container: {
    paddingVertical: Metrics.baseMargin,
    flex: 1,
    backgroundColor: Colors.background.home,
  },
  statusBar: {
    backgroundColor: Colors.white,
    height: getStatusBarHeight() + 30,
  },
  statusBarWithoutNotch: {
    backgroundColor: Colors.white,
    height: Metrics.ratio(30),
  },
  rudiChatContainer: {
    marginTop: Metrics.ratio(20),
    marginHorizontal: Metrics.ratio(24),
    height: Metrics.ratio(60),
    width: Metrics.ratio(60),
    borderRadius: Metrics.ratio(30),
    backgroundColor: Colors.black,
    justifyContent: 'center',
    alignItems: 'center',
  },
  alignSelf: {
    alignSelf: 'flex-start',
  },
});
