import {StyleSheet} from 'react-native';
import {Colors, Fonts, Metrics} from '../../theme';
import {getStatusBarHeight} from 'react-native-iphone-x-helper';

export default StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: 0,
    backgroundColor: Colors.background.home,
  },
  statusBar: {
    backgroundColor: Colors.white,
    height: getStatusBarHeight() + 30,
  },
  statusBarWithoutNotch: {
    backgroundColor: Colors.white,
    height: Metrics.ratio(30),
  },
  flastList: {
    paddingBottom: Metrics.ratio(40),
  },
  header: {
    paddingBottom: Metrics.ratio(20),
  },
  modalParentContainer: {
    marginTop: 0,
  },
  modalContainer: {
    marginTop: Metrics.ratio(25),
    marginHorizontal: Metrics.ratio(40),
    flexDirection: 'row',
  },
  modalTextContainer: {
    paddingLeft: Metrics.ratio(20),
    flex: 1,
    justifyContent: 'center',
  },
  modalTextOne: {
    fontFamily: Fonts.type.semi_bold,
    fontSize: Metrics.ratio(13),
    color: Colors.text.titleColor,
  },
  modalTextTwo: {
    fontFamily: Fonts.type.medium,
    fontSize: Metrics.ratio(11),
    color: Colors.textLight,
  },
  modalOptionSeperator: {
    marginTop: Metrics.ratio(10),
    backgroundColor: Colors.lightGray,
    height: 0.5,
    width: Metrics.screenWidth - 40,
    alignSelf: 'center',
  },
  pdfStyle: {
    height: Metrics.screenHeight * 0.8,
    width: Metrics.screenWidth - 40,
    borderRadius: 6,
  },
  leaveImprint: {
    marginTop: Metrics.ratio(2),
    height: Metrics.ratio(70),
    backgroundColor: 'white',
  },
  pdfModal: {
    alignItems: 'center',
    justifyContent: 'center',
    width: Metrics.screenWidth,
    height: Metrics.screenHeight,
    backgroundColor: '#000000AB',
  },
  pdf: {
    height: Metrics.screenHeight * 0.8,
    width: Metrics.screenWidth - 40,
    borderRadius: 6,
  },
  white: {
    flex: 1,
    backgroundColor: Colors.white,
    borderBottomColor: Colors.gray,
    width: Metrics.screenWidth,
    zIndex: 9999,
  },
  border: {
    height: Metrics.ratio(24),
    backgroundColor: Colors.gray,
  },
  bottomBorder: {
    marginHorizontal: Metrics.ratio(24),
    height: Metrics.ratio(1),
    backgroundColor: Colors.gray,
  },
  editImage: {
    height: Metrics.screenHeight,
    width: Metrics.screenWidth,
  },
  minHeight: {minHeight: 100},
});
