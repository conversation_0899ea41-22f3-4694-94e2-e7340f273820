import React, {useCallback, useEffect, useMemo, useRef, useState} from 'react';
import {
  View,
  Animated,
  Linking,
  Modal,
  TouchableOpacity,
  Alert,
  RefreshControl,
} from 'react-native';
import styles from './styles';
import {
  CustomNavbar,
  LeaveImprint,
  Loader,
  ReportModal,
  ViolationModal,
} from '../../components';
import {useIsFocused, useNavigation, useRoute} from '@react-navigation/native';
import UserCoverDetails from '../../components/UserCoverDetails';
import {connect} from 'react-redux';
import {
  CompleteProfile,
  FOLLOWERS,
  IAboutCardHeader,
  IBlockEntity,
  IDeleteImprintPayload,
  IMedia_CheckIn,
  IProfileOptions,
  IReportContentPayload,
  ITagMedia,
  ITimeLine,
  IUnFollow,
  ImprintMedia,
  PostReactionPayload,
  REPORT_ENUMS,
  ReactionType,
  Score,
  SubscriptionStatus,
  UserImprintInteractions,
  UserInfo,
  UserState,
} from '../../types';
import Routes from '../../constants/RouteConstants';
import {
  userTimelineRequest,
  postReactionRequest,
  imprintShoutoutRequest,
  imprintVerificationRequest,
  getUserTimeLineRequest,
  getFilteredTimeLineRequest,
  imprintBookmarkRequest,
  requestFollowRequest,
  deleteImprintSuccess,
  deleteImprintRequest,
  resetTimeLine,
  reportContentRequest,
} from '../../actions/TimelineActions';
import {
  editImageRequest,
  completeProfileRequest,
  getOtherUserDetailsRequest,
  profilePhotoModerationRequest,
} from '../../actions/ProfileActions';
import {userInfoRequest} from '../../actions/UserActions';
import {AnimatedFlashList} from '@shopify/flash-list';
import TimeLineCard from '../../components/TimeLine/TimeLineCard';
import {
  ABOUT,
  FRIENDS,
  IMPRINT_MEDIA,
  IMPRINT_TIMELINE,
  KIN_STATUS,
  PROFILE_OPTIONS_LIST,
  REPORT_CONTENT,
  TOAST_MESSAGES,
  TOAST_TYPE,
} from '../../constants/StringConstants';
import util, {isUserSubscribed} from '../../util';
import {showToastMsg} from '../../components/Alert';
import {PROFILE_OPTIONS, TOAST_VISIBILITY_TIMEOUT} from '../../constants';
import ProfileOptions from '../../components/ProfileOptions';
import MoreBottomModal from '../../components/MoreBottomModal';
import FriendsHub from '../FriendsHub';
import RenderModal from '../../components/Common/MoreOption';
import UploadImage from '../../components/ProfileSetup/UploadImage';
import ImageCropPicker from 'react-native-image-crop-picker';
import AboutCard from '../../components/About';
import {
  generateAboutData,
  generateAboutDataForOtherUsers,
} from '../../helpers/ProfileHelper';

import MultipleMediaList from '../MultipleMediaList';
import CheckInTab from '../CheckInTab';
import _ from 'lodash';
import {AppStyles, Metrics} from '../../theme';
import NextOfKin from '../../components/NextOfKin';
import FileViewer from '../../components/FileViewer';
import {getValuePortfolioRequest} from '../../actions/AnalyticsActions';
import {
  blockImprintRequest,
  blockUserRequest,
  unFollowRequest,
} from '../../actions/FollowersActions';
import ReportBadgeModal from '../../components/ReportContent/ReportBadgeModal';
import {THREE_DOT_VERTICAL} from '../../constants/AssetSVGConstants';
import ProfileActionSheetComponent, {
  ProfileActonSheet,
} from '../../components/ProfileActonSheet';
import {ActionSheetProvider} from '@expo/react-native-action-sheet';

interface UserProfileProps {
  reportContentRequest: (
    payload: IReportContentPayload,
    callback: (res: any) => void,
  ) => void;
  user: UserState;
  userTimelineRequest: (payload: any, callback: (res: any) => void) => void;
  unFollowRequest: (payload: IUnFollow, callback: (res: any) => void) => void;

  blockUserRequest: (
    payload: IBlockEntity,
    callback: (res: any) => void,
  ) => void;
  getUserTimeLineRequest: (payload: any, callback: (res: any) => void) => void;
  postReactionRequest: (payload: any, callback: (res: any) => void) => void;
  imprintShoutoutRequest: (payload: any, callback: (res: any) => void) => void;
  imprintBookmarkRequest: (payload: any, callback: (res: any) => void) => void;
  requestFollowRequest: (payload: any, callback: (res: any) => void) => void;
  imprintVerificationRequest: (
    payload: any,
    callback: (res: any) => void,
  ) => void;
  editImageRequest: (payload: any, callback: (res: any) => void) => void;
  completeProfileRequest: (callback: (res: any) => void) => void;
  deleteImprintSuccess: (response: (res: any) => void) => void;

  getOtherUserDetailsRequest: (
    payload: any,
    callback: (res: any) => void,
  ) => void;
  getFilteredTimeLineRequest: (
    payload: any,
    callback: (res: any) => void,
  ) => void;
  personalTimeLine: any;
  userInfo: UserInfo;
  otherUserDetails: UserInfo;
  completeProfile: CompleteProfile;
  userMedia_CheckInRequest: (
    payload: IMedia_CheckIn,
    callback: (res: any) => void,
  ) => void;
  userTagMediaRequest: (
    payload: ITagMedia,
    callback: (res: any) => void,
  ) => void;
  userInfoRequest: (response: (res: any) => void) => void;
  getValuePortfolioRequest: (
    payload: any,
    callback: (res: any) => void,
  ) => void;
  deleteImprintRequest: (
    payload: IDeleteImprintPayload,
    callback: (res: any) => void,
  ) => void;
  blockImprintRequest: (
    payload: IBlockEntity,
    callback: (res: any) => void,
  ) => void;
  portfolioValue: Score;
  portfolio: any;
  resetTimeLine: () => void;
  profilePhotoModerationRequest: (
    payload: any,
    callback: (res: any) => void,
  ) => void;
}

const UserProfile: React.FC<UserProfileProps> = ({
  unFollowRequest,
  blockUserRequest,
  user,
  userTimelineRequest,
  postReactionRequest,
  imprintShoutoutRequest,
  imprintVerificationRequest,
  editImageRequest,
  completeProfileRequest,
  getUserTimeLineRequest,
  getOtherUserDetailsRequest,
  getFilteredTimeLineRequest,
  imprintBookmarkRequest,
  requestFollowRequest,
  getValuePortfolioRequest,
  blockImprintRequest,
  deleteImprintSuccess,
  deleteImprintRequest,
  personalTimeLine,
  otherUserDetails,
  completeProfile,
  portfolio,
  userInfoRequest,
  resetTimeLine,
  profilePhotoModerationRequest,
  reportContentRequest,
}) => {
  const animatedValue = new Animated.Value(0);
  const navigation = useNavigation();
  const [page, setPage] = useState<number>(1);
  const [personalMorePage, setpersonalMorePage] = useState<boolean>(true);
  let scrolled = useRef(false).current;
  const [paylod, setPayload] = useState<PostReactionPayload>({});
  const [pdfURL, setPdfURL] = useState<string>('');
  const [visibleModal, setVisibleModal] = useState(false);
  const [selectedItem, setSelectedItem] = useState<IProfileOptions>(
    PROFILE_OPTIONS[0],
  );
  const [showBottomModal, setBottomModal] = useState<boolean>(false);
  const [profileOptions, setProfileOptions] = useState(PROFILE_OPTIONS);
  const [editImage, setEditImage] = useState(false);
  const [loading, setLoading] = useState<boolean>(true);
  const [aboutOptions, setAboutOptions] = useState<IAboutCardHeader[]>([]);
  const route = useRoute();
  const {userName} = (route?.params as {userName: string}) || {userName: ''};
  const [otherUserProfileDetails, setOtherUserDetails] = useState<UserInfo>(
    user.userInfo as UserInfo,
  );
  const [showKin, setShowKin] = useState<boolean>(false);
  const [imprintData, setImprintData] = useState<ITimeLine>();
  const [userImage, setUserImage] = useState<string>(user.userInfo.avatarUrl);
  const [refreshing, setRefreshing] = useState(false);
  const [showReportModal, setShowReportModal] = useState<boolean>(false);
  const [showViolation, setShowViolation] = useState<boolean>(false);
  const [showBadgeModal, setBadgeModal] = useState<boolean>(false);
  const actionSheetRef = useRef<ProfileActonSheet>(null);
  const [onlyUser, setOnlyUser] = useState<boolean>(false);

  const [ageGroup, setAgeGroup] = useState<string>('');
  const [reportType, setReportType] = useState<string>('');
  const selectedItemBeforeModal = useRef<{id: number; name: string} | null>(
    null,
  );

  const onItemPress = (item: React.SetStateAction<IProfileOptions>) => {
    const {subscriptionStatus} = user.userInfo;

    if (
      item.name !== PROFILE_OPTIONS_LIST.ABOUT &&
      item.name !== PROFILE_OPTIONS_LIST.POSTS &&
      subscriptionStatus !== SubscriptionStatus.ACTIVE &&
      subscriptionStatus !== SubscriptionStatus.CancellationScheduled &&
      subscriptionStatus !== SubscriptionStatus.TRAILING
    ) {
      showToastMsg(TOAST_MESSAGES.SUBSCRIPTION_ERROR);
      return;
    }

    setSelectedItem(item);

    if (item.name === PROFILE_OPTIONS_LIST.MORE) {
      setBottomModal(true);
      selectedItemBeforeModal.current = selectedItem;
    }
  };

  const isFocus = useIsFocused();

  const handleAddOption = (id: number, name: string) => {
    const friendsOptionIndex = profileOptions.findIndex(
      option => option.name === PROFILE_OPTIONS_LIST.FRIENDS,
    );
    const moreOptionIndex = profileOptions.findIndex(
      option => option.name === PROFILE_OPTIONS_LIST.MORE,
    );

    if (friendsOptionIndex !== -1 && moreOptionIndex !== -1) {
      setProfileOptions(prevOptions => {
        const newOptions = [...prevOptions];
        newOptions.splice(
          friendsOptionIndex + 1,
          moreOptionIndex - friendsOptionIndex - 1,
          {id, name},
        );
        return newOptions;
      });
      setSelectedItem({id, name});
    }
    setBottomModal(false);
  };

  useEffect(() => {
    if (
      completeProfile &&
      selectedItem.name === PROFILE_OPTIONS_LIST.ABOUT &&
      userName === ''
    ) {
      setAboutOptions(generateAboutData(completeProfile));
    }
  }, [completeProfile, refreshing]);

  useEffect(() => {
    if (isFocus) {
      userInfoRequest(res => {
        setUserImage(res.avatarUrl);
      });
    }
  }, [isFocus, refreshing]);

  useEffect(() => {
    if (
      completeProfile &&
      selectedItem.name === PROFILE_OPTIONS_LIST.ABOUT &&
      userName === ''
    ) {
      setAboutOptions(generateAboutData(completeProfile));
    }
  }, [completeProfile, refreshing]);

  useEffect(() => {
    const targetOption = profileOptions.find(
      option => option.name === selectedItem.name,
    );
    if (targetOption) {
      if (
        targetOption.name !== PROFILE_OPTIONS_LIST.POSTS ||
        targetOption.name === PROFILE_OPTIONS_LIST.SPEAK_UP ||
        targetOption.name === PROFILE_OPTIONS_LIST.BOOK_MARKS ||
        targetOption.name === PROFILE_OPTIONS_LIST.ARTICLES
      ) {
        setPage(1);
        scrolled = false;
        setpersonalMorePage(true);
      }
      setSelectedItem(targetOption);
    }
  }, [profileOptions, selectedItem, refreshing]);

  useEffect(() => {
    if (selectedItem.name === PROFILE_OPTIONS_LIST.POSTS) {
      if (userName === user.userInfo.displayName || userName === '') {
        const payload = {page};
        requestAnimationFrame(() => {
          if (personalMorePage) {
            userTimelineRequest(payload, (res: ITimeLine[]) => {
              if (res && res.length < 10) {
                setpersonalMorePage(false);
              }
              endAnimation();
            });
            const payloadOther = {
              isGlobal: true,
              type: 'overall',
            };
            getValuePortfolioRequest(payloadOther, () => {});
          }
        });
      } else {
        const payload = {page, userName};
        getUserTimeLineRequest(payload, (res: ITimeLine[]) => {
          if (res && res.length < 10) {
            setpersonalMorePage(false);
          }
          endAnimation();
        });
        getOtherUserDetailsRequest({name: userName}, (res: any) => {
          if (res) {
            setOnlyUser(true);
            setOtherUserDetails(res);
            setAboutOptions(generateAboutDataForOtherUsers(res));
          }
        });
      }
    } else if (selectedItem.name === PROFILE_OPTIONS_LIST.ABOUT) {
      completeProfileRequest(() => {});
    } else if (selectedItem.name === PROFILE_OPTIONS_LIST.SPEAK_UP) {
      const payload = {page, name: 'speakup'};
      getFilteredTimeLineRequest(payload, (res: any) => {
        if (res && res.length < 10) {
          setpersonalMorePage(false);
        }
        endAnimation();
      });
    } else if (selectedItem.name === PROFILE_OPTIONS_LIST.BOOK_MARKS) {
      const payload = {page, name: 'bookmarks'};
      setLoading(true);
      getFilteredTimeLineRequest(payload, (res: any) => {
        setLoading(false);
        if (res && res.length < 10) {
          setpersonalMorePage(false);
        }
        endAnimation();
      });
    } else if (selectedItem.name === PROFILE_OPTIONS_LIST.ARTICLES) {
      const payload = {page, name: 'articles'};
      getFilteredTimeLineRequest(payload, (res: any) => {
        if (res && res.length < 10) {
          setpersonalMorePage(false);
        }
        endAnimation();
      });
    }
  }, [
    page,
    selectedItem,
    userName,
    user.userInfo.displayName,
    otherUserDetails.avatarUrl,
    refreshing,
    otherUserDetails.followerRequestSent,
  ]);

  const startAnimation = () => {
    Animated.timing(animatedValue, {
      toValue: 1,
      duration: 500,
      useNativeDriver: true,
    }).start();
  };

  const endAnimation = () => {
    Animated.timing(animatedValue, {
      toValue: 0,
      duration: 500,
      useNativeDriver: true,
    }).start();
  };

  const renderPDF = (pdfURL: string) => {
    return (
      <Modal
        animationType="fade"
        transparent={true}
        visible={pdfURL !== ''}
        onRequestClose={() => {
          setPdfURL('');
        }}>
        <TouchableOpacity
          onPressOut={() => setPdfURL('')}
          style={styles.pdfModal}>
          <View style={styles.pdf}>
            <FileViewer pdfURL={pdfURL} />
          </View>
        </TouchableOpacity>
      </Modal>
    );
  };

  useEffect(() => {
    setLoading(true);
    setTimeout(() => {
      setLoading(false);
    }, 1000);
  }, []);

  const handleReactionClicked = useCallback(
    (
      reaction?: string,
      userImprintInteraction?: UserImprintInteractions,
      item?: ITimeLine,
    ) => {
      if (reaction === IMPRINT_TIMELINE.BOOKMARK) {
        const payload = {
          imprintId: item?.id,
          isBookmark: !userImprintInteraction?.isBookmarked,
          isGlobal: false,
        };
        imprintBookmarkRequest(payload, () => {});
      } else {
        if (!item && !userImprintInteraction) return;

        const payload: PostReactionPayload = {
          isHide: false,
          imprintId: item?.id,
          userId: user?.data?.userId,
          reaction:
            userImprintInteraction?.reaction === reaction
              ? userImprintInteraction?.reaction
              : reaction === ReactionType.SHOUT_OUT ||
                reaction === IMPRINT_TIMELINE.FLAG ||
                reaction === IMPRINT_TIMELINE.BOOKMARK
              ? userImprintInteraction?.reaction
              : reaction,
          isRedFlagged:
            reaction === IMPRINT_TIMELINE.FLAG
              ? !userImprintInteraction?.isRedFlagged
              : userImprintInteraction?.isRedFlagged ?? false,
        };

        if (userImprintInteraction?.reaction === reaction) {
          payload.reaction = null;
        }

        payload.isGlobal = false;

        if (reaction === ReactionType.SHOUT_OUT) {
          util.shoutOutAlert(() => {
            const shoutoutPayload = {
              imprintId: item?.id,
            };
            imprintShoutoutRequest(shoutoutPayload, () => {});
          });
        } else if (reaction === IMPRINT_TIMELINE.FLAG) {
          setImprintData(item);

          item?.userId === user.data.userId
            ? setBadgeModal(true)
            : setOnlyUser(false),
            setShowReportModal(true);
        } else {
          if (payload.isRedFlagged) {
            delete payload.isRedFlagged;
          }
          postReactionRequest(payload, () => {});
        }
      }
    },
    [postReactionRequest, imprintShoutoutRequest, user?.data?.userId],
  );

  const handleMediaClicked = useCallback((item?: ITimeLine) => {
    if (!item) return;
    if (
      item?.imprintMedias.length == 1 &&
      item?.imprintMedias[0]?.type == IMPRINT_MEDIA.DOCUMENT
    ) {
      setPdfURL(item?.imprintMedias[0]?.url || '');
    } else {
      (navigation.navigate as (route: string, params: any) => void)(
        Routes.TIME_LINE_MEDIA_VIEW,
        {item},
      );
    }
  }, []);

  const handleVerifyClick = (imprint?: ITimeLine) => {
    if (!imprint) return;
    if (
      imprint?.verificationRequest &&
      imprint?.verificationRequest?.verificationStatus ===
        IMPRINT_TIMELINE.REQUESTED
    ) {
      showToastMsg(TOAST_MESSAGES.ALREADY_REQUESET);
    } else if (
      imprint?.verificationRequest?.verificationStatus ===
      IMPRINT_TIMELINE.FAILED
    ) {
      showToastMsg(TOAST_MESSAGES.FAILED);
    } else if (
      imprint?.verificationRequest?.verificationStatus ===
      IMPRINT_TIMELINE.VERIFIED
    ) {
      showToastMsg(TOAST_MESSAGES.POST_VERIFIED);
    } else if (
      imprint?.verificationRequest?.verificationStatus ===
      IMPRINT_TIMELINE.REQUEST_DENIED
    ) {
      showToastMsg(TOAST_MESSAGES.REQUEST_DENIED);
    } else {
      const payload = {
        id: imprint?.id,
        isGlobal: false,
      };

      imprintVerificationRequest(payload, (res: any) => {
        if (res) {
          showToastMsg(
            TOAST_MESSAGES.VERIFICATION_REQUEST_SENT,
            TOAST_TYPE.SUCCESS,
          );
        }
      });
    }
  };

  const hideModal = () => {
    setVisibleModal(!visibleModal);
  };

  const onMoreOptionClick = (imprint?: ITimeLine) => {
    setImprintData(imprint);
    //  setFollowId(imprint?.user.id ?? '');
    setVisibleModal(true);
  };

  const handleOnPressArticle = (article: ImprintMedia) => {
    Linking.openURL(article.url ?? '');
  };

  const imageEditClicked = () => {
    setEditImage(!editImage);
  };

  const onLeaveImprintPress = () => {
    if (!isUserSubscribed(user)) {
      (navigation.navigate as (route: string) => void)(Routes.SUBSCRIPTION);
      showToastMsg(
        TOAST_MESSAGES.SUBSCRIPTION_ERROR,
        TOAST_TYPE.DEFAULT,
        TOAST_VISIBILITY_TIMEOUT,
        'top',
      );
      return;
    }
    userInfoRequest((res: UserInfo) => {
      if (res.kinApprovalStatus === KIN_STATUS.PENDING) {
        setShowKin(true);
      } else {
        (navigation.navigate as (route: string) => void)(Routes.CREATE_IMPRINT);
        setpersonalMorePage(true);
        setPage(1);
      }
    });
  };

  const updateImage = (image: any) => {
    setEditImage(false);
    setLoading(true);
    const formData = new FormData();
    if (image) {
      const payload = {
        uri: image.path ? image.path : image.sourceURL,
        type: image.mime,
        name: image.path.substring(image.path.lastIndexOf('/') + 1),
      };
      formData.append('file', payload);
    }
    profilePhotoModerationRequest(formData, (res: any) => {
      if (res.isValidContent === true) {
        editImageRequest(formData, (res: any) => {
          if (res) {
            setTimeout(() => {
              setLoading(false);
              userInfoRequest(() => {});
              setUserImage(res.avatarUrl);
            }, 1000);
          }
        });
      } else {
        setLoading(false);
        showToastMsg(TOAST_MESSAGES.IMAGE_MODERATION_ERROR);
      }
    });
  };

  const openGalleryView = async () => {
    ImageCropPicker.openPicker({
      width: 300,
      height: 400,
      cropping: true,
      cropperCircleOverlay: true,
      mediaType: 'photo',
    })
      .then(image => {
        if (image) {
          updateImage(image);
        }
      })
      .catch(error => {
        if (error.message.includes(TOAST_MESSAGES.GALLERY_MSG_ERROR)) {
          Alert.alert(
            TOAST_MESSAGES.PERMISSION_ERROR_HEADING,
            TOAST_MESSAGES.ALLOW_PHOTO_PERMISSION,
            [
              {text: 'Open Settings', onPress: () => Linking.openSettings()},
              {text: 'Not Now', onPress: () => {}},
            ],
          );
        }
      });
  };

  const openCameraView = async () => {
    ImageCropPicker.openCamera({
      cropping: true,
      cropperCircleOverlay: true,
      freeStyleCropEnabled: true,
      mediaType: 'photo',
      compressImageQuality: 1,
    })
      .then(image => {
        if (image) {
          updateImage(image);
        }
      })
      .catch(() => {
        Alert.alert(
          TOAST_MESSAGES.CAMERA_ERROR_HEADING,
          TOAST_MESSAGES.CAMERA_ERROR_DETAIL,
          [
            {text: 'Open Settings', onPress: () => Linking.openSettings()},
            {text: 'Not Now', onPress: () => {}},
          ],
        );
      });
  };

  const handleCrossClicked = () => {
    setShowKin(false);
  };

  const onEditClicked = (data: IAboutCardHeader) => {
    if (
      data.name === ABOUT.OVERVIEW ||
      data.name === ABOUT.PERSONAL_INFO ||
      data.name === ABOUT.CONTACT
    ) {
      (navigation.navigate as (route: string, {}) => void)(
        Routes.TYPE_ONE_EDIT,
        {
          data: data,
        },
      );
    } else {
      (navigation.navigate as (route: string, {}) => void)(
        Routes.TYPE_TWO_EDIT,
        {
          data: data,
        },
      );
    }
  };

  const handlingBackNavigation = () => {
    resetTimeLine();
    navigation.goBack();
  };

  const HeaderComponent = useMemo(
    () => (
      <View style={styles.header}>
        <UserCoverDetails
          user={userName !== '' ? otherUserProfileDetails! : user.userInfo}
          image={
            userName !== '' ? otherUserProfileDetails!.avatarUrl : userImage
          }
          imageEdit={userName !== '' ? undefined : imageEditClicked}
          scores={userName !== '' ? otherUserProfileDetails!.scores : portfolio}
          isOwnProfile={userName === ''}
          showFollowButton={
            !otherUserProfileDetails?.followerRequestSent &&
            !otherUserProfileDetails?.isFollower
          }
          onFollowPress={() =>
            Alert.alert(
              FRIENDS.FRIEND_REQUEST,
              `${FRIENDS.FRIENDS_REQUEST_DETAILS}${otherUserProfileDetails.displayName} ?`,
              [
                {
                  text: 'Yes',
                  onPress: () => onFollowPress(otherUserProfileDetails.id),
                },
                {text: 'No ', onPress: () => {}},
              ],
            )
          }
        />

        {pdfURL ? renderPDF(pdfURL) : null}
        <View style={styles.white}>
          <View style={styles.border} />
          <ProfileOptions
            user={user}
            profileOptions={profileOptions}
            onItemPress={item => onItemPress(item)}
            selectedItem={selectedItem}
            isOtherProfile={userName === '' ? false : true}
          />
          <View style={[styles.bottomBorder]} />
        </View>

        {selectedItem.name == PROFILE_OPTIONS_LIST.POSTS && userName === '' && (
          <LeaveImprint
            onLeaveBoxPress={() => {
              onLeaveImprintPress();
            }}
          />
        )}
      </View>
    ),
    [
      otherUserProfileDetails,
      user,
      imageEditClicked,
      otherUserProfileDetails.avatarUrl,
      otherUserProfileDetails.scores,
      user.userInfo.displayName,
      user.userInfo.avatarUrl,
      user.userInfo.scores,
      refreshing,
      setRefreshing,
    ],
  );

  const onFollowPress = (userId: string) => {
    const payload = {
      to: userId,
      isGlobal: false,
    };

    requestFollowRequest(payload, (res: any) => {
      if (res) {
        showToastMsg(TOAST_MESSAGES.FOLLOW_REQUEST_SENT, TOAST_TYPE.SUCCESS);
        setVisibleModal(false);

        setOtherUserDetails({
          ...otherUserProfileDetails,
          followerRequestSent: true,
        });
      }
    });
  };

  const onRefresh = () => {
    setRefreshing(true);

    setTimeout(() => {
      setRefreshing(false);
    }, 2000);
  };

  const onBlockPress = () => {
    const payload: IBlockEntity = {
      follower: imprintData?.userId || '',
      status: true,
    };
    setVisibleModal(false);
    setTimeout(() => {
      setLoading(true);
      blockImprintRequest(payload, res => {
        if (res) {
          const obj = {
            userId: imprintData?.userId || '',
          };
          deleteImprintSuccess(obj);
          setVisibleModal(false);
          setLoading(false);
        }
      });
    }, 500);

    setLoading(false);
    setVisibleModal(false);
  };

  const onDeletePress = () => {
    setLoading(true);
    setVisibleModal(false);

    const payload: IDeleteImprintPayload = {
      imprintId: imprintData?.id || '',
    };
    deleteImprintRequest(payload, () => {
      setLoading(false);
    });
  };

  const revisitProfile = (item: FOLLOWERS) => {
    navigation.push(Routes.USER_PROFILE, {userName: item.userName});
  };

  const handleOptionPress = (text: string) => {
    switch (text) {
      case FRIENDS.UN_BLOCK_USER:
      case FRIENDS.BLOCK_USER:
        const isCurrentlyBlocked = !!otherUserProfileDetails.isUserBlocked;

        const payload: IBlockEntity = {
          follower: otherUserProfileDetails.id,
          status: !isCurrentlyBlocked,
        };
        blockUserRequest(payload, (res: any) => {
          if (res) {
            showToastMsg(
              `User ${
                otherUserProfileDetails.isUserBlocked ? 'Unblock' : 'Block'
              } Successfully `,
              TOAST_TYPE.SUCCESS,
              2000,
              'top',
            );

            setOtherUserDetails({
              ...otherUserProfileDetails,
              isUserBlocked: !otherUserProfileDetails.isUserBlocked,
            });
          }
        });
        break;

      case FRIENDS.BLOCK:
      case FRIENDS.UN_BLOCK:
        const isImprintBlocked = !!otherUserProfileDetails.blockedImprints;

        const payloadBlock: IBlockEntity = {
          follower: otherUserProfileDetails.id,
          status: !isImprintBlocked,
        };
        blockImprintRequest(payloadBlock, res => {
          if (res) {
            showToastMsg(
              `Imprints ${
                otherUserProfileDetails.blockedImprints ? 'Unblock' : 'Block'
              } Successfully `,
              TOAST_TYPE.SUCCESS,
              2000,
              'top',
            );

            setOtherUserDetails({
              ...otherUserProfileDetails,
              blockedImprints: !otherUserProfileDetails.blockedImprints,
            });
          }
        });
        break;
      case FRIENDS.REPORT_USER:
        setShowReportModal(true);
        setOnlyUser(true);

        break;

      case FRIENDS.REMOVE:
        const payloadUnfollow: IUnFollow = {
          follower: otherUserProfileDetails?.id.toString(),
        };
        unFollowRequest(payloadUnfollow, () => {
          showToastMsg(TOAST_MESSAGES.REMOVE_FROM_TRIBE, TOAST_TYPE.SUCCESS);
          setOtherUserDetails({
            ...otherUserProfileDetails,
            isFollower: false,
          });
        });
    }
  };

  if (loading && userName !== '') {
    return <Loader loading={loading} />;
  }

  return (
    <ActionSheetProvider>
      <View style={styles.container}>
        {showKin ? (
          <View
            style={{
              height: Metrics.screenHeight,
              width: Metrics.screenWidth,
              backgroundColor: 'transparent',
              position: 'absolute',
              zIndex: 1000,
            }}>
            <NextOfKin crossClicked={handleCrossClicked} />
          </View>
        ) : (
          <></>
        )}
        {showReportModal && (
          <ReportModal
            onlyForUser={onlyUser}
            reportType={REPORT_CONTENT.IMPRINT}
            crossClicked={() => setShowReportModal(false)}
            handleSubmitClick={(ageGroup, option) => {
              setAgeGroup(ageGroup);
              setReportType(option);
              setShowReportModal(false);
              setShowViolation(true);
            }}
          />
        )}

        {showViolation && (
          <ViolationModal
            crossClicked={() => setShowViolation(false)}
            handleSubmitClick={ageGroup => {
              setShowViolation(false);
              const payload: IReportContentPayload = {
                id:
                  reportType === REPORT_ENUMS.POST
                    ? imprintData?.id || ''
                    : user.userInfo.userId,
                type: reportType,
                potential_violations: ageGroup,
                imprintId: imprintData?.id,
                userId: imprintData?.userId,
                isGlobal: false,
              };

              reportContentRequest(payload, res => {
                if (res) {
                  showToastMsg(
                    reportType === REPORT_ENUMS.POST
                      ? TOAST_MESSAGES.REPORT_SENT
                      : TOAST_MESSAGES.REPORT_USER,
                    TOAST_TYPE.SUCCESS,
                    2000,
                    'top',
                  );
                }
              });
            }}
            ageGroup={ageGroup}
          />
        )}

        {showBadgeModal && (
          <ReportBadgeModal
            crossClicked={() => setBadgeModal(false)}
            handleSubmitClick={ageGroup => {
              setShowViolation(false);
              const payload: IReportContentPayload = {
                id: imprintData?.id,
                type: 'post',
                potential_violations: ageGroup,
                imprintId: imprintData?.id,
                userId: imprintData?.userId,
                isGlobal: false,
              };

              reportContentRequest(payload, res => {
                if (res) {
                  showToastMsg(
                    TOAST_MESSAGES.REPORT_VALUE_BADGE,
                    TOAST_TYPE.SUCCESS,
                    2000,
                    'top',
                  );
                  setBadgeModal(false);
                  // onFlagImprint(imprintData);
                }
              });
            }}
            ageGroup={ageGroup}
          />
        )}
        <CustomNavbar
          title={
            userName !== ''
              ? otherUserProfileDetails.displayName
              : user.userInfo.displayName
          }
          titleSize={16}
          leftBtnPress={handlingBackNavigation}
          style={styles.minHeight}
          hasMultiRight
          rightBtnImage1={
            otherUserProfileDetails.id !== user.data.userId ? (
              <THREE_DOT_VERTICAL />
            ) : undefined
          }
          rightBtnPress1={() => actionSheetRef.current?.handlePress()}
          img1Style={AppStyles.padding15}
        />

        <AnimatedFlashList
          ListHeaderComponent={HeaderComponent}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
          data={
            selectedItem.name == PROFILE_OPTIONS_LIST.POSTS ||
            selectedItem.name == PROFILE_OPTIONS_LIST.SPEAK_UP ||
            selectedItem.name == PROFILE_OPTIONS_LIST.BOOK_MARKS ||
            selectedItem.name == PROFILE_OPTIONS_LIST.ARTICLES
              ? !_.isEmpty(personalTimeLine)
                ? personalTimeLine
                : []
              : selectedItem.name == PROFILE_OPTIONS_LIST.ABOUT
              ? aboutOptions
              : [1]
          }
          keyExtractor={
            selectedItem.name == PROFILE_OPTIONS_LIST.POSTS ||
            selectedItem.name == PROFILE_OPTIONS_LIST.SPEAK_UP ||
            selectedItem.name == PROFILE_OPTIONS_LIST.BOOK_MARKS ||
            selectedItem.name == PROFILE_OPTIONS_LIST.ARTICLES
              ? (item: {id: string}, index) => item?.id + index
              : (item, index) => index.toString()
          }
          contentContainerStyle={
            !personalMorePage
              ? styles.flastList
              : {backgroundColor: 'transparent'}
          }
          estimatedItemSize={447}
          onScroll={() => (scrolled = true)}
          onEndReached={() => {
            if (
              selectedItem.name == PROFILE_OPTIONS_LIST.POSTS ||
              selectedItem.name == PROFILE_OPTIONS_LIST.SPEAK_UP ||
              selectedItem.name == PROFILE_OPTIONS_LIST.BOOK_MARKS ||
              selectedItem.name == PROFILE_OPTIONS_LIST.ARTICLES
            ) {
              {
                !personalMorePage ? null : startAnimation();
              }
              if (scrolled) {
                setPage(page + 1);
              }
            }
          }}
          renderItem={({item, index}) => (
            <View
              style={index === 0 ? {marginTop: 0} : {}}
              key={selectedItem.name}>
              {selectedItem.name == PROFILE_OPTIONS_LIST.POSTS ||
              selectedItem.name == PROFILE_OPTIONS_LIST.SPEAK_UP ||
              selectedItem.name == PROFILE_OPTIONS_LIST.BOOK_MARKS ||
              selectedItem.name == PROFILE_OPTIONS_LIST.ARTICLES ? (
                <Animated.View
                  style={{
                    opacity: animatedValue.interpolate({
                      inputRange: [0.4, 1],
                      outputRange: [1, 0.4],
                    }),
                  }}>
                  {!_.isEmpty(personalTimeLine) ? (
                    <TimeLineCard
                      handleReactionClicked={(
                        userImprintInteraction,
                        reaction,
                      ) =>
                        handleReactionClicked(
                          reaction,
                          userImprintInteraction,
                          item as ITimeLine,
                        )
                      }
                      handleMediaClicked={() =>
                        handleMediaClicked(item as ITimeLine)
                      }
                      handleVerifyClick={handleVerifyClick}
                      handleMoreOptionClick={onMoreOptionClick}
                      onPressArticle={handleOnPressArticle}
                      item={item as ITimeLine}
                      isGlobal={false}
                      user={user}
                    />
                  ) : (
                    <></>
                  )}
                </Animated.View>
              ) : null}

              {selectedItem.name == PROFILE_OPTIONS_LIST.FRIENDS && (
                <FriendsHub onItemPress={revisitProfile} />
              )}
              {selectedItem.name == PROFILE_OPTIONS_LIST.ABOUT &&
              aboutOptions.length > 0 ? (
                <AboutCard
                  data={item}
                  onEditClicked={
                    userName === user.userInfo.displayName || userName === ''
                      ? onEditClicked
                      : undefined // Change null to undefined
                  }
                />
              ) : null}

              {selectedItem.name == PROFILE_OPTIONS_LIST.PHOTOS && (
                <MultipleMediaList mediaType={IMPRINT_MEDIA.IMAGE} />
              )}
              {selectedItem.name == PROFILE_OPTIONS_LIST.VIDEOS && (
                <MultipleMediaList mediaType={IMPRINT_MEDIA.VIDEO} />
              )}
              {selectedItem.name == PROFILE_OPTIONS_LIST.CHECK_INS && (
                <CheckInTab />
              )}
            </View>
          )}
        />
        <ProfileActionSheetComponent
          userData={otherUserProfileDetails}
          ref={actionSheetRef}
          onSelect={handleOptionPress}
        />
        <RenderModal
          hideModal={hideModal}
          visible={visibleModal}
          onFollowPress={onFollowPress}
          userId={imprintData?.user.id || ''}
          isSelfUser={user.userInfo.userId}
          imprintData={imprintData}
          onBlockPress={onBlockPress}
          onDeletePress={onDeletePress}
        />

        <MoreBottomModal
          showMoreModal={showBottomModal}
          hideModal={() => {
            if (selectedItemBeforeModal.current) {
              setSelectedItem(selectedItemBeforeModal.current);
              setBottomModal(false);
            }
          }}
          onPhotoPress={() => handleAddOption(5, PROFILE_OPTIONS_LIST.PHOTOS)}
          onVideosPress={() => handleAddOption(6, PROFILE_OPTIONS_LIST.VIDEOS)}
          onArticlesPress={() =>
            handleAddOption(7, PROFILE_OPTIONS_LIST.ARTICLES)
          }
          onCheckInPress={() =>
            handleAddOption(7, PROFILE_OPTIONS_LIST.CHECK_INS)
          }
          onBookMarksPress={() =>
            handleAddOption(8, PROFILE_OPTIONS_LIST.BOOK_MARKS)
          }
          onSpeakUpPress={() =>
            handleAddOption(9, PROFILE_OPTIONS_LIST.SPEAK_UP)
          }
        />
        <Loader loading={loading} />
        {editImage && (
          <View style={styles.editImage}>
            <UploadImage
              onCameraPress={openCameraView}
              onGalleryPress={openGalleryView}
              showNavBar={true}
              close={imageEditClicked}
            />
          </View>
        )}
      </View>
    </ActionSheetProvider>
  );
};

const mapStateToProps = (state: any) => ({
  personalTimeLine: state.timeline.personalTimeLine,
  user: state.user,
  userInfo: state.profile.userInfo,
  completeProfile: state.profile.completeProfile,
  otherUserDetails: state.profile.otherUserDetails,
  portfolioValue: state.analytics.portfolio,
  portfolio: state.analytics.portfolio,
});

export default connect(mapStateToProps, {
  userTimelineRequest,
  editImageRequest,
  postReactionRequest,
  imprintShoutoutRequest,
  imprintVerificationRequest,
  completeProfileRequest,
  getUserTimeLineRequest,
  getOtherUserDetailsRequest,
  getFilteredTimeLineRequest,
  imprintBookmarkRequest,
  deleteImprintSuccess,
  requestFollowRequest,
  userInfoRequest,
  getValuePortfolioRequest,
  blockImprintRequest,
  deleteImprintRequest,
  resetTimeLine,
  profilePhotoModerationRequest,
  reportContentRequest,
  blockUserRequest,
  unFollowRequest,
})(UserProfile);
