// @flow
import {StyleSheet} from 'react-native';
import {Metrics} from '../../theme';

export default StyleSheet.create({
  container: {
    flex: 1,
  },
  userinfoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: Metrics.baseMargin,
  },
  imageContainer: {
    marginTop: Metrics.ratio(0),
    height: Metrics.ratio(45),
    width: Metrics.ratio(45),
  },
  image: {
    height: Metrics.ratio(45),
    width: Metrics.ratio(45),
  },

  collagePadding: {padding: Metrics.ratio(24)},
  userInfoTextContainer: {
    flex: 1,
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
  },
  kinContainer: {
    height: Metrics.screenHeight,
    width: Metrics.screenWidth,
    backgroundColor: 'transparent',
    position: 'absolute',
    zIndex: 1000,
  },
});
