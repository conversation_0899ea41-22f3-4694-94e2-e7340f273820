// @flow

import React, {useRef} from 'react';
import {View, Image} from 'react-native';
import {CustomNavbar} from '../../components';
import styles from './styles';
import {CROSS_ROUND} from '../../constants/AssetSVGConstants';
import {Colors} from '../../theme';
import _ from 'lodash';
import {SafeAreaView} from 'react-native-safe-area-context';
import Video from 'react-native-video';
import util from '../../util';
import {MediaItem} from '../../types';
import {useNavigation} from '@react-navigation/native';
import Pdf from 'react-native-pdf';
import {IMPRINT_MEDIA_TYPES_MAP} from '../../constants';

interface ListProps {
  route: {
    params: {
      item: MediaItem;
    };
  };
}

const ViewMedia: React.FC<ListProps> = ({route}) => {
  const {item} = route.params;
  const navigation = useNavigation();

  const videoRef = useRef(null);
  const type = util.getType(item.mime as string);

  return (
    <SafeAreaView style={{flex: 1}}>
      <CustomNavbar
        leftBtnPress={() => navigation.goBack()}
        leftBtnImage={<CROSS_ROUND />}
        style={{backgroundColor: Colors.black1}}
      />
      <View style={styles.container}>
        {type === IMPRINT_MEDIA_TYPES_MAP.image ||
        type == IMPRINT_MEDIA_TYPES_MAP.map ? (
          <Image
            source={{uri: item.sourceURL || item.path}}
            style={styles.images}
          />
        ) : type === IMPRINT_MEDIA_TYPES_MAP.document ? (
          <View style={styles.container}>
            <Pdf
              source={{uri: item.file}}
              onLoadComplete={numberOfPages => {}}
              style={styles.pdf}
            />
          </View>
        ) : (
          <>
            <Video
              resizeMode="contain"
              controls
              paused={false}
              volume={2.0}
              source={{
                uri: item.path,
              }}
              ref={videoRef}
              style={styles.images}
            />
          </>
        )}
      </View>
    </SafeAreaView>
  );
};

export default ViewMedia;
