import _ from 'lodash';
import {connect} from 'react-redux';
import React, {useEffect, useRef, useState} from 'react';
import {
  IQuiz,
  IQuizContentResponse,
  IQuizResponse,
  ISelectedAnswer,
  ISubmitQuizPayload,
  UserInfo,
} from '../../types';
import {Text, Loader, CustomNavbar} from '../../components/';

import {View, FlatList} from 'react-native';
import styles from './styles';
import {AppStyles, Colors, Fonts} from '../../theme';

import {
  getEntertainmentQuiz,
  submitQuizRequest,
  updateRewardItem,
} from '../../actions/EntertainmentActions';
import {useNavigation} from '@react-navigation/native';
import {ENTERTAINMENTS, IMPRINT_SCORES} from '../../constants/StringConstants';
import QuestionItem from '../../components/QuizQuestion';
import {hasNotch} from 'react-native-device-info';
import {
  LAUGHTER,
  LIFE,
  LOVE,
  PURPOSE,
  RESPECT,
  SAFETY,
  TICK_ICON,
  TICK_ICON_NEXT,
} from '../../constants/AssetSVGConstants';
import util from '../../util';
import ShareModal from '../../components/EntertainmentComponents/ShareModal';
import {BottomSheetModal} from '@gorhom/bottom-sheet';

interface EntertainmentQuizProps {
  getEntertainmentQuiz: (payload: IQuiz, callback: (res: any) => void) => void;
  submitQuizRequest: (
    payload: ISubmitQuizPayload,
    callback: (res: any) => void,
  ) => void;
  updateRewardItem: (item: any) => void;

  user: UserInfo;
  route: {
    params: {
      item: IQuizContentResponse;
    };
  };
}

const EntertainmentQuiz: React.FC<EntertainmentQuizProps> = ({
  getEntertainmentQuiz,
  submitQuizRequest,
  user,
  route,
  updateRewardItem,
}) => {
  const [questions, setQuestions] = useState<IQuizResponse[]>([]);
  const [selectedAnswers, setSelectedAnswers] = useState<ISelectedAnswer[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [reward, setReward] = useState<any>({});

  const shareBottomSheet = useRef<BottomSheetModal>(null);

  const TotalQuestions = route.params.item.totalQuestions;

  const navigation = useNavigation();

  useEffect(() => {
    const payload = {
      id: route.params.item.id,
      page: 1,
      limit: route.params.item.totalQuestions,
    };
    setLoading(true);
    getEntertainmentQuiz(payload, res => {
      setLoading(false);

      setQuestions(res.questions);
    });
  }, []);

  const handleOptionPress = (
    questionId: string,
    optionId: string,
    optionKey: string | undefined,
  ) => {
    setSelectedAnswers((prevState: ISelectedAnswer[]) => {
      const existingAnswerIndex = prevState.findIndex(
        (answer: ISelectedAnswer) => answer.questionId === questionId,
      );
      if (existingAnswerIndex >= 0) {
        const updatedAnswers = [...prevState];
        updatedAnswers[existingAnswerIndex] = {questionId, optionId, optionKey};
        return updatedAnswers;
      } else {
        return [...prevState, {questionId, optionId, optionKey}];
      }
    });
  };

  const handleSubmit = () => {
    setLoading(true);
    const payload: any = {
      answers: selectedAnswers,
      quizId: route.params.item.id,
    };

    submitQuizRequest(payload, res => {
      if (res) {
        setLoading(false);
        setReward(res.reward);
        res.reward !== null && shareBottomSheet.current?.present();
        updateRewardItem(res);
      }
    });
  };

  const renderSVG = (valueType: string) => {
    switch (valueType) {
      case IMPRINT_SCORES.LAUGHTER.toLocaleUpperCase():
        return (
          <View style={styles.imagePadding}>
            <LAUGHTER width={20} height={26} />
          </View>
        );
      case IMPRINT_SCORES.LIFE.toLocaleUpperCase():
        return (
          <View style={styles.imagePadding}>
            <LIFE width={20} height={20} />
          </View>
        );
      case IMPRINT_SCORES.LOVE.toLocaleUpperCase():
        return (
          <View style={styles.imagePadding}>
            <LOVE width={20} height={20} />
          </View>
        );
      case IMPRINT_SCORES.PURPOSE.toLocaleUpperCase():
        return (
          <View style={styles.imagePadding}>
            <PURPOSE width={20} height={20} />
          </View>
        );
      case IMPRINT_SCORES.RESPECT.toLocaleUpperCase():
        return (
          <View style={styles.imagePadding}>
            <RESPECT width={20} height={20} />
          </View>
        );
      case IMPRINT_SCORES.SAFETY.toLocaleUpperCase():
        return (
          <View style={styles.imagePadding}>
            <SAFETY width={26} height={26} />
          </View>
        );
      default:
        return <></>;
    }
  };

  const WelcomeMessage = () => {
    return (
      <View style={styles.welcomeContainer}>
        <View style={AppStyles.flexRow}>
          <View style={[styles.itemContainer]}>
            {renderSVG(route.params.item.category)}
            <Text color={Colors.black} size={'xxSmall'} type="medium">
              {util.capitalizeFirstLetter(
                route.params.item.category.toLocaleLowerCase(),
              )}
            </Text>
          </View>
          <View style={styles.itemContainer}>
            <Text color={Colors.black} size={Fonts.size.xxSmall}>
              {util.capitalizeFirstLetter(
                route.params.item.category.toLocaleLowerCase(),
              )}
              {ENTERTAINMENTS.QUIZ}
            </Text>
          </View>
        </View>
        <Text
          size={Fonts.size.Large}
          type="semi_bold"
          style={styles.topTitle}
          color={Colors.text.homeTitleColor}
          textAlign="left">
          {route.params.item.description.tagline}
        </Text>
        <Text color={Colors.text.modalText} size={'xSmall'} textAlign="left">
          {route.params.item.totalQuestions}
          {ENTERTAINMENTS.QUESTIONS}
        </Text>
      </View>
    );
  };

  return (
    <View style={AppStyles.flex}>
      <View
        style={
          hasNotch() ? styles.statusBar : styles.statusBarWithoutNotch
        }></View>
      <CustomNavbar
        title={ENTERTAINMENTS.QUIZ}
        hasBack={true}
        leftBtnPress={() => navigation.goBack()}
        hasRight={true}
        rightBtnImage={
          selectedAnswers.length === TotalQuestions ? (
            <TICK_ICON width={30} height={30} />
          ) : (
            <TICK_ICON_NEXT width={30} height={30} />
          )
        }
        rightBtnPress={
          selectedAnswers.length === TotalQuestions
            ? () => handleSubmit()
            : () => {}
        }
      />
      {WelcomeMessage()}

      <View style={styles.container}>
        <FlatList
          data={questions}
          keyExtractor={item => item.id}
          renderItem={({item}) => (
            <QuestionItem
              item={item}
              selectedAnswers={selectedAnswers}
              handleOptionPress={handleOptionPress}
            />
          )}
        />
        <Loader loading={loading} />
      </View>
      <ShareModal
        shareModalRef={shareBottomSheet}
        title={reward?.title}
        description={reward?.description}
        type={route.params.item.category}
        isBack={true}
      />
    </View>
  );
};

const mapStateToProps = (state: any) => ({
  user: state.user.userInfo,
});

const actions = {getEntertainmentQuiz, submitQuizRequest, updateRewardItem};

export default connect(mapStateToProps, actions)(EntertainmentQuiz);
