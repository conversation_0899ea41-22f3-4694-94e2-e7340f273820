// @flow
import {StyleSheet} from 'react-native';
import {Colors, Metrics} from '../../theme';
import {getStatusBarHeight} from 'react-native-iphone-x-helper';

export default StyleSheet.create({
  container: {
    flex: 1,
    padding: Metrics.ratio(20),
  },

  statusBar: {
    backgroundColor: Colors.white,
    height: getStatusBarHeight() + 30,
  },
  statusBarWithoutNotch: {
    backgroundColor: Colors.white,
    height: Metrics.ratio(30),
  },
  itemContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.white,
    flexDirection: 'row',
    borderRadius: Metrics.ratio(100),
    marginHorizontal: Metrics.ratio(8),
    paddingHorizontal: Metrics.ratio(15),
    paddingVertical: Metrics.ratio(2),
  },
  selectedColor: {backgroundColor: Colors.black1},
  imagePadding: {
    paddingRight: Metrics.ratio(4),
  },
  welcomeContainer: {
    padding: Metrics.ratio(24),
    backgroundColor: Colors.lightGray2,
    borderBottomEndRadius: Metrics.ratio(10),
    borderBottomStartRadius: Metrics.ratio(10),
  },
  topTitle: {
    marginTop: Metrics.ratio(20),
  },
});
