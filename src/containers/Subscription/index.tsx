import React, {useCallback, useEffect, useState} from 'react';
import {Alert, Platform, View, Switch} from 'react-native';
import {styles} from './styles';
import {SUBSCRIPTION} from '../../constants/StringConstants';
import {CustomNavbar, Loader, Text} from '../../components';
import {useNavigation, CommonActions} from '@react-navigation/native';
import Routes from '../../constants/RouteConstants';
import {Colors, Metrics} from '../../theme';
import {hasNotch} from 'react-native-device-info';
import SubscriptionItem from '../../components/SubcriptionItem';
import Carousel from 'react-native-snap-carousel';
import {connect} from 'react-redux';
import {
  getValidateCodeRequest,
  updateUserSubscriptionStatus,
  validateReceiptRequest,
  clearLocalSubscriptionUpdate,
  acceptTermsAndConditionRequest,
  userInfoRequest,
} from '../../actions/UserActions';
import {registrationSource, RootState, UserState} from '../../types';
import InviteCodeAlert from '../../components/VoucherAlert';
import NoInviteDisclaimerAlert from '../../components/NoInviteDisclaimer';

import {
  getSubscriptions,
  requestSubscription,
  getAvailablePurchases,
  initConnection,
  purchaseErrorListener,
  purchaseUpdatedListener,
  endConnection,
  clearTransactionIOS,
  setup,
  acknowledgePurchaseAndroid,
  type Purchase,
  type Subscription,
} from 'react-native-iap';

import {ProPlans, ProPlansYearly} from '../../constants';
import {isUserSubscribed} from '../../util';
import TermsModal from '../../components/TermsModal';
import PrivacyModal from '../../components/privacyModal';

const productSkus = Platform.select({
  android: ['basic_android', 'basic_yearly_android'],
  ios: ['Basic_plans', 'basic_plan_annual'],
});
export const constants = {
  productSkus,
};

export const APP_SKUS = productSkus;
interface subscriptionPlansProps {
  updateUserSubscriptionStatus: () => void;
  acceptTermsAndConditionRequest: (callback: (res: any) => void) => void;
  getValidateCodeRequest: (payload: any, callback: (res: any) => void) => void;
  user: UserState;
  userInfoRequest: (callback: (res: any) => void) => void;
}

const Subscription: React.FC<subscriptionPlansProps> = ({
  getValidateCodeRequest,
  updateUserSubscriptionStatus,
  acceptTermsAndConditionRequest,
  userInfoRequest,
  user,
}) => {
  const [subscriptionPlans, setSubscriptionPlans] = useState<
    (Subscription | (typeof ProPlans)[0])[]
  >([]);
  const [voucherModal, setVoucherModal] = useState<boolean>(false);
  const [voucherCode, setVoucherCode] = useState<string>('');
  const [disClaimer, setDisClaimer] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [purchaseLoading, setPurchaseLoading] = useState<boolean>(false);
  const [initializing, setInitializing] = useState<boolean>(true);
  const [iapConnected, setIapConnected] = useState<boolean>(false);
  const [purchaseUuid, setPurchasedUuid] = useState<string>('');
  const [showTermsModal, setShowTermsModal] = useState<boolean>(false);
  const [showPrivacyModal, setShowPrivacyModal] = useState<boolean>(false);

  const [connectionInProgress, setConnectionInProgress] =
    useState<boolean>(false);
  const [isYearlyToggle, setIsYearlyToggle] = useState<boolean>(false);

  const [errorText, setErrorText] = useState<boolean>(false);
  const [applyVoucher, setApplyVoucher] = useState<boolean>(false);

  const navigation = useNavigation();

  useEffect(() => {
    const initializeScreen = async () => {
      setLoading(true);

      try {
        const res = await getSubscriptions({skus: APP_SKUS || []});
        // Filter real subscription plans based on toggle
        const filteredRealPlans = res.flat().filter((plan: any) => {
          if (isYearlyToggle) {
            // Show yearly plans
            return (
              plan.productId?.includes('annual') ||
              plan.productId?.includes('yearly')
            );
          } else {
            // Show monthly plans
            return (
              !plan.productId?.includes('annual') &&
              !plan.productId?.includes('yearly')
            );
          }
        });

        // Add ProPlans based on toggle
        const proPlansToShow = isYearlyToggle ? ProPlansYearly : ProPlans;
        setSubscriptionPlans([...filteredRealPlans, ...proPlansToShow]);

        setInitializing(false);

        if (!isUserSubscribed(user) && !user.isAppliedVoucher) {
          setTimeout(() => {
            //  setVoucherModal(true);
          }, 800);
        }
      } catch (error) {
        console.error(SUBSCRIPTION.ERROR_LOADING_SUBSCRIPTIONS, error);
        setInitializing(false);
      } finally {
        setLoading(false);
      }
    };

    initializeScreen();
  }, [user, isYearlyToggle]);

  const ensureIAPConnection = async (retryCount = 0): Promise<boolean> => {
    const maxRetries = 2;

    // If already connected, return true
    if (iapConnected) {
      return true;
    }

    // If connection is in progress, wait for it
    if (connectionInProgress) {
      // Wait for connection to complete (max 5 seconds)
      let attempts = 0;
      while (connectionInProgress && attempts < 50) {
        await new Promise(resolve => setTimeout(resolve, 100));
        attempts++;
      }
      return iapConnected;
    }

    try {
      setConnectionInProgress(true);
      await initConnection();
      setIapConnected(true);
      return true;
    } catch (error) {
      console.error(SUBSCRIPTION.IAP_CONNECTION_ERROR, error);
      setIapConnected(false);

      // Retry logic
      if (retryCount < maxRetries) {
        console.log(
          `Retrying IAP connection, attempt ${retryCount + 1}/${maxRetries}`,
        );
        await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second before retry
        return ensureIAPConnection(retryCount + 1);
      }

      return false;
    } finally {
      setConnectionInProgress(false);
    }
  };

  // const proceedWithPurchase = async (subscription: any) => {
  //   if (user.userInfo.registrationSource === registrationSource.STRIPE) {
  //     Alert.alert(
  //       SUBSCRIPTION.SUBSCRIPTION_ERROR_TITLE,
  //       SUBSCRIPTION.STRIPE_USER_ERROR,
  //     );
  //     return;
  //   }

  //   if (!applyVoucher) {
  //     setVoucherModal(true);
  //     return;
  //   }

  //   if (
  //     user.userInfo.registrationSource === null ||
  //     user.userInfo.registrationSource === registrationSource.APPLE_PAY ||
  //     user.userInfo.registrationSource === registrationSource.GOOGLE_PAY
  //   ) {
  //     try {
  //       setPurchaseLoading(true);

  //       // Ensure IAP connection is ready before proceeding
  //       const connectionReady = await ensureIAPConnection();
  //       if (!connectionReady) {
  //         Alert.alert(
  //           SUBSCRIPTION.PURCHASE_FAILED_TITLE,
  //           SUBSCRIPTION.CONNECTION_FAILED_MESSAGE,
  //         );
  //         return;
  //       }

  //       const productId = subscription.productId;
  //       if (!productId) {
  //         Alert.alert(
  //           SUBSCRIPTION.PURCHASE_FAILED_TITLE,
  //           SUBSCRIPTION.INVALID_PRODUCT_MESSAGE,
  //         );
  //         return;
  //       }

  //       let requestModel: any = {};

  //       if (Platform.OS === 'ios') {
  //         requestModel = {
  //           sku: productId,
  //           appAccountToken:
  //             user?.userInfo?.uuid || SUBSCRIPTION.DEFAULT_USER_ID,
  //           andDangerouslyFinishTransactionAutomaticallyIOS: false,
  //         };
  //       } else {
  //         const offerToken =
  //           subscription.subscriptionOfferDetails?.[0]?.offerToken;
  //         requestModel = {
  //           sku: productId,
  //           ...(offerToken && {
  //             subscriptionOffers: [{sku: productId, offerToken}],
  //           }),
  //           developerPayloadAndroid: user?.userInfo?.email,
  //           obfuscatedAccountIdAndroid: user?.userInfo?.userId,
  //         };
  //       }

  //       // Brief delay to show loader, then initiate purchase
  //       await new Promise(resolve => setTimeout(resolve, 300));
  //       await requestSubscription(requestModel);

  //       // Success handling
  //       navigation.goBack();
  //       Alert.alert(
  //         SUBSCRIPTION.PURCHASE_SUCCESS_TITLE,
  //         SUBSCRIPTION.PURCHASE_SUCCESS_MESSAGE,
  //       );

  //       // Update subscription status
  //       updateUserSubscriptionStatus();
  //     } catch (error: any) {
  //       console.error('Purchase error:', error);

  //       const isUserCancellation =
  //         error?.message?.includes('cancelled') ||
  //         error?.message?.includes('canceled') ||
  //         error?.code === 'E_USER_CANCELLED';

  //       if (!isUserCancellation) {
  //         Alert.alert(
  //           SUBSCRIPTION.PURCHASE_FAILED_TITLE,
  //           error?.message || SUBSCRIPTION.PURCHASE_FAILED_MESSAGE,
  //         );

  //         // Reset connection state on error to allow retry
  //         setIapConnected(false);
  //         try {
  //           await endConnection();
  //         } catch (endError) {
  //           console.error('Error ending connection:', endError);
  //         }
  //       }
  //     } finally {
  //       setPurchaseLoading(false);
  //       if (Platform.OS === 'ios') {
  //         try {
  //           await clearTransactionIOS();
  //         } catch (clearError) {
  //           console.error('Error clearing iOS transactions:', clearError);
  //         }
  //       }
  //     }
  //   }
  // };

  const handlePurchase = async (subscription: any) => {
    if (purchaseLoading) return;

    const currentPlatform =
      Platform.OS === 'ios'
        ? registrationSource.APPLE_PAY
        : registrationSource.GOOGLE_PAY;

    const userPlatform = user?.userInfo?.registrationSource;
    const subscriptionStatus = user?.userInfo?.subscriptionStatus;

    const isSubscribed = isUserSubscribed(user);

    const planName =
      Platform.OS === 'ios' ? subscription?.title : subscription?.name;

    if (userPlatform === registrationSource.STRIPE) {
      Alert.alert(
        SUBSCRIPTION.SUBSCRIPTION_ERROR_TITLE,
        SUBSCRIPTION.STRIPE_USER_ERROR,
      );
      return;
    }

    if (purchaseUuid && purchaseUuid !== user.userInfo.uuid) {
      Alert.alert(
        'Apple id already used in different subscription',
        'please use the same Apple id to purchase subscription',
      );
      return;
    }

    if (userPlatform !== null && userPlatform !== currentPlatform) {
      const errorMessage =
        currentPlatform === registrationSource.APPLE_PAY
          ? SUBSCRIPTION.ALREADY_SUBSCRIBED_ON_ANDROID
          : SUBSCRIPTION.ALREADY_SUBSCRIBED_ON_IOS;

      Alert.alert(SUBSCRIPTION.SUBSCRIPTION_ERROR_TITLE, errorMessage);
      return;
    }

    // ✅ uncommented to Show voucher modal if applicable
    // if (!applyVoucher && !isUserSubscribed(user)) {
    //   setVoucherModal(true);
    //   return;
    // }

    if (user.userInfo.EULA === false) {
      setShowTermsModal(true);
      return;
    }

    // ✅ Proceed with purchase for Apple or Google
    if (
      userPlatform === null ||
      userPlatform === registrationSource.APPLE_PAY ||
      userPlatform === registrationSource.GOOGLE_PAY
    ) {
      try {
        setPurchaseLoading(true);

        const connectionReady = await ensureIAPConnection();
        if (!connectionReady) {
          Alert.alert(
            SUBSCRIPTION.PURCHASE_FAILED_TITLE,
            SUBSCRIPTION.CONNECTION_FAILED_MESSAGE,
          );
          return;
        }

        const productId = subscription.productId;
        if (!productId) {
          Alert.alert(
            SUBSCRIPTION.PURCHASE_FAILED_TITLE,
            SUBSCRIPTION.INVALID_PRODUCT_MESSAGE,
          );
          return;
        }

        let requestModel: any = {};

        if (Platform.OS === 'ios') {
          requestModel = {
            sku: productId,
            appAccountToken:
              user?.userInfo?.uuid || SUBSCRIPTION.DEFAULT_USER_ID,
            andDangerouslyFinishTransactionAutomaticallyIOS: false,
          };
        } else {
          const offerToken =
            subscription.subscriptionOfferDetails?.[0]?.offerToken;
          requestModel = {
            sku: productId,
            ...(offerToken && {
              subscriptionOffers: [{sku: productId, offerToken}],
            }),
            developerPayloadAndroid: user?.userInfo?.email,
            obfuscatedAccountIdAndroid: user?.userInfo?.userId,
          };
        }

        // Delay to allow UI to show loader
        await new Promise(resolve => setTimeout(resolve, 300));
        await requestSubscription(requestModel);
        updateUserSubscriptionStatus();

        // ✅ 5. Navigate to home tabs (which contains the home screen)
        navigation.dispatch(
          CommonActions.reset({
            index: 0,
            routes: [
              {
                name: Routes.HOME_TABS,
                state: {
                  index: 0, // Navigate to first tab (HOME)
                  routes: [{name: Routes.HOME_STACK}],
                },
              },
            ],
          }),
        );
        // ✅ Success - Purchase initiated, wait for purchaseUpdateListener to handle validation
      } catch (error: any) {
        console.error('Purchase error:', error);

        const isUserCancellation =
          error?.message?.includes('cancelled') ||
          error?.message?.includes('canceled') ||
          error?.code === 'E_USER_CANCELLED';

        if (!isUserCancellation) {
          Alert.alert(
            SUBSCRIPTION.PURCHASE_FAILED_TITLE,
            error?.message || SUBSCRIPTION.PURCHASE_FAILED_MESSAGE,
          );

          setIapConnected(false);
          try {
            await endConnection();
          } catch (endError) {
            console.error('Error ending connection:', endError);
          }
        }
      } finally {
        setPurchaseLoading(false);
        if (Platform.OS === 'ios') {
          try {
            await clearTransactionIOS();
          } catch (clearError) {
            console.error('Error clearing iOS transactions:', clearError);
          }
        }
      }
    }
  };

  useEffect(() => {
    const initializeIAP = async () => {
      try {
        setup({storekitMode: 'STOREKIT2_MODE'});
        setConnectionInProgress(true);
        await initConnection();
        setIapConnected(true);
      } catch (error) {
        console.error(SUBSCRIPTION.IAP_CONNECTION_ERROR, error);
        setIapConnected(false);
      } finally {
        setConnectionInProgress(false);
      }
    };

    initializeIAP();
    getPaymentHistory();

    const purchaseUpdateSubscription =
      purchaseUpdatedListener(handlePurchaseUpdate);

    const purchaseErrorSubscription = purchaseErrorListener(error => {
      console.error(SUBSCRIPTION.LISTENER_ERROR, error.message);
    });

    return () => {
      purchaseUpdateSubscription.remove();
      purchaseErrorSubscription.remove();
      // Clean up connection state on unmount
      setIapConnected(false);
      setConnectionInProgress(false);
    };
  }, []);

  // ✅ Receipt validation function will be called after backend implementation
  const validateReceiptWithBackend = async (purchase: Purchase) => {
    const receiptData = {
      platform: Platform.OS,
      receipt:
        Platform.OS === 'ios'
          ? purchase.transactionReceipt
          : purchase.purchaseToken,
      productId: purchase.productId,
      transactionId: purchase.transactionId,
      userId: user?.userInfo?.userId,
    };

    return new Promise((resolve, reject) => {
      validateReceiptRequest(receiptData, (response: any, error: any) => {
        if (response && response.isValid) {
          setTimeout(() => {
            clearLocalSubscriptionUpdate();
          }, 10000);
          resolve(response);
        } else {
          reject(error || new Error('Receipt validation failed'));
        }
      });
    });
  };

  const isAnnualProduct = (productId: string) => {
    return ProPlans.some(
      plan => plan.productId === productId && plan.isAnnual === true,
    );
  };

  const handlePurchaseUpdate = useCallback(async (purchase: Purchase) => {
    const {
      purchaseStateAndroid,
      purchaseToken,
      isAcknowledgedAndroid,
      transactionReceipt,
      transactionId,
    } = purchase;

    console.log('🔄 Purchase Update Received:', purchase);

    // ❌ Guard: Skip if no transaction receipt or token (likely old/stale)
    if (
      (!transactionReceipt || transactionReceipt.trim() === '') &&
      !purchaseToken
    ) {
      console.log(
        '⚠️ No transaction receipt. Likely no new purchase. Skipping...',
      );
      return;
    }

    // ❌ Guard: Ignore RENEWAL transactions (not first-time purchase)
    if (
      Platform.OS === 'ios' &&
      (purchase as any)?.transactionReasonIOS === 'RENEWAL'
    ) {
      console.log(
        'ℹ️ Skipping RENEWAL transaction from Apple (not a fresh purchase).',
      );
      return;
    }

    try {
      // ✅ 1. Acknowledge the purchase (required for Android)
      if (
        Platform.OS === 'android' &&
        purchaseStateAndroid === 1 &&
        !isAcknowledgedAndroid &&
        purchaseToken
      ) {
        await acknowledgePurchaseAndroid({token: purchaseToken});
        console.log('✅ Subscription acknowledged on Android');
      }

      // ❌ 2. Backend validation is currently disabled
      // const validationResponse = await validateReceiptWithBackend(purchase);
      // console.log('✅ Backend receipt validated:', validationResponse);

      // ✅ 3. Determine plan type
      const isAnnual = isAnnualProduct(purchase.productId);

      if (isAnnual) {
        // ❌ Cooling-off API disabled
        // await startCoolingOffPeriod(purchase, validationResponse);

        Alert.alert(
          SUBSCRIPTION.PURCHASE_SUCCESS_TITLE,
          'Annual plan purchase recorded (cooling-off will apply later).',
        );
      } else {
        // ✅ Monthly plan – activate immediately
        updateUserSubscriptionStatus();

        Alert.alert(
          SUBSCRIPTION.PURCHASE_SUCCESS_TITLE,
          SUBSCRIPTION.PURCHASE_SUCCESS_MESSAGE,
        );
      }

      // ✅ Navigate to home
      navigation.navigate(Routes.HOME_TABS as never);
    } catch (error) {
      console.error('❌ Purchase processing failed:', error);

      Alert.alert(
        SUBSCRIPTION.PURCHASE_FAILED_TITLE,
        SUBSCRIPTION.PURCHASE_FAILED_MESSAGE,
      );
    }
  }, []);

  const getPaymentHistory = async () => {
    try {
      const purchaseHistory = await getAvailablePurchases();
      console.log(SUBSCRIPTION.PAYMENT_HISTORY_LOG, purchaseHistory);

      if (purchaseHistory.length > 0) {
        setPurchasedUuid(
          purchaseHistory[0].appAccountToken?.toLocaleLowerCase() || '',
        );
      } else {
        console.error(SUBSCRIPTION.PAYMENT_HISTORY_LOG, purchaseHistory);
      }
    } catch (e) {
      console.error(SUBSCRIPTION.PAYMENT_HISTORY_ERROR, e);
    }
  };

  const onVoucherDone = (text: string) => {
    const payload = {
      code: text,
      purpose: SUBSCRIPTION.VOUCHER_PURPOSE,
    };
    getValidateCodeRequest(payload, (res: any) => {
      if (res?.isValid) {
        setVoucherCode(text);
        setVoucherModal(false);
        Alert.alert(SUBSCRIPTION.INVITE_CODE_APPROVE);
        setApplyVoucher(true);
      } else {
        setErrorText(true);
        setApplyVoucher(false);
      }
    });
  };

  return (
    <View style={styles.container}>
      <View
        style={
          hasNotch() ? styles.statusBar : styles.statusBarWithoutNotch
        }></View>
      <CustomNavbar
        title={SUBSCRIPTION.AMPLIFY_IMPRINT}
        hasBack={true}
        leftBtnPress={() => navigation.goBack()}
      />
      <View style={styles.contentContainer}>
        <Text
          textAlign="center"
          color={Colors.black}
          type="semi_bold"
          size={'medium'}
          style={styles.headingText}>
          {SUBSCRIPTION.SUBSCRIPTION_HEADING}
        </Text>
        <Text
          color={Colors.BlackButton}
          textAlign="center"
          style={styles.subHeading}>
          {SUBSCRIPTION.SUB_HEADING_TEXT}
        </Text>
      </View>

      <View style={styles.toggleContainer}>
        <Text
          style={[
            styles.toggleLabel,
            !isYearlyToggle && styles.toggleLabelActive,
          ]}>
          {SUBSCRIPTION.MONTHLY_LABEL}
        </Text>
        <Switch
          value={isYearlyToggle}
          onValueChange={setIsYearlyToggle}
          trackColor={{false: Colors.lightGray, true: Colors.black}}
          thumbColor={isYearlyToggle ? Colors.white : Colors.white}
          ios_backgroundColor={Colors.lightGray}
          style={styles.toggleSwitch}
        />
        <Text
          style={[
            styles.toggleLabel,
            isYearlyToggle && styles.toggleLabelActive,
          ]}>
          {SUBSCRIPTION.YEARLY_LABEL}
        </Text>
      </View>

      <View style={styles.carouselContainer}>
        <Carousel
          layout={'default'}
          data={[subscriptionPlans].flat()}
          renderItem={(item: {item: any}) => (
            <SubscriptionItem
              item={item.item}
              onPurchasedClick={item => handlePurchase(item)}
              user={user}
            />
          )}
          sliderWidth={Metrics.screenWidth}
          itemWidth={Metrics.screenWidth * 0.8}
          showSnap={true}
        />
      </View>

      <InviteCodeAlert
        onTyping={() => setErrorText(false)}
        errorText={errorText}
        key={
          user.isAppliedVoucher
            ? SUBSCRIPTION.VOUCHER_APPLIED_KEY
            : SUBSCRIPTION.VOUCHER_NOT_APPLIED_KEY
        }
        placeHolder={SUBSCRIPTION.VOUCHER_PLACEHOLDER}
        visible={voucherModal}
        onDone={text => onVoucherDone(text)}
        onClose={() => {
          setErrorText(false);
          setVoucherModal(false);
        }}
        onRequestClose={() => {
          setErrorText(false);
          setVoucherModal(false);
          setDisClaimer(true);
        }}
        handleNoCode={() => {
          setErrorText(false);
          setVoucherModal(false);

          setTimeout(() => {
            setDisClaimer(true);
          }, 500);
        }}
      />
      <NoInviteDisclaimerAlert
        visible={disClaimer}
        onDone={() => {
          setDisClaimer(false);
          (navigation.navigate as (route: string) => void)(Routes.USER_PROFILE);
        }}
        onClose={() => setDisClaimer(false)}
      />
      <TermsModal
        visible={showTermsModal}
        onCancel={() => setShowTermsModal(false)}
        onAccept={() => {
          setShowTermsModal(false);
          setShowPrivacyModal(true);
        }}
      />
      <PrivacyModal
        visible={showPrivacyModal}
        onCancel={() => setShowPrivacyModal(false)}
        onAccept={() => {
          setShowPrivacyModal(false);
          setTimeout(() => {
            acceptTermsAndConditionRequest(() => {
              userInfoRequest(() => {});
            });
          }, 100);
        }}
      />
      <Loader
        loading={(loading || purchaseLoading) && !voucherModal && !disClaimer}
      />
    </View>
  );
};

const mapStateToProps = (state: RootState) => ({
  user: state.user,
});

const actions = {
  updateUserSubscriptionStatus,
  getValidateCodeRequest,
  validateReceiptRequest,
  clearLocalSubscriptionUpdate,
  acceptTermsAndConditionRequest,
  userInfoRequest,
};

export default connect(mapStateToProps, actions)(Subscription);
