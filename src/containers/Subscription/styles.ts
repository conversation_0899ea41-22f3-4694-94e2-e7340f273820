import {Dimensions, StyleSheet} from 'react-native';
import {Colors, Fonts, Metrics} from '../../theme';
import {getStatusBarHeight} from 'react-native-iphone-x-helper';

export const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.background.home,
    flex: 1,
  },

  statusBar: {
    backgroundColor: Colors.white,
    height: getStatusBarHeight() + 30,
  },
  statusBarWithoutNotch: {
    backgroundColor: Colors.white,
    height: 30,
  },
  subHeading: {
    marginHorizontal: Metrics.baseMargin,
    marginTop: Metrics.ratio(10),
  },
  description: {
    marginHorizontal: Metrics.baseMargin,
    marginTop: Metrics.ratio(10),
  },
  hereButton: {
    fontStyle: 'italic',
    color: Colors.black,
    fontSize: Metrics.ratio(14),
  },
  // Content styling
  contentContainer: {
    // Container for main content area
  },
  headingText: {
    marginTop: 20, // Replaces AppStyles.mTop20
  },
  carouselContainer: {
    // Container for carousel component
  },

  modalContainer: {
    justifyContent: 'center',
    height: Metrics.screenHeight,
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    height: '70%',
    width: '80%',
    backgroundColor: Colors.white,
    borderRadius: 10,
    padding: 20,
    alignItems: 'center',
  },
  modalTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 10,
    color: Colors.black,
  },
  modalText: {
    fontSize: 12,
    textAlign: 'left',
    marginBottom: 20,
    color: Colors.black,
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  button: {
    flex: 1,
    padding: 10,
    borderRadius: 5,
    alignItems: 'center',
    marginHorizontal: 5,
  },
  cancelButton: {
    backgroundColor: Colors.lightGray1,
  },
  acceptButton: {
    backgroundColor: Colors.BlackButton,
  },
  buttonText: {
    color: Colors.white,
    fontWeight: 'bold',
  },

  // Toggle Switch Styles
  toggleContainer: {
    // backgroundColor: 'red',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    marginTop: Metrics.ratio(10),
    // marginVertical: Metrics.ratio(20),
    paddingHorizontal: Metrics.ratio(20),
  },
  toggleLabel: {
    fontSize: Fonts.size.xxSmall,
    color: Colors.gray,
    fontWeight: '500',
  },
  toggleLabelActive: {
    color: Colors.black,
    fontWeight: 'bold',
  },
  toggleSwitch: {
    backgroundColor: 'red',
    marginHorizontal: Metrics.ratio(5),
    transform: [{scaleX: 0.9}, {scaleY: 0.9}],
  },
});
