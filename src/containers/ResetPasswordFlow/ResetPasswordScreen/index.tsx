import _ from 'lodash';
import {connect} from 'react-redux';
import React, {useRef, useState} from 'react';
import {
  AppButton,
  CustomNavbar,
  TextInput,
  Text,
  Loader,
} from '../../../components';
import {
  EYE_CLOSE,
  EYE_OPEN,
  LOCK_ICON,
} from '../../../constants/AssetSVGConstants';
import {View, TextInput as RNTextInput} from 'react-native';
import styles from './styles';
import {Colors, Fonts} from '.././../../theme';
import {useNavigation} from '@react-navigation/native';
import {
  COMMON,
  RESET_PASSWORD,
  SIGN_UP,
  TOAST_MESSAGES,
  TOAST_TYPE,
} from '../../../constants/StringConstants';
import Util from '../../../util';
import {showToastMsg} from '../../../components/Alert';
import {hasNotch} from 'react-native-device-info';
import Routes from '../../../constants/RouteConstants';
import {resetPasswordRequest} from '../../../actions/UserActions';

interface ResetPassProps {
  route: {
    params: {
      email: string;
      otp: string;
    };
  };
  resetPasswordRequest: (payload: any, callback: (res: any) => void) => void;
}

const ResetPasswordScreen: React.FC<ResetPassProps> = ({
  route,
  resetPasswordRequest,
}) => {
  const navigation = useNavigation();
  const [secureText, setSecureText] = useState<boolean>(true);
  const [confirmSecureText, setConfirmSecureText] = useState<boolean>(true);

  const [password, setPassword] = useState<string>('');
  const [confirmPassword, setConfirmPassword] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);

  const passwordRef = useRef<RNTextInput>(null);
  const confirmPasswordRef = useRef<RNTextInput>(null);

  const _validateForm = () => {
    if (_.isEmpty(password)) {
      showToastMsg(TOAST_MESSAGES.PASSWORD_REQUIRED);
      return false;
    } else if (!Util.isPasswordValid(password)) {
      showToastMsg(TOAST_MESSAGES.VALID_PASSWORD, TOAST_TYPE.DEFAULT, 2500);
      return false;
    }

    if (_.isEmpty(confirmPassword)) {
      showToastMsg(TOAST_MESSAGES.CONFIRM_PASSWORD_REQUIRED);
      return false;
    } else if (password != confirmPassword) {
      showToastMsg(TOAST_MESSAGES.PASSWORD_SHOULD_BE_SAME);
      return false;
    } else {
      return true;
    }
  };

  const _onsubmitForm = () => {
    if (_validateForm()) {
      handleResetForm();
    }
  };
  const headerText = () => {
    return (
      <>
        <Text
          type="semi_bold"
          size={'large'}
          color={Colors.black}
          style={styles.headerText}>
          {RESET_PASSWORD.RESET_PASSWORD_DETAILS}
        </Text>
        <Text
          type="medium"
          size={Fonts.size.xxSmall}
          color={Colors.black2}
          style={styles.subHeaderText}>
          {RESET_PASSWORD.SUBMIT_PASSWORD}
        </Text>
      </>
    );
  };
  const handleResetForm = () => {
    /* comment this code as we are not using phone number in signup form */
    /* if (!valid || !formattedValue) {
      showToastMsg(TOAST_MESSAGES.INVALID_PHONE);
      return;
    }
   }/*/
    setLoading(true);
    const payload = {
      email: route.params.email.toLocaleLowerCase(),
      otp: route.params.otp.toString(),
      newPassword: confirmPassword,
    };
    resetPasswordRequest(payload, res => {
      if (res) {
        setTimeout(() => {
          setLoading(false);
          showToastMsg(
            TOAST_MESSAGES.PASSWORD_RESET_SUCCESS,
            TOAST_TYPE.SUCCESS,
            4000,
          );
          (navigation.navigate as (route: string) => void)(Routes.LOGIN);
        }, 2000);
      }
      setLoading(false);
    });
  };

  return (
    <View style={styles.container}>
      <View
        style={
          hasNotch() ? styles.statusBar : styles.statusBarWithoutNotch
        }></View>
      <CustomNavbar
        title={RESET_PASSWORD.RESET_PASSWORD}
        leftBtnPress={() =>
          navigation.reset({
            index: 0,
            routes: [
              {
                name: Routes.AUTH_STACK as never,
                state: {routes: [{name: Routes.LOGIN as never}]},
              },
            ],
          })
        }
      />
      {headerText()}
      <TextInput
        ref={passwordRef}
        containerStyle={styles.passwordInputContainer}
        label={SIGN_UP.PASSWORD_LABEL}
        leftImage={<LOCK_ICON />}
        rightImage={secureText ? <EYE_CLOSE /> : <EYE_OPEN />}
        onPress={() => setSecureText(!secureText)}
        secureTextEntry={secureText}
        onChangeText={val => setPassword(val)}
        onSubmitEditing={() => confirmPasswordRef.current?.focus()}
        placeholder={SIGN_UP.CONFIRM_PASSWORD_PLACEHOLDER}
      />
      <TextInput
        ref={confirmPasswordRef}
        containerStyle={styles.passwordInputContainer}
        label={SIGN_UP.CONFIRM_PASSWORD_LABEL}
        placeholder={SIGN_UP.CONFIRM_PASSWORD_PLACEHOLDER}
        leftImage={<LOCK_ICON />}
        rightImage={confirmSecureText ? <EYE_CLOSE /> : <EYE_OPEN />}
        onPress={() => setConfirmSecureText(!confirmSecureText)}
        onChangeText={val => setConfirmPassword(val)}
        secureTextEntry={confirmSecureText}
      />

      <AppButton
        text={COMMON.SUBMIT}
        onPress={_onsubmitForm}
        textColor={Colors.text.white}
      />
      <Loader loading={loading} />
    </View>
  );
};

const mapStateToProps = () => ({});

const actions = {resetPasswordRequest};

export default connect(mapStateToProps, actions)(ResetPasswordScreen);
