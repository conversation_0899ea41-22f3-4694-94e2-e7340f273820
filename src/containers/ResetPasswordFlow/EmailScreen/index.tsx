import _ from 'lodash';
import {connect} from 'react-redux';
import React, {useRef, useState} from 'react';
import {
  AppButton,
  CustomNavbar,
  Loader,
  Text,
  TextInput,
} from '../../../components';
import {EMAIL_ICON} from '../../../constants/AssetSVGConstants';
import {TextInput as RNTextInput, View} from 'react-native';
import styles from './styles';
import {Colors, Fonts, Metrics} from '../../../theme';
import {useNavigation} from '@react-navigation/native';
import {
  COMMON,
  RESET_PASSWORD,
  SIGN_UP,
  TOAST_MESSAGES,
} from '../../../constants/StringConstants';
import Util from '../../../util';
import {showToastMsg} from '../../../components/Alert';
import {hasNotch} from 'react-native-device-info';
import Routes from '../../../constants/RouteConstants';
import {sendOtpRequest} from '../../../actions/UserActions';
import {OTP_ENUMS} from '../../../types';

interface emailScreenProps {
  sendOtpRequest: (payload: any, callback: (res: any) => void) => void;
}

const SignUp: React.FC<emailScreenProps> = ({sendOtpRequest}) => {
  const navigation = useNavigation();
  const [secureText, setSecureText] = useState<boolean>(true);
  const [confirmSecureText, setConfirmSecureText] = useState<boolean>(true);

  const [email, setEmail] = useState<string>('');

  const [loading, setLoading] = useState<boolean>(false);

  const emailRef = useRef<RNTextInput>(null);

  const _validateForm = () => {
    if (_.isEmpty(email)) {
      showToastMsg(TOAST_MESSAGES.EMAIL_REQUIRED);
      return false;
    } else if (!Util.isEmailValid(email)) {
      showToastMsg(TOAST_MESSAGES.VALID_EMAIL);
      return false;
    } else {
      return true;
    }
  };

  const _onsubmitForm = async () => {
    if (_validateForm()) {
      onSendOtpRequest();
    }
  };

  const onSendOtpRequest = () => {
    const payload = {
      email: email.toLocaleLowerCase(),
      purpose: OTP_ENUMS.RESET_PASSWORD,
    };
    setLoading(true);
    sendOtpRequest(payload, res => {
      if (res) {
        (
          navigation.navigate as (
            route: string,
            param: {email: string; purpose: string},
          ) => void
        )(Routes.OTP_SCREEN, {
          email: email,
          purpose: OTP_ENUMS.RESET_PASSWORD,
        });
        setLoading(false);
      }
      setLoading(false);
    });
  };

  const headerText = () => {
    return (
      <>
        <Text
          type="semi_bold"
          size={'large'}
          color={Colors.black}
          style={styles.headerText}>
          {RESET_PASSWORD.RESET_PASSWORD_DETAILS}
        </Text>
        <Text
          type="medium"
          size={Fonts.size.xxSmall}
          color={Colors.black2}
          style={styles.subHeaderText}>
          {RESET_PASSWORD.SUBMIT_YOUR_EMAIL}
        </Text>
      </>
    );
  };

  return (
    <View style={styles.container}>
      <View
        style={
          hasNotch() ? styles.statusBar : styles.statusBarWithoutNotch
        }></View>
      <CustomNavbar
        title={RESET_PASSWORD.RESET_PASSWORD}
        leftBtnPress={() => navigation.goBack()}
      />
      {headerText()}
      <TextInput
        returnKeyLabel="submit"
        ref={emailRef}
        containerStyle={styles.emailInputContainer}
        label={SIGN_UP.EMAIL_LABEL}
        placeholder={SIGN_UP.EMAIL_PLACEHOLDER}
        leftImage={<EMAIL_ICON />}
        onChangeText={val => setEmail(val)}
        onSubmitEditing={() => _onsubmitForm()}
      />

      <AppButton
        text={COMMON.SUBMIT}
        onPress={_onsubmitForm}
        textColor={Colors.text.white}
        buttonStye={{marginHorizontal: Metrics.ratio(30)}}
      />

      <Loader loading={loading} />
    </View>
  );
};

const mapStateToProps = () => ({});

const actions = {sendOtpRequest};

export default connect(mapStateToProps, actions)(SignUp);
