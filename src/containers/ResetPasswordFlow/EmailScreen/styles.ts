// @flow
import {StyleSheet} from 'react-native';
import {Colors, Metrics, Fonts} from '../../../theme';
import {getStatusBarHeight} from 'react-native-iphone-x-helper';

export default StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: 0,
  },
  statusBar: {
    backgroundColor: Colors.white,
    height: getStatusBarHeight() + 30,
  },
  statusBarWithoutNotch: {
    backgroundColor: Colors.white,
    height: Metrics.ratio(30),
  },

  button: {
    marginTop: Metrics.ratio(30),
  },
  headerText: {
    color: Colors.black,
    marginTop: Metrics.ratio(40),
    marginBottom: Metrics.ratio(30),
    marginLeft: Metrics.ratio(30),
  },
  subHeaderText: {
    marginBottom: Metrics.ratio(40),
    marginHorizontal: Metrics.ratio(34),
  },
  emailInputContainer: {
    marginHorizontal: Metrics.ratio(22),
    marginBottom: Metrics.ratio(20),
  },
});
