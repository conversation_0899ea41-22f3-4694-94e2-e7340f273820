// @<PERSON><PERSON><PERSON>
import React, {useEffect, useRef, useState} from 'react';
import {
  ImageBackground,
  Pressable,
  View,
  Animated,
  Easing,
  Platform,
} from 'react-native';
import {CustomNavbar, Text} from '../../components';
import styles from './styles';
import {hasNotch} from 'react-native-device-info';
import {ANALYTICS} from '../../constants/StringConstants';
import {
  getValueAnalyticsRequest,
  getValuePortfolioRequest,
  getTimeOptionsRequest,
} from '../../actions/AnalyticsActions';
import {connect} from 'react-redux';
import {ScrollView} from 'react-native-gesture-handler';
import SymbolOptions from '../../components/SymbolOptions';
import Graph from '../../components/Graph';
import util from '../../util';
import {AppStyles, Metrics, Colors} from '../../theme';
import {ITimeOption, Score, UserState} from '../../types';
import {useIsFocused} from '@react-navigation/native';
import _ from 'lodash';
import AnalyticsTabs from '../../components/AnalyticsTab';

interface IAnalyticsProps {
  user: UserState;
  getValueAnalyticsRequest: (
    payload: any,
    callback: (res: any) => void,
  ) => void;
  getValuePortfolioRequest: (
    payload: any,
    callback: (res: any) => void,
  ) => void;
  getTimeOptionsRequest: (payload: any, callback: (res: any) => void) => void;
  analyticsValue: any;
  portfolioValue: Score;
  timeOption: ITimeOption[];
}

const Analytics: React.FC<IAnalyticsProps> = ({
  getValueAnalyticsRequest,
  getValuePortfolioRequest,
  getTimeOptionsRequest,
  analyticsValue,
  portfolioValue,
  user,
  timeOption,
}) => {
  const [isPersonal, setPersonal] = useState<boolean>(true);
  const [selectedTab, setSelectedTab] = useState<string>('overall');

  const [isValue, setValue] = useState<boolean>(true);
  const [valueData, setValueData] = useState<any>([]);
  const [optionSelected, setOptionSelected] = useState<string>('life');
  const AssignStoneValue = util.addScoresToStonesData(portfolioValue);
  const SortedStoneData = AssignStoneValue.sort(
    (a, b) => a.score - b.score,
  ).reverse();
  const [visibleIndexes, setVisibleIndexes] = useState<number[]>([]);
  const isFocued = useIsFocused();
  const [open, setOpen] = useState(false);
  const [dropDownValue, setdDropDownValue] = useState('0-cd');
  const [items, setItems] = useState([{}]);

  const valuesToSubstractForiOS = [80, 70, 225, 200, 170, 170, 135];
  const valuesToSubstractForAndroid = [30, 20, 175, 150, 120, 120, 85];

  // This section may be needed for future use if the client reverts back to the previous requirement
  // useEffect(() => {
  //   if (_.isEmpty(timeOption)) {
  //     const payload = {
  //       isGlobal: !isPersonal,
  //     };
  //     getTimeOptionsRequest(payload, (res: ITimeOption[]) => {
  //       const items = res.map(item => {
  //         return {label: item.text, value: item.value};
  //       });
  //       setItems(items);
  //     });
  //   }
  // }, []);

  useEffect(() => {
    if (!isValue) {
      const payload = {
        isGlobal: selectedTab === 'personal' ? false : true,
        valueType: optionSelected,
      };
      getValueAnalyticsRequest(payload, (res: any) => {});
    }
  }, [isValue, isPersonal, optionSelected, selectedTab]);

  useEffect(() => {
    if (analyticsValue && (analyticsValue?.valueAnalytics?.length ?? 0) > 0) {
      setValueData([...analyticsValue.valueAnalytics].reverse());
    }
  }, [analyticsValue]);

  useEffect(() => {
    if (
      isValue &&
      isFocued &&
      (selectedTab === 'personal' ||
        selectedTab === 'global' ||
        selectedTab === 'overall')
    ) {
      animatedValues.forEach(value => value.setValue(0));
      setVisibleIndexes([]);
      const payload = {
        isGlobal: selectedTab === 'personal' ? false : true,
        type: selectedTab === 'overall' ? 'overall' : 'imprints',
      };
      getValuePortfolioRequest(payload, (res: ITimeOption[]) => {});
    }
  }, [isPersonal, isValue, isFocued, selectedTab]);

  useEffect(() => {
    if (!isFocued) {
      animatedValues.forEach(value => value.setValue(0));
    }
  }, [isFocued]);

  useEffect(() => {
    if (!isFocued) {
      return;
    }
    setTimeout(() => {
      startAnimation();
    }, 500);
  }, [isValue, isPersonal, isFocued, dropDownValue, selectedTab]);

  useEffect(() => {
    if (!isFocued) {
      return;
    }
    SortedStoneData.forEach((_, index) => {
      setTimeout(
        () => {
          setVisibleIndexes(prevIndexes => [...prevIndexes, index]);
        },
        index < 3 ? 3000 * (index + 1) : 2500 * (index + 1),
      );
    });
  }, [isValue, isPersonal, isFocued, dropDownValue, selectedTab]);

  const animatedValues = useRef(
    SortedStoneData.map(() => new Animated.Value(0)),
  ).current;

  const startAnimation = () => {
    const animations = SortedStoneData.map((_, index) => {
      return Animated.timing(animatedValues[index], {
        toValue: 1,
        duration: 1500,
        easing: Easing.linear,
        useNativeDriver: true,
        delay: 500,
      });
    });
    Animated.sequence(animations).start();
  };

  const [backgroundColors] = useState(
    Array(SortedStoneData.length)
      .fill(Colors.white)
      .map(color => `${color}90`),
  );

  const togglePersonal = (selectedTab: string) => {
    if (selectedTab === 'Personal') return;
    // setPersonal(!isPersonal);
    setSelectedTab(selectedTab);

    animatedValues.forEach(value => value.setValue(0));
    setVisibleIndexes([]);
  };

  const toggleValue = (isValueSelected: boolean) => {
    if (isValueSelected === isValue) return;
    setValue(!isValue);
    setSelectedTab(isValue ? 'personal' : 'overall');
    animatedValues.forEach(value => value.setValue(0));
    setVisibleIndexes([]);
  };

  const renderAnalyticsButtons = () => {
    return (
      <View style={styles.buttonContainer}>
        <Pressable
          style={isValue ? styles.selectedButton : styles.unSelectedButton}
          onPress={() => toggleValue(true)}>
          <Text style={isValue ? styles.selectedText : styles.unSelectedText}>
            {ANALYTICS.VALUE_PROFILE}
          </Text>
        </Pressable>
        <Pressable
          style={!isValue ? styles.selectedButton : styles.unSelectedButton}
          onPress={() => toggleValue(false)}>
          <Text style={!isValue ? styles.selectedText : styles.unSelectedText}>
            {ANALYTICS.ANALYTICS}
          </Text>
        </Pressable>
      </View>
    );
  };

  const onOptionClicked = (option: string) => {
    setOptionSelected(option);
  };

  const refresh = () => {
    animatedValues.forEach(value => value.setValue(0));
    setVisibleIndexes([]);
    SortedStoneData.forEach((_, index) => {
      setTimeout(
        () => {
          setVisibleIndexes(prevIndexes => [...prevIndexes, index]);
        },
        index < 3 ? 3000 * (index + 1) : 2500 * (index + 1),
      );
    });
    const payload = {
      isGlobal: selectedTab == 'personal' ? false : true,
    };
    getValuePortfolioRequest(payload, (res: ITimeOption[]) => {
      setTimeout(() => {
        startAnimation();
      }, 500);
    });
  };

  return (
    <View style={styles.container}>
      <View
        style={
          hasNotch() ? styles.statusBar : styles.statusBarWithoutNotch
        }></View>
      <CustomNavbar
        title={ANALYTICS.USER_ANALYTICS}
        titleSize={16}
        hasMultiRight={true}
        hasBack={false}
        rightBtnPress1={() => refresh()}
      />
      {renderAnalyticsButtons()}

      <AnalyticsTabs
        onOverallPress={isValue ? () => togglePersonal('overall') : undefined}
        onPersonalPress={() => togglePersonal('personal')}
        onGlobalPress={() => togglePersonal('global')}
        isPersonalSelect={selectedTab === 'personal'}
        isGlobalSelect={selectedTab === 'global'}
        isOverallSelect={selectedTab === 'overall'}
      />

      {!isValue && valueData && valueData.length > 0 ? (
        <ScrollView
          style={styles.scrollContainer}
          showsVerticalScrollIndicator={false}>
          <View style={styles.titleContainer}>
            <Text style={styles.titleText}>{ANALYTICS.SENTIMENT}</Text>
          </View>
          <Graph valueData={valueData} optionSelected={optionSelected} />
          <View style={styles.optionContainer}>
            <SymbolOptions
              onOptionClicked={onOptionClicked}
              optionSelected={optionSelected}
            />
          </View>
        </ScrollView>
      ) : (
        <ScrollView
          showsVerticalScrollIndicator={false}
          style={styles.stoneScroll}>
          {/* This section may be needed for future use if the client reverts back to the previous requirement
          <View style={styles.stoneTitleContainer}>
            <Text style={styles.portFolioText}>
              {ANALYTICS.PORTFOLIO_TITLE}
            </Text>
            <View style={styles.dropDownContainer}>
              <DropDownPicker
                listMode="SCROLLVIEW"
                open={open}
                value={dropDownValue}
                items={items}
                setOpen={setOpen}
                setValue={setdDropDownValue}
                setItems={setItems}
                style={{
                  backgroundColor: Colors.gray2,
                }}
              />
            </View>
          </View> */}
          <ImageBackground
            style={styles.cover}
            source={require('../../assets/icons/cover/cover.png')}>
            {SortedStoneData.map((item, index) => {
              const translateY = animatedValues[index].interpolate({
                inputRange: [0, 1],

                outputRange: (() => {
                  switch (index) {
                    case 0:
                      return [
                        -Metrics.screenHeight * 0.2,
                        Metrics.screenHeight * 0.4 -
                          (Platform.OS === 'ios' ? 50 : 20),
                      ];
                    case 1:
                      return [
                        -Metrics.screenHeight * 0 - 200,
                        Metrics.screenHeight * 0.4 -
                          170 -
                          (Platform.OS === 'ios' ? 40 : 10),
                      ];
                    case 2:
                      return [
                        -Metrics.screenHeight * 0 - 400,
                        Metrics.screenHeight * 0.4 -
                          170 -
                          (Platform.OS === 'ios' ? 195 : 165),
                      ];
                    case 3:
                      return [
                        -Metrics.screenHeight * 0 - 600,
                        Metrics.screenHeight * 0.4 -
                          170 -
                          170 -
                          (Platform.OS === 'ios' ? 170 : 140),
                      ];
                    case 4:
                      return [
                        -Metrics.screenHeight * 0 - 800,
                        Metrics.screenHeight * 0.4 -
                          170 -
                          170 -
                          170 -
                          (Platform.OS === 'ios' ? 140 : 110),
                      ];
                    case 5:
                      return [
                        -Metrics.screenHeight * 0 - 1000,
                        Metrics.screenHeight * 0.4 -
                          170 -
                          170 -
                          170 -
                          170 -
                          (Platform.OS === 'ios' ? 105 : 80),
                      ];

                    default:
                      return [0, 0];
                  }
                })(),
                extrapolate: 'identity',
              });

              return (
                <Animated.View
                  key={item.id}
                  style={[
                    styles.animatedView,
                    {
                      transform: [{translateY}],
                    },
                  ]}>
                  <View style={styles.imageContainer}>
                    <item.image height={100} width={190 - index * 20} />
                  </View>
                  {visibleIndexes.includes(index as never) && (
                    <View
                      id={index.toString()}
                      style={[
                        styles.textContainer,
                        {backgroundColor: backgroundColors[index]},
                      ]}>
                      <Text
                        color={Colors.black}
                        textAlign="left"
                        size={'xxxSmall'}
                        style={AppStyles.mLeft5}>
                        <Text
                          color={Colors.black}
                          size={'xxSmall'}
                          type="regular"
                          style={AppStyles.mRight5}>
                          {util.capitalizeFirstLetter(item.text)}:
                        </Text>
                        {item.score}%
                      </Text>
                    </View>
                  )}
                </Animated.View>
              );
            })}
          </ImageBackground>
        </ScrollView>
      )}
    </View>
  );
};

const mapStateToProps = (state: any) => ({
  analyticsValue: state.analytics.value,
  portfolioValue: state.analytics.portfolio,
  timeOption: state.analytics.timeOption,
  user: state.user,
});

export default connect(mapStateToProps, {
  getValueAnalyticsRequest,
  getValuePortfolioRequest,
  getTimeOptionsRequest,
})(Analytics);
