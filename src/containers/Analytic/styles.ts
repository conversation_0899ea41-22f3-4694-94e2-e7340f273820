import {StyleSheet} from 'react-native';
import {Colors, Fonts, Metrics} from '../../theme';
import {getStatusBarHeight} from 'react-native-iphone-x-helper';

export default StyleSheet.create({
  container: {
    backgroundColor: Colors.background.home,
  },
  statusBar: {
    backgroundColor: Colors.white,
    height: Metrics.ratio(getStatusBarHeight() + 30),
  },
  statusBarWithoutNotch: {
    backgroundColor: Colors.white,
    height: Metrics.ratio(30),
  },
  buttonContainer: {
    height: Metrics.ratio(72),
    marginTop: Metrics.ratio(2),
    backgroundColor: 'white',
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: Metrics.ratio(24),
  },
  selectedButton: {
    backgroundColor: Colors.gray3,
    height: Metrics.ratio(38),
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: Metrics.ratio(6),
  },
  unSelectedButton: {
    backgroundColor: Colors.white,
    height: Metrics.ratio(38),
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: Metrics.ratio(6),
  },
  cover: {
    zIndex: -1,
    overflow: 'hidden',
    width: Metrics.ratio(Metrics.screenWidth - 48),
    height: Metrics.ratio(410),
    alignSelf: 'center',
  },
  animatedView: {
    alignItems: 'center',
    flexDirection: 'row',
  },
  imageContainer: {
    flex: 1,
    alignItems: 'center',
    marginRight: Metrics.doubleBaseMargin,
  },
  textContainer: {
    width: Metrics.ratio(105),
    right: 0,
    borderRadius: Metrics.ratio(8),
    position: 'absolute',
  },
  selectedText: {
    fontFamily: Fonts.type.semi_bold,
    fontSize: Metrics.ratio(16),
    paddingHorizontal: Metrics.ratio(16),
    color: Colors.black1,
  },
  unSelectedText: {
    fontFamily: Fonts.type.semi_bold,
    fontSize: Metrics.ratio(16),
    paddingHorizontal: Metrics.ratio(16),
    color: Colors.tabsTextColor.inActiveTabColor,
  },
  optionContainer: {
    backgroundColor: 'white',
    width: Metrics.ratio(Metrics.screenWidth - 30),
    alignSelf: 'center',
    borderBottomLeftRadius: Metrics.ratio(16),
    borderBottomRightRadius: Metrics.ratio(16),
    paddingBottom: Metrics.ratio(100),
  },
  graph: {
    backgroundColor: Colors.white,
    alignSelf: 'center',
    paddingHorizontal: Metrics.ratio(10),
  },
  titleText: {
    fontFamily: Fonts.type.regular,
    fontSize: Metrics.ratio(16),
    color: Colors.toggleColors.activeColor,
    paddingLeft: Metrics.ratio(20),
  },
  titleContainer: {
    backgroundColor: Colors.white,
    width: Metrics.ratio(Metrics.screenWidth - 30),
    height: Metrics.ratio(60),
    alignSelf: 'center',
    justifyContent: 'center',
    borderTopLeftRadius: Metrics.ratio(16),
    borderTopRightRadius: Metrics.ratio(16),
  },
  optionMainContainer: {
    backgroundColor: 'white',
  },
  optionInnerContainer: {
    backgroundColor: 'white',
    flexDirection: 'row',
  },
  optionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: Metrics.ratio(10),
    borderRadius: Metrics.ratio(4),
  },
  dot: {
    width: Metrics.ratio(10),
    height: Metrics.ratio(10),
    borderRadius: Metrics.ratio(100),
    marginHorizontal: Metrics.ratio(10),
  },
  optionText: {
    fontFamily: Fonts.type.regular,
    fontSize: Metrics.ratio(14),
    color: Colors.text.homeTitleColor,
    paddingVertical: Metrics.ratio(8),
    marginRight: Metrics.ratio(10),
  },
  scrollContainer: {
    marginTop: Metrics.ratio(24),
    marginBottom: Metrics.ratio(Metrics.screenHeight * 0.42),
  },
  portFolioText: {
    width: Metrics.screenWidth - 220,
    marginRight: Metrics.ratio(10),
    color: Colors.toggleColors.activeColor,
    fontFamily: Fonts.type.regular,
    fontSize: Metrics.ratio(16),
  },
  stoneScroll: {
    backgroundColor: 'white',
    marginTop: Metrics.ratio(20),
    marginBottom: Metrics.ratio(Metrics.screenHeight * 0.42),
    marginHorizontal: Metrics.ratio(24),
    borderRadius: Metrics.ratio(16),
  },
  stoneTitleContainer: {
    flexDirection: 'row',
    paddingTop: Metrics.ratio(16),
    paddingHorizontal: Metrics.ratio(24),
    paddingBottom: Metrics.ratio(20),
    alignItems: 'center',
  },
  dropDownContainer: {
    width: Metrics.ratio(130),
  },
});
