import React from 'react';
import {Linking, View} from 'react-native';
import {CustomNavbar} from '../../components';
import styles from './styles';
import {hasNotch} from 'react-native-device-info';
import {
  ABOUT_IMPRINT,
  DOWN_ARROW,
  HELP,
  RIGHT_ICON,
  SETTINGS,
  SUBSCRIPTION,
} from '../../constants/AssetSVGConstants';
import {getZendeskTokenRequest, userLogout} from '../../actions/UserActions';
import ProfileOption from '../../components/ProfileOption';
import {IProfileOptionObject, UserState} from '../../types';
import {connect} from 'react-redux';
import {
  PROFILE,
  TOAST_MESSAGES,
  TOAST_TYPE,
} from '../../constants/StringConstants';
import {useIsFocused, useNavigation} from '@react-navigation/native';
import Routes from '../../constants/RouteConstants';
import FastImage from 'react-native-fast-image';
import {showToastMsg} from '../../components/Alert';
import {resetTimeLine} from '../../actions/TimelineActions';
import {Fonts} from '../../theme';
import {
  ABOUT_IMPRINT_PAGE,
  return_to,
  TOAST_VISIBILITY_TIMEOUT,
} from '../../constants';
import {isUserSubscribed} from '../../util';

interface ProfileProps {
  userLogout: () => void;
  resetTimeLine: () => void;
  getZendeskTokenRequest: (res: any) => void;
  user: UserState;
}

const Profile: React.FC<ProfileProps> = ({
  user,
  userLogout,
  resetTimeLine,
  getZendeskTokenRequest,
}) => {
  const navigation = useNavigation();
  const isFocused = useIsFocused();

  // useEffect(() => {
  //   if (!isFocused) {
  //     resetTimeLine();
  //   }
  // }, [isFocused]);

  const userObject: IProfileOptionObject[] = [
    {
      leftImage: user.userInfo.avatarUrl,
      name: user.userInfo.userName,
      description: PROFILE.MAIN_ACCOUNT,
      rightImage: <DOWN_ARROW />,
    },
  ];

  const logoutObject: IProfileOptionObject[] = [
    {
      leftImage: null,
      name: PROFILE.LOGOUT,
      description: '',
      rightImage: null,
    },
  ];

  const otherOptions: IProfileOptionObject[] = [
    {
      leftImage: <SUBSCRIPTION />,
      name: PROFILE.SUBSCRIPTION,
      description: PROFILE.IMPRINTS_WITHOUT_LIMIT,
      rightImage: <RIGHT_ICON />,
    },
    {
      leftImage: <ABOUT_IMPRINT width={20} height={20} />,
      name: PROFILE.ABOUT_IMPRINT,
      description: PROFILE.STORY_ABOUT,
      rightImage: <RIGHT_ICON />,
    },
    {
      leftImage: <SETTINGS />,
      name: PROFILE.SETTINGS,
      description: PROFILE.ACCESS_WIDGET_SETTINGS,
      rightImage: <RIGHT_ICON />,
    },

    {
      leftImage: <HELP />,
      name: PROFILE.HELP,
      description: PROFILE.CONTACT_OUR_SUPPORT,
      rightImage: <RIGHT_ICON />,
    },
  ];

  const onOptionClicked = (option: IProfileOptionObject) => {
    switch (option.name) {
      case user.userInfo.userName:
        (navigation.navigate as (route: string) => void)(Routes.USER_PROFILE);
        break;
      case PROFILE.SETTINGS:
        showToastMsg('Settings Clicked');
        break;
      case PROFILE.HELP:
        !isUserSubscribed(user)
          ? showToastMsg(
              TOAST_MESSAGES.SUBSCRIPTION_ERROR,
              TOAST_TYPE.DEFAULT,
              TOAST_VISIBILITY_TIMEOUT,
              'top',
            )
          : navigateToHelpCenter();
        break;
      case PROFILE.DISPLAY_ACCESSIBILITY:
        showToastMsg('Display & Accessibility Clicked');
        break;
      case PROFILE.ABOUT_IMPRINT:
        (navigation.navigate as (route: string, param: any) => void)(
          Routes.REPORT_SCREEN,
          {
            url: ABOUT_IMPRINT_PAGE,
            title: PROFILE.ABOUT_IMPRINT,
          },
        );
        break;
      case PROFILE.SUBSCRIPTION:
        (navigation.navigate as (route: string) => void)(Routes.SUBSCRIPTION);

        break;

      case PROFILE.LOGOUT:
        logout();
        break;
      default:
        break;
    }
  };

  const logout = () => {
    userLogout();
    FastImage.clearMemoryCache();
    FastImage.clearDiskCache();
    navigation.reset({
      index: 0,
      routes: [
        {
          name: Routes.AUTH_STACK as never,
          state: {routes: [{name: Routes.LOGIN as never}]},
        },
      ],
    });
  };

  const navigateToHelpCenter = () => {
    getZendeskTokenRequest((res: boolean) => {
      if (res) {
        console.log('RES', res);
        (
          navigation.navigate as (
            route: string,
            params: {url: string; title: string},
          ) => void
        )(Routes.REPORT_SCREEN, {
          url: `https://imprint-live.zendesk.com/access/jwt?jwt=${res}&return_to=${return_to}`,
          title: 'Help Center',
        });
      }
    });
  };

  return (
    <View style={styles.container}>
      <View
        style={
          hasNotch() ? styles.statusBar : styles.statusBarWithoutNotch
        }></View>
      <CustomNavbar
        title={PROFILE.MENU}
        titleSize={Fonts.size.large}
        alignTitleLeft
        hasBack={false}
        hasMultiRight={true}
        rightBtnPress2={() => alert('messagePress')}
      />
      <ProfileOption data={userObject} onPress={onOptionClicked} />
      <ProfileOption data={otherOptions} onPress={onOptionClicked} />
      <ProfileOption data={logoutObject} onPress={onOptionClicked} />
    </View>
  );
};

const mapStateToProps = (state: any) => ({
  user: state.user,
});

export default connect(mapStateToProps, {
  userLogout,
  resetTimeLine,
  getZendeskTokenRequest,
})(Profile);
