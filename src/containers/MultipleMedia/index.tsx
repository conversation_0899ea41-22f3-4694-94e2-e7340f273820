// @flow

import React, {useRef, useState} from 'react';
import {View, Image, ActivityIndicator} from 'react-native';
import {CustomNavbar, Loader} from '../../components';
import styles from './styles';
import {CROSS_ROUND} from '../../constants/AssetSVGConstants';
import {AppStyles, Colors, Metrics} from '../../theme';
import _ from 'lodash';
import {SafeAreaView} from 'react-native-safe-area-context';
import Video from 'react-native-video';
import {ImprintMedia, MediaItem} from '../../types';
import {useNavigation} from '@react-navigation/native';
import {IMPRINT_MEDIA_TYPES_MAP} from '../../constants';
import Handle from '../../components/TimeLineMediaView/Handle';
import FastImage from 'react-native-fast-image';
import {IMPRINT_MEDIA} from '../../constants/StringConstants';

interface ListProps {
  route: {
    params: {
      selectedItem: ImprintMedia;
      mediaList: MediaItem[];
    };
  };
}

const MultipleMedia: React.FC<ListProps> = ({route}) => {
  const {selectedItem, mediaList} = route.params;

  const [Item, setItem] = useState<ImprintMedia>(selectedItem);
  const [loading, setLoading] = useState<boolean>(false);

  const navigation = useNavigation();

  let Index = _.findIndex(mediaList, o => o.id === Item.id);

  const videoRef = useRef(null);

  const onPressRight = () => {
    if (Index < mediaList.length - 1) {
      const updatedIndex = Index + 1;
      const selectedItem = mediaList[updatedIndex];
      setItem(selectedItem);
    }
  };

  const onPressLeft = () => {
    if (Index > 0) {
      const updatedIndex = Index - 1;
      const selectedItem = mediaList[updatedIndex];
      setItem(selectedItem);
    }
  };

  return (
    <SafeAreaView style={AppStyles.flex}>
      <CustomNavbar
        leftBtnPress={() => navigation.goBack()}
        leftBtnImage={<CROSS_ROUND />}
        style={{backgroundColor: Colors.black1}}
        title={`${Index + 1} of ${mediaList.length}`}
        titleColor={Colors.whit1}
      />
      <View style={styles.container}>
        <>
          {mediaList.length > 1 && (
            <View style={styles.detailView}>
              <Handle
                onPressLeft={onPressLeft}
                onPressRight={onPressRight}
                overrideStyle={styles.handleOverrideStyle}
              />
            </View>
          )}

          {Item.type === IMPRINT_MEDIA.VIDEO ? (
            <>
              {loading ? (
                <View style={styles.videoView}>
                  <ActivityIndicator size={'large'} />
                </View>
              ) : null}
              <Video
                key={Item.id}
                onLoadStart={() => setLoading(true)}
                resizeMode="cover"
                controls
                paused={false}
                volume={2.0}
                source={{
                  uri: Item.url,
                }}
                fullscreen={true}
                ref={videoRef}
                style={styles.images}
                onLoad={() => setLoading(false)}
              />
            </>
          ) : (
            <FastImage source={{uri: Item.url}} style={styles.images} />
          )}
        </>
      </View>
    </SafeAreaView>
  );
};

export default MultipleMedia;
