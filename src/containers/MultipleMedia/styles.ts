// @flow
import {StyleSheet} from 'react-native';
import {Colors, Metrics} from '../../theme';

export default StyleSheet.create({
  container: {
    justifyContent: 'center',
    flex: 1,
    backgroundColor: Colors.black1,
  },

  images: {
    height: Metrics.screenHeight / 2,
    width: Metrics.screenWidth,
  },
  detailView: {
    bottom: Metrics.screenHeight * 0.45,
    justifyContent: 'center',
    alignSelf: 'center',
    zIndex: 99,
    position: 'absolute',
    width: Metrics.screenWidth,
  },
  videoView: {position: 'absolute', alignSelf: 'center'},
  handleOverrideStyle: {bottom: null},
});
