import _ from 'lodash';
import {connect} from 'react-redux';
import React, {useEffect, useState} from 'react';
import {AppButton, CustomNavbar, Loader, Text} from '../../components/';

import {View, FlatList, SafeAreaView} from 'react-native';
import styles from './styles';
import {AppStyles, Colors, Metrics} from '../../theme';
import {
  userFriendsSuggestionRequest,
  followFriendsRequest,
} from '../../actions/UserActions';
import {CommonActions, useNavigation} from '@react-navigation/native';
import {SUGGESTED_FRIENDS} from '../../constants/StringConstants';

import Routes from '../../constants/RouteConstants';
import {showToastMsg} from '../../components/Alert';
import SuggestedItem from '../../components/FriendsComponents/SuggestedItem';
import util from '../../util';

interface ActionsProps {
  userFriendsSuggestionRequest: (callback: (res: any) => void) => void;
  followFriendsRequest: (payload: any, callback: (res: any) => void) => void;
  // Add other props if needed
}

interface ItemProps {
  item: {
    id: string;
    avatarUrl: string;
    displayName: string;
    email: string;
    userName: string;
  };
}

const SuggestedFriendsListing: React.FC<ActionsProps> = ({
  userFriendsSuggestionRequest,
  followFriendsRequest,
}) => {
  const navigation = useNavigation();
  const [loading, setLoading] = useState<boolean>(false);
  const [mainLoading, setMainLoading] = useState<boolean>(false);

  const [suggestedFriendsList, setFriendsList] = useState<ItemProps[]>([]);
  const [selectedItems, setSelectedItems] = useState<string[]>([]);

  const renderItem = ({item}: ItemProps) => {
    return (
      <SuggestedItem
        item={item}
        onItemPress={() => handleItemClick(item.id)}
        selectedItems={selectedItems}
      />
    );
  };

  const handleItemClick = (itemId: string) => {
    const selectedIndex = selectedItems.indexOf(itemId);
    let newSelectedItems = [...selectedItems];

    if (selectedIndex === -1) {
      newSelectedItems.push(itemId);
    } else {
      newSelectedItems.splice(selectedIndex, 1);
    }

    setSelectedItems(newSelectedItems);
  };

  useEffect(() => {
    fetchList();
  }, []);

  const fetchList = () => {
    try {
      setLoading(true);
      userFriendsSuggestionRequest((data: any) => {
        if (data) {
          setLoading(false);
          setFriendsList(data);
        }
      });
      // Make the API call
    } catch (error) {
      console.error('Error fetching data:', error);
    }
  };
  const handleNavigation = () => {
    // Reset the navigation stack to the user profile screen

    util.resetToSubscriptions(navigation);
  };

  const footerComponent = () => {
    return (
      <View style={styles.buttonContainer}>
        <AppButton
          buttonStye={styles.finishButton}
          text={SUGGESTED_FRIENDS.FINISH}
          textColor={Colors.white}
          onPress={submitFollowRequest}
        />
        <AppButton
          buttonStye={styles.retakeButton}
          text={SUGGESTED_FRIENDS.SKIP}
          onPress={handleNavigation}
        />
      </View>
    );
  };

  const submitFollowRequest = () => {
    if (_.isEmpty(selectedItems)) {
      showToastMsg('Please select atleast one friend');
    } else {
      setMainLoading(true);
      for (const id of selectedItems) {
        const payload = [
          {
            to: id,
          },
        ];

        followFriendsRequest(payload, (res: any) => {
          setMainLoading(false);
          if (res) {
            handleNavigation();
          }
        });
      }
    }
  };

  const HeaderText = () => {
    return (
      <Text
        size={'normal'}
        textAlign="center"
        style={{
          margin: Metrics.ratio(20),
        }}
        color={Colors.text.homeTitleColor}
        type="bold">
        Connecting with people
        <Text color={Colors.black} style={{fontWeight: 'normal'}}>
          {SUGGESTED_FRIENDS.HEADING}
        </Text>
      </Text>
    );
  };

  return (
    <SafeAreaView style={AppStyles.flex}>
      <CustomNavbar title={SUGGESTED_FRIENDS.TITLE} hasRight hasBack={false} />
      <FlatList
        contentContainerStyle={[
          AppStyles.alignStart,
          AppStyles.alignSelfCenter,
        ]}
        data={suggestedFriendsList}
        refreshing={loading}
        onRefresh={() => fetchList()}
        renderItem={renderItem}
        showsVerticalScrollIndicator={false}
        numColumns={2}
        ListHeaderComponent={HeaderText}
      />
      {footerComponent()}
      <Loader loading={mainLoading} />
    </SafeAreaView>
  );
};

const mapStateToProps = () => ({});

const actions = {userFriendsSuggestionRequest, followFriendsRequest};

export default connect(mapStateToProps, actions)(SuggestedFriendsListing);
