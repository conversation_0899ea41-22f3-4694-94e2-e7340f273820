import _ from 'lodash';
import {connect} from 'react-redux';
import React, {useEffect, useMemo, useRef, useState} from 'react';
import {FollowerState, RootState, User, UserState} from '../../types'; // Assuming RootState contains the type definition of your root state
import {
  CreateChatItem,
  CustomNavbar,
  EmptyStateComponent,
  Loader,
  SearchBar,
  Text,
} from '../../components/';
import {
  View,
  Platform,
  Pressable,
  Alert,
  Linking,
  Image,
  Modal,
} from 'react-native';
import styles from './styles';
import {Colors, Metrics} from '../../theme';
import {getUserFriendsRequest} from '../../actions/FollowersActions';
import {createChatGroupRequest} from '../../actions/ChatActions';
import {isRefreshPage} from '../../actions//UserActions';
import {useNavigation} from '@react-navigation/native';
import {
  CHAT_LIST,
  FRIENDS,
  TOAST_MESSAGES,
  TOAST_TYPE,
} from '../../constants/StringConstants';
import {showToastMsg} from '../../components/Alert';
import {FORWARD_ICON, GROUP_CAMERA} from '../../constants/AssetSVGConstants';
import {BottomSheetModal, BottomSheetModalProvider} from '@gorhom/bottom-sheet';
import {ScrollView, TextInput} from 'react-native-gesture-handler';
import ImageCropPicker from 'react-native-image-crop-picker';
import GroupDetails from '../../components/ChatComponents/GroupDetails';
import {FlatList} from 'react-native-gesture-handler';
import Routes from '../../constants/RouteConstants';

interface ActionsProps {
  getUserFriendsRequest: (callback: (res: any) => void) => void;
  createChatGroupRequest: (
    payload: FormData,
    callback: (res: any) => void,
  ) => void;
  followers: FollowerState;
  user: UserState;
  isRefreshPage: Function;
  bottomSheetModalRef: any;
  screenTitle: string;
  singleChatList: boolean;
  selectedTab?: (tab: 'single' | 'group') => void;
}

const CreateChatList: React.FC<ActionsProps> = ({
  getUserFriendsRequest,
  createChatGroupRequest,
  followers,
  isRefreshPage,
  user,
  bottomSheetModalRef,
  screenTitle,
  singleChatList,
  selectedTab,
}) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [mainLoading, setMainLoading] = useState<boolean>(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [icon, setIcon] = useState<any>(null);
  const [title, setTitle] = useState<string>('');

  const [FriendsList, setFriendsList] = useState<User[]>([]);
  const [selectedItems, setSelectedItems] = useState<any[]>([]);
  const [showGroupDetails, setShowGroupDetails] = useState<boolean>(false);
  const navigation = useNavigation();

  let isSubmitting = false; // Prevent multiple rapid clicks

  const renderItem = ({item}: {item: User}) => {
    return (
      <CreateChatItem
        item={item}
        onItemPress={() => handleItemClick(item)}
        selectedItems={selectedItems}
      />
    );
  };

  useEffect(() => {
    if (showGroupDetails && singleChatList && selectedItems.length > 0) {
      const participantList = selectedItems.map(item => ({
        addedBy: user.userInfo.userId,
        participantID: item.id,
      }));
      const payload = {participantList};
      const formData = new FormData();
      formData.append('data', JSON.stringify(payload));
      createChatGroupRequest(formData, handleSuccessResponse);
    }
  }, [showGroupDetails]);

  const handleItemClick = (item: User) => {
    const selectedIndex = selectedItems.findIndex(
      selected => selected.id === item.id,
    );

    let newSelectedItems = [...selectedItems];
    if (singleChatList) {
      newSelectedItems = [item];
    } else {
      if (selectedIndex === -1) {
        newSelectedItems.push(item);
      } else {
        newSelectedItems.splice(selectedIndex, 1);
      }
    }
    setSelectedItems(newSelectedItems);
  };

  useEffect(() => {
    fetchList();
  }, []);

  const fetchList = () => {
    try {
      setLoading(true);
      getUserFriendsRequest((data: any) => {
        if (data) {
          setLoading(false);
          setFriendsList(data);
        }
      });
    } catch (error) {
      console.error('Error fetching data:', error);
    }
  };

  const handleSearch = (text: string) => {
    setSearchQuery(text);
    const friendsData = _.cloneDeep(followers.data);

    const searchData = friendsData.filter(item =>
      item.userName.toLowerCase().includes(text.toLowerCase()),
    );
    setFriendsList(searchData);
  };

  const submitFollowRequest = () => {
    if (isSubmitting) return;
    isSubmitting = true;
    setTimeout(() => {
      isSubmitting = false;
    }, 300);

    // Check if any items are selected
    if (_.isEmpty(selectedItems)) {
      showToastMsg(
        CHAT_LIST.SELECT_ONE,
        TOAST_TYPE.DEFAULT,
        2500,
        'bottom',
        400,
      );
      return;
    }

    // Handle single chat case
    if (singleChatList) {
      if (!showGroupDetails) {
        setShowGroupDetails(true);
      } else {
        const participantList = selectedItems.map(item => ({
          addedBy: user.userInfo.userId,
          participantID: item.id,
        }));
        const payload = {participantList};
        const formData = new FormData();
        formData.append('data', JSON.stringify(payload));
        createChatGroupRequest(formData, handleSuccessResponse);
      }
      return;
    }

    // Handle group chat case
    if (!showGroupDetails) {
      setShowGroupDetails(true);
      return;
    }

    // Validate icon and title for group chats
    if (!icon) {
      showToastMsg(
        TOAST_MESSAGES.REQUIRED_LOGO,
        TOAST_TYPE.DEFAULT,
        2500,
        'bottom',
        400,
      );
      return;
    }
    if (!title) {
      showToastMsg(
        TOAST_MESSAGES.REQUIRED_NAME,
        TOAST_TYPE.DEFAULT,
        2500,
        'bottom',
        400,
      );
      return;
    }

    // Prepare payload for group chat
    const payload = {
      participantList: selectedItems.map(item => ({
        participantID: item.id,
        addedBy: user.userInfo.userId,
      })),
      createdBy: user.userInfo.userId,
      title,
    };

    const formData = new FormData();
    if (icon) {
      const filePayload = {
        uri: icon.path || icon.sourceURL,
        type: icon.mime,
        name: icon.path.substring(icon.path.lastIndexOf('/') + 1),
      };
      formData.append('icon', filePayload);
    }
    formData.append('data', JSON.stringify(payload));

    // Make the API request to create a chat group
    createChatGroupRequest(formData, handleSuccessResponse);
  };

  const handleSuccessResponse = (res: any) => {
    setMainLoading(false);
    if (res) {
      // logic to navigate to chat screen directly
      const index = _.findIndex(
        res.participantList,
        (participant: {participantID: string}) =>
          participant.participantID === user.userInfo.userId,
      );
      const updatedItem = _.cloneDeep(res);
      updatedItem.participantList.splice(index, 1);
      setTimeout(() => {
        (navigation.navigate as (route: string, {}) => void)(Routes.CHAT, {
          conversationId: updatedItem.id,
          participantList: selectedItems,
          isBlocked: updatedItem.isBlocked,
          title: updatedItem.title,
          canMessage: true,
        });
      }, 0);
      setTimeout(() => {
        const tab = singleChatList ? 'single' : 'group';
        selectedTab && selectedTab(tab);
        setSelectedItems([]);
        setShowGroupDetails(false);
        setTitle('');
        setIcon(null);
        isRefreshPage(true);
        bottomSheetModalRef.current?.dismiss();
      }, 500);
    }
  };

  const snapPoints = useMemo(
    () => ['10%', Platform.OS == 'android' ? '95%' : '95%'],
    [],
  );

  const openGalleryView = async () => {
    ImageCropPicker.openPicker({
      width: 300,
      height: 400,
      cropping: true,
      cropperCircleOverlay: true,
      mediaType: 'photo',
    })
      .then(image => {
        if (image) {
          setIcon(image);
        }
      })
      .catch(error => {
        if (error.message.includes(TOAST_MESSAGES.GALLERY_MSG_ERROR)) {
          Alert.alert(
            TOAST_MESSAGES.PERMISSION_ERROR_HEADING,
            TOAST_MESSAGES.ALLOW_PHOTO_PERMISSION,
            [
              {text: 'Open Settings', onPress: () => Linking.openSettings()},
              {text: 'Not Now', onPress: () => {}},
            ],
          );
        }
      });
  };

  const openCameraView = async () => {
    ImageCropPicker.openCamera({
      width: 300,
      height: 400,
      cropping: true,
      cropperCircleOverlay: true,
      mediaType: 'photo',
    })
      .then(image => {
        if (image) {
          setIcon(image);
        }
      })
      .catch(() => {
        Alert.alert(
          TOAST_MESSAGES.CAMERA_ERROR_HEADING,
          TOAST_MESSAGES.CAMERA_ERROR_DETAIL,
          [
            {text: 'Open Settings', onPress: () => Linking.openSettings()},
            {text: 'Not Now', onPress: () => {}},
          ],
        );
      });
  };

  return (
    <BottomSheetModalProvider>
      <BottomSheetModal
        handleIndicatorStyle={styles.handleStyle}
        contentHeight={Metrics.screenHeight * 0.9}
        backgroundStyle={styles.bottomSheet}
        enablePanDownToClose={true}
        ref={bottomSheetModalRef}
        index={1}
        snapPoints={snapPoints}
        enableHandlePanningGesture={false}
        enableContentPanningGesture={false}>
        <View style={styles.container}>
          <CustomNavbar
            hasBack={true}
            leftBtnPress={() => {
              if (singleChatList) {
                bottomSheetModalRef.current?.dismiss();
                setSelectedItems([]);
                setIcon(null);
                setSearchQuery('');
              } else {
                if (showGroupDetails) {
                  setShowGroupDetails(false);
                } else {
                  bottomSheetModalRef.current?.dismiss();
                  setTitle('');
                  setIcon(null);
                  setSelectedItems([]);
                  setSearchQuery('');
                }
              }
              fetchList();
            }}
            rightBtnImage={
              (singleChatList && selectedItems.length === 1) ||
              (!singleChatList && selectedItems.length > 1) ? (
                <FORWARD_ICON />
              ) : null
            }
            rightBtnPress={() => {
              if (!singleChatList && selectedItems.length === 1) {
                showToastMsg(CHAT_LIST.MULTIPLE_USERS);
                return;
              }
              submitFollowRequest();
            }}
            title={screenTitle}
            hasRight
          />
          {singleChatList || !showGroupDetails ? (
            <>
              <SearchBar
                autoFocus={false}
                containerStyle={styles.searchBox}
                onSearchText={handleSearch}
                value={searchQuery}
              />
              <FlatList
                contentContainerStyle={{paddingBottom: Metrics.ratio(50)}}
                ListEmptyComponent={
                  <EmptyStateComponent text={FRIENDS.EMPTY_STATE} />
                }
                data={FriendsList}
                refreshing={loading}
                onRefresh={() => fetchList()}
                renderItem={renderItem}
                showsVerticalScrollIndicator={false}
                extraData={FriendsList}
                style={{
                  height: Metrics.screenHeight * 0.75,
                }}
              />
            </>
          ) : (
            <GroupDetails
              icon={icon}
              title={title}
              selectedItems={selectedItems}
              setTitle={setTitle}
              setSelectedItems={setSelectedItems}
              openCameraView={openCameraView}
              openGalleryView={openGalleryView}
            />
          )}

          <Loader loading={mainLoading} />
        </View>
      </BottomSheetModal>
    </BottomSheetModalProvider>
  );
};

const mapStateToProps = (state: RootState) => ({
  user: state.user,
  isRefresh: state.user,
  followers: state.followers,
});
const actions = {getUserFriendsRequest, createChatGroupRequest, isRefreshPage};

export default connect(mapStateToProps, actions)(CreateChatList);
