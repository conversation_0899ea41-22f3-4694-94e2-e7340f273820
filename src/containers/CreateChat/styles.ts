// @flow
import {StyleSheet} from 'react-native';
import {Colors, Metrics} from '../../theme';
import {getStatusBarHeight} from 'react-native-iphone-x-helper';

export default StyleSheet.create({
  container: {
    marginVertical: Metrics.baseMargin,
  },

  nextButton: {marginHorizontal: Metrics.ratio(16), flex: 1},
  searchBox: {
    backgroundColor: Colors.gray4,
    marginTop: Metrics.ratio(10),
    borderRadius: Metrics.ratio(20),
  },
  handleStyle: {
    backgroundColor: Colors.handle,
    height: Metrics.ratio(6),
    borderRadius: Metrics.ratio(8),
    width: Metrics.ratio(45),
  },
  bottomSheet: {
    elevation: 10,
    shadowColor: Colors.black,
    shadowOffset: {
      width: 0,
      height: Metrics.ratio(12) as number,
    },
    shadowOpacity: 0.58,
    shadowRadius: 16.0,
  },
});
