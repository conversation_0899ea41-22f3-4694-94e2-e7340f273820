// @flow

import React, {useEffect, useRef, useState} from 'react';
import {connect} from 'react-redux';
import {View} from 'react-native';
import {Colors, Metrics} from '../../theme';
import _ from 'lodash';
import MapView from 'react-native-map-clustering';
import {PROVIDER_GOOGLE} from 'react-native-maps';
declare const navigator: any;
navigator.geolocation = require('@react-native-community/geolocation');
import Geolocation from '@react-native-community/geolocation';

import geolocationConfig from '../../config/geoLocationConfig';
import {
  DEFAULT_PADDING,
  LATITUDE_DELTA,
  LONGITUDE_DELTA,
} from '../../constants';
import {MapViewTabs, CustomMarker} from '../../components';
import {userMedia_CheckInRequest} from '../../actions/UserActions.ts';

import {useIsFocused, useNavigation} from '@react-navigation/native';
import Routes from '../../constants/RouteConstants.ts';
import {
  ILocationData,
  IMarkers,
  IMedia_CheckIn,
  RootState,
  UserState,
} from '../../types';

interface MediaProps {
  user: UserState;

  userMedia_CheckInRequest: (
    payload: IMedia_CheckIn,
    callback: (res: any) => void,
  ) => void;
}

const CheckInTab: React.FC<MediaProps> = ({userMedia_CheckInRequest, user}) => {
  const [regionCoords, setRegion] = useState({lat: 37.7749, lng: -122.4194});
  const [markerItems, setMarkers] = useState<ILocationData[]>([]);
  const userName = user.userInfo.userName;

  const isFocus = useIsFocused();

  const mapRef = useRef<MapView>(null);
  const navigation = useNavigation();
  Geolocation.setRNConfiguration(geolocationConfig);

  useEffect(() => {
    getAllCheckIn();
  }, [isFocus]);

  useEffect(() => {
    if (markerItems.length > 0) {
      fitAllMarkers();
    }
  }, [markerItems]);

  const getAllCheckIn = () => {
    const payload: IMedia_CheckIn = {
      checkin: true,
    };
    userMedia_CheckInRequest(payload, res => {
      if (res) {
        const filteredCheckIns = res.map((entry: any) => entry.checkIn);

        setMarkers(filteredCheckIns);
        fitAllMarkers();
      }
    });
  };

  const fitAllMarkers = () => {
    let markersArray: IMarkers[] = [];

    markerItems.filter(data => {
      let obj = {
        latitude: Number(data.latitude),
        longitude: Number(data.longitude),
      };
      markersArray.push(obj);
    });
    if (!_.isEmpty(markerItems)) {
      setTimeout(() => {
        (mapRef.current as any)?.fitToCoordinates(markersArray, {
          animated: true,
          edgePadding: DEFAULT_PADDING,
        });
      }, 500);
    }
  };

  return (
    <View>
      <MapViewTabs
        onAddPress={() =>
          (
            navigation.navigate as (
              route: string,
              param: {userName: string},
            ) => void
          )(Routes.CHECK_IN, {userName: userName})
        }
      />
      <MapView
        clusterColor={Colors.black}
        ref={mapRef}
        onMapReady={fitAllMarkers}
        provider={PROVIDER_GOOGLE}
        style={{height: Metrics.screenHeight * 0.8}}
        region={{
          latitude: regionCoords.lat,
          longitude: regionCoords.lng,
          latitudeDelta: LATITUDE_DELTA,
          longitudeDelta: LONGITUDE_DELTA,
        }}
        showsUserLocation={false}>
        {markerItems.map((element: any) => (
          <CustomMarker
            data={element}
            coordinate={{
              latitude: Number(element?.latitude),
              longitude: Number(element?.longitude),
            }}
          />
        ))}
      </MapView>
    </View>
  );
};

const mapStateToProps = (state: RootState) => ({
  user: state.user,
});

const actions = {userMedia_CheckInRequest};

export default connect(mapStateToProps, actions)(CheckInTab);
