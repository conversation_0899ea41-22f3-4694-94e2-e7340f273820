// @flow
import {StyleSheet} from 'react-native';
import {Colors, Metrics, Fonts} from '../../theme';

export default StyleSheet.create({
  container: {
    position: 'absolute',
    alignSelf: 'center',
  },
  button: {
    marginTop: Metrics.ratio(30),
  },
  headerText: {
    color: '#000',
    marginTop: Metrics.ratio(40),
    marginBottom: Metrics.ratio(30),
    marginLeft: Metrics.ratio(24),
  },
  emailInputContainer: {
    marginHorizontal: Metrics.ratio(30),
    marginBottom: Metrics.ratio(20),
  },
  passwordInputContainer: {
    marginHorizontal: Metrics.ratio(30),
    marginVertical: 10,
  },
  forgotContainer: {
    alignSelf: 'flex-end',
    marginVertical: Metrics.ratio(20),
    marginRight: Metrics.ratio(40),
  },
  socialLoginContainer: {
    alignSelf: 'center',
    borderRadius: 6,
    borderColor: '#DAE0E6',
    borderWidth: 1,
    width: Metrics.ratio(100),
    height: Metrics.ratio(50),
    alignItems: 'center',
    justifyContent: 'center',
  },
  footerTextContainer: {
    marginTop: Metrics.ratio(30),
    justifyContent: 'flex-end',
    alignItems: 'center',
  },

  doneButton: {
    paddingHorizontal: Metrics.ratio(100),
    marginTop: Metrics.ratio(20),
  },
  congratsText: {
    fontFamily: Fonts.type.regular,
    fontSize: Metrics.ratio(16),
    lineHeight: Metrics.ratio(24),
    textAlign: 'center',
    paddingTop: Metrics.ratio(16),
    color: Colors.text.modalText,
    paddingHorizontal: Metrics.ratio(32),
  },

  title: {
    fontFamily: Fonts.type.bold,
    fontSize: Metrics.ratio(22),
    lineHeight: Metrics.ratio(18),
    textAlign: 'center',
    zIndex: 999,
    color: Colors.black,
    paddingTop: Metrics.ratio(16),
  },
  cancelButton: {
    backgroundColor: Colors.white,
    paddingHorizontal: Metrics.ratio(100),
    marginTop: Metrics.ratio(20),
  },
  phoneInputContainer: {
    height: Metrics.ratio(60),
    paddingVertical: 0,
    backgroundColor: Colors.white,
    borderRadius: Metrics.ratio(16),
    paddingRight: Metrics.ratio(15),
    borderColor: '#DAE0E6',
    borderWidth: Metrics.ratio(1),
  },
  phoneTextInput: {
    padding: 0,
  },
  phoneContainer: {
    paddingTop: Metrics.ratio(10),
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: Metrics.ratio(24),
  },
  dobContainer: {
    marginHorizontal: Metrics.ratio(30),
    marginVertical: Metrics.ratio(10),
  },
  above18Container: {
    flexDirection: 'row',
    padding: Metrics.ratio(20),
    alignItems: 'center',
  },

  above18Text: {
    fontSize: 14,
    color: '#000',
    flexWrap: 'wrap',
    flex: 1,
  },

  linkText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: Colors.black, // or any link color
    textDecorationLine: 'underline',
  },
  appleButton: {
    marginHorizontal: Metrics.ratio(30),

    backgroundColor: 'white',
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    width: 48,
    height: 48,
    shadowColor: '#000',
    shadowOpacity: 0.1,
    shadowRadius: 6,
    elevation: 3,
  },
  appleContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
  },
});
