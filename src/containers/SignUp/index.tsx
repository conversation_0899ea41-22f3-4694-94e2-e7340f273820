import _ from 'lodash';
import {connect} from 'react-redux';
import React, {useRef, useState} from 'react';
import {
  ISocialPayload,
  ISignUpPayload,
  QUIZ_STATUS,
  SubscriptionStatus,
  OTP_ENUMS,
} from '../../types';
import {
  AgeVerificationModal,
  AppButton,
  ButtonView,
  CustomNavbar,
  Loader,
  Text,
  TextInput,
} from '../../components';
import {
  APPLE_LOGIN,
  DATE_ICON,
  EMAIL_ICON,
  EYE_CLOSE,
  EYE_OPEN,
  GOOGLE_LOGIN,
  LOCK_ICON,
  SPLASH_BG,
  TICK_BLUE,
  TICK_ICON_NEXT,
} from '../../constants/AssetSVGConstants';
import {
  View,
  TextInput as RNTextInput,
  Pressable,
  Platform,
  TouchableOpacity,
} from 'react-native';
import styles from './styles';
import {AppStyles, Colors, Metrics} from '../../theme';
import DividerWithText from '../../components/Spacer';
import {
  userSignUPRequest,
  userInfoRequest,
  userGoogleLoginRequest,
  userAppleLoginRequest,
  startYotiSessionRequest,
  userLogout,
  updateDobRequest,
} from '../../actions/UserActions';
import {useNavigation} from '@react-navigation/native';
import {
  AUTH,
  NEXT_OF_KIN,
  SIGN_UP,
  TOAST_MESSAGES,
  TOAST_TYPE,
} from '../../constants/StringConstants';
import Util from '../../util';
import Routes from '../../constants/RouteConstants';
import {
  GoogleSignin,
  statusCodes,
} from '@react-native-google-signin/google-signin';
import {showToastMsg} from '../../components/Alert';
import DatePicker from 'react-native-date-picker';
import util from '../../util';
import BottomSheetModal from '../../components/BottomSheetModal';
import PhoneInput from 'react-native-phone-number-input';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import parsePhoneNumberFromString from 'libphonenumber-js';
import OutsidePressHandler from 'react-native-outside-press';
import {
  PRIVACY_POLICY_LINK,
  PRIVACY_POLICY_LINK_LIVE,
  TERMS_AND_CONDITIONS_LINK,
  TERMS_AND_CONDITIONS_LINK_LIVE,
} from '../../constants';
import {BASE_URL} from '../../config/WebService';
import appleAuth from '@invertase/react-native-apple-authentication';

interface ActionsProps {
  userSignUPRequest: (
    payload: ISignUpPayload,
    callback: (res: any) => void,
  ) => void;
  userInfoRequest: (callback: (res: any) => void) => void;
  userGoogleLoginRequest: (
    payload: ISocialPayload,
    response: (res: any) => void,
  ) => void;
  userAppleLoginRequest: (
    payload: ISocialPayload,
    response: (res: any) => void,
  ) => void;
  startYotiSessionRequest: (callback: (res: any) => void) => void;
  userLogout: () => void;
  updateDobRequest: (payload: any, callback: (res: any) => void) => void;
}

const SignUp: React.FC<ActionsProps> = ({
  userSignUPRequest,
  userInfoRequest,
  userGoogleLoginRequest,
  startYotiSessionRequest,
  userLogout,
  userAppleLoginRequest,
  updateDobRequest,
}) => {
  const navigation = useNavigation();
  const [secureText, setSecureText] = useState<boolean>(true);
  const [confirmSecureText, setConfirmSecureText] = useState<boolean>(true);
  const [checkClicked, setCheckClicked] = useState<boolean>(false);

  const [email, setEmail] = useState<string>('');
  const [password, setPassword] = useState<string>('');
  const [confirmPassword, setConfirmPassword] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [dob, setDob] = useState<Date>();
  const [open, setOpen] = useState(false);
  const [showValue, setShowValue] = useState(false);
  const [visibleModal, setVisibleModal] = useState(false);

  const emailRef = useRef<RNTextInput>(null);
  const passwordRef = useRef<RNTextInput>(null);
  const confirmPasswordRef = useRef<RNTextInput>(null);
  const [countryCode, setCountryCode] = useState<any>('');
  const [phoneValue, setPhoneValue] = useState('');
  const [valid, setValid] = useState(true);
  const phoneInput = useRef<PhoneInput>(null);
  const [formattedValue, setFormattedValue] = useState('');

  const [showAgeVerificationModal, setShowAgeVerificationModal] =
    useState(false);

  const _validateForm = () => {
    const errors = {};
    if (_.isEmpty(email)) {
      showToastMsg(TOAST_MESSAGES.EMAIL_REQUIRED);
      return false;
    } else if (!Util.isEmailValid(email)) {
      showToastMsg(TOAST_MESSAGES.VALID_EMAIL);
      return false;
    } else if (_.isEmpty(password)) {
      showToastMsg(TOAST_MESSAGES.PASSWORD_REQUIRED);
      return false;
    } else if (!Util.isPasswordValid(password)) {
      showToastMsg(TOAST_MESSAGES.VALID_PASSWORD, TOAST_TYPE.DEFAULT, 2500);
      return false;
    } else if (password != confirmPassword) {
      showToastMsg(TOAST_MESSAGES.PASSWORD_SHOULD_BE_SAME);
      return false;
    } else if (!checkClicked) {
      showToastMsg(
        TOAST_MESSAGES.PRIVACY_POLICY_ERROR,
        TOAST_TYPE.DEFAULT,
        2500,
      );
      return false;
    } else if (!_.isEmpty(dob) || dob === undefined) {
      showToastMsg(TOAST_MESSAGES.PLEASE_SELECT_DOB);
      return false;
    } else if (util.isAgeBetween18(new Date(), dob || new Date())) {
      setVisibleModal(true);
      return false;
    }
    // else if (!valid || !formattedValue) {
    //   showToastMsg(TOAST_MESSAGES.INVALID_PHONE);
    //   return false;
    // }
    else {
      return true;
    }
  };

  const _onsubmitForm = () => {
    if (_validateForm()) {
      handleSignUpProcess();
    }
  };

  const handleAgeVerificationComplete = () => {
    setShowAgeVerificationModal(false);
    setLoading(false);
    util.handleUserInfo(
      userInfoRequest,
      navigation,
      setLoading,
      setShowAgeVerificationModal,
    );
  };

  const handleSignUpProcess = () => {
    /* comment this code as we are not using phone number in signup form */
    /* if (!valid || !formattedValue) {
      showToastMsg(TOAST_MESSAGES.INVALID_PHONE);
      return;
    }
   }/*/

    setLoading(true);

    const payload: ISignUpPayload = {
      email: email.toLowerCase(),
      password: password,
      dob: dob ? dob : '',
      phone: '',
      countryCode: '',
    };

    userSignUPRequest(payload, (res: any) => {
      setLoading(false);
      if (res) {
        setTimeout(() => {
          (
            navigation.navigate as (
              route: string,
              param: {email: string; purpose: string},
            ) => void
          )(Routes.OTP_SCREEN, {
            email: res.email,
            purpose: OTP_ENUMS.USER_VERIFICATION,
          });
        }, 200);
      }
    });
  };

  const onGooglePress = async () => {
    if (!checkClicked) {
      showToastMsg(
        TOAST_MESSAGES.PRIVACY_POLICY_ERROR,
        TOAST_TYPE.DEFAULT,
        2500,
      );
      return;
    }
    try {
      await GoogleSignin.hasPlayServices({
        // Check if device has Google Play Services installed
        // Always resolves to true on iOS
        showPlayServicesUpdateDialog: true,
      });
      const userInfo = await GoogleSignin.signIn();
      const payload: ISocialPayload = {
        idToken: userInfo.data?.idToken as string,
      };
      const extraInfo = {
        displayName: userInfo.data?.user?.givenName,
        avatarUrl: userInfo.data?.user?.photo,
        isGoogleLogin: true,
      };
      userGoogleLoginRequest(payload, (res: any) => {
        setLoading(true);
        if (res) {
          setTimeout(() => {
            userInfoRequest(res => {
              if (res) {
                if (res.isOnboarded === true && res.ageVerified === false) {
                  setTimeout(() => {
                    setShowAgeVerificationModal(true);
                  }, 500);
                  return;
                }
                if (
                  res.isOnboarded &&
                  res.onboardingQuizStatus === QUIZ_STATUS.COMPLETED
                ) {
                  if (
                    ![
                      SubscriptionStatus.Inactive,
                      SubscriptionStatus.Incomplete,
                      SubscriptionStatus.Canceled,
                    ].includes(res.subscriptionStatus)
                  ) {
                    // If the user has a valid subscription status, navigate to HOME_TABS
                    (navigation.navigate as (route: string) => void)(
                      Routes.HOME_TABS,
                    );
                  } else {
                    util.resetToUserProfile(navigation);
                  }
                  // (navigation.navigate as (route: string) => void)(
                  //   Routes.HOME_TABS,
                  // );
                } else {
                  if (extraInfo.avatarUrl === null) {
                    (
                      navigation.navigate as (
                        route: string,
                        params: any,
                      ) => void
                    )(Routes.PROFILE_SETUP, {personalData: extraInfo});
                  }
                  if (res.profile.dob === null) {
                    (
                      navigation.navigate as (
                        route: string,
                        params: any,
                      ) => void
                    )(Routes.PROFILE_SETUP, {personalData: extraInfo});
                  } else if (
                    res.onboardingQuizStatus === QUIZ_STATUS.NOT_ATTEMPTED
                  ) {
                    (navigation.navigate as (route: string) => void)(
                      Routes.QUIZ_FLOW,
                    );
                  } else {
                    return (
                      navigation.navigate as (
                        route: string,
                        params: any,
                      ) => void
                    )(Routes.PROFILE_SETUP, {personalData: extraInfo});
                  }
                }
              } else {
                showToastMsg('something went wrong');
              }
            });
          }, 1000);
          setLoading(false);
          // alert('login Success');
        }
        setLoading(false);
      });
    } catch (error: any) {
      if (error.code === statusCodes.SIGN_IN_CANCELLED) {
        alert('User cancelled the login flow !');
      } else if (error.code === statusCodes.IN_PROGRESS) {
        alert('Signin in progress');
      } else if (error.code === statusCodes.PLAY_SERVICES_NOT_AVAILABLE) {
        alert('Google play services not available or outdated !');
      } else {
        alert(error);
      }
    }
  };

  const onDonePressed = () => {
    setVisibleModal(false);
    setTimeout(() => {
      handleSignUpProcess();
    }, 500);
  };
  const hideModal = () => {
    setVisibleModal(!visibleModal);
  };

  const onAppleButtonPress = async () => {
    try {
      setLoading(true);
      const appleAuthRequestResponse = await appleAuth.performRequest({
        requestedOperation: appleAuth.Operation.LOGIN,
        requestedScopes: [appleAuth.Scope.EMAIL, appleAuth.Scope.FULL_NAME],
      });

      const {user, email, fullName, identityToken, authorizationCode} =
        appleAuthRequestResponse;

      // Send identityToken to your backend to verify the user and create a session
      console.log('Apple user:', {
        user,
        email,
        fullName,
        identityToken,
        authorizationCode,
      });
      const extraInfo = {
        displayName: fullName,
        avatarUrl: '',
        isGoogleLogin: true,
      };
      const payload: ISocialPayload = {
        idToken: identityToken as string,
      };

      userAppleLoginRequest(payload, (res: any) => {
        setLoading(true);
        if (res) {
          setTimeout(() => {
            userInfoRequest(res => {
              if (res) {
                if (res.isOnboarded === true && res.ageVerified === false) {
                  setTimeout(() => {
                    setShowAgeVerificationModal(true);
                  }, 500);
                  return;
                }
                if (
                  res.isOnboarded &&
                  res.onboardingQuizStatus === QUIZ_STATUS.COMPLETED
                ) {
                  if (
                    ![
                      SubscriptionStatus.Inactive,
                      SubscriptionStatus.Incomplete,
                      SubscriptionStatus.Canceled,
                    ].includes(res.subscriptionStatus)
                  ) {
                    // If the user has a valid subscription status, navigate to HOME_TABS
                    (navigation.navigate as (route: string) => void)(
                      Routes.HOME_TABS,
                    );
                  } else {
                    util.resetToUserProfile(navigation);
                  }
                  // (navigation.navigate as (route: string) => void)(
                  //   Routes.HOME_TABS,
                  // );
                } else {
                  if (extraInfo.avatarUrl === null) {
                    (
                      navigation.navigate as (
                        route: string,
                        params: any,
                      ) => void
                    )(Routes.PROFILE_SETUP, {personalData: res});
                  } else if (res.profile.dob === null) {
                    (
                      navigation.navigate as (
                        route: string,
                        params: any,
                      ) => void
                    )(Routes.PROFILE_SETUP, {personalData: extraInfo});
                  } else if (
                    res.onboardingQuizStatus === QUIZ_STATUS.NOT_ATTEMPTED
                  ) {
                    (navigation.navigate as (route: string) => void)(
                      Routes.QUIZ_FLOW,
                    );
                  } else {
                    return (
                      navigation.navigate as (
                        route: string,
                        params: any,
                      ) => void
                    )(Routes.PROFILE_SETUP, {personalData: extraInfo});
                  }
                }
              } else {
                showToastMsg('Something went wrong ');
              }
            });
          }, 1000);
          setLoading(false);
          // alert('login Success');
        }
        setLoading(false);
      });
    } catch (error) {
      setLoading(false);

      console.warn('Apple Sign-in Error', error);
    }
  };

  return (
    <KeyboardAwareScrollView>
      <CustomNavbar title="" leftBtnPress={() => navigation.goBack()} />
      {headerText()}
      <TextInput
        ref={emailRef}
        containerStyle={styles.emailInputContainer}
        label={SIGN_UP.EMAIL_LABEL}
        placeholder={SIGN_UP.EMAIL_PLACEHOLDER}
        leftImage={<EMAIL_ICON />}
        onChangeText={val => setEmail(val)}
        onSubmitEditing={() => passwordRef.current?.focus()}
      />
      <TextInput
        ref={passwordRef}
        containerStyle={styles.passwordInputContainer}
        label={SIGN_UP.PASSWORD_LABEL}
        placeholder={SIGN_UP.PASSWORD_PLACEHOLDER}
        leftImage={<LOCK_ICON />}
        rightImage={secureText ? <EYE_CLOSE /> : <EYE_OPEN />}
        onPress={() => setSecureText(!secureText)}
        secureTextEntry={secureText}
        onChangeText={val => setPassword(val)}
        onSubmitEditing={() => confirmPasswordRef.current?.focus()}
      />

      <TextInput
        ref={confirmPasswordRef}
        containerStyle={styles.passwordInputContainer}
        label={SIGN_UP.CONFIRM_PASSWORD_LABEL}
        placeholder={SIGN_UP.CONFIRM_PASSWORD_PLACEHOLDER}
        leftImage={<LOCK_ICON />}
        rightImage={confirmSecureText ? <EYE_CLOSE /> : <EYE_OPEN />}
        onPress={() => setConfirmSecureText(!confirmSecureText)}
        onChangeText={val => setConfirmPassword(val)}
        secureTextEntry={confirmSecureText}
      />

      <Pressable style={styles.dobContainer} onPress={() => setOpen(!open)}>
        <TextInput
          label={SIGN_UP.DOB_LABEL}
          placeholder={SIGN_UP.DOB_PLACEHOLDER}
          rightImage={<DATE_ICON />}
          value={showValue ? Util.getFormattedDate(dob) : ''}
          onPress={() => setOpen(!open)}
          editable={false}
          onPressIn={() => setOpen(!open)}
        />
      </Pressable>
      <View style={styles.above18Container}>
        <Pressable onPress={() => setCheckClicked(!checkClicked)}>
          {checkClicked ? (
            <TICK_BLUE width={20} height={20} />
          ) : (
            <TICK_ICON_NEXT width={20} height={20} />
          )}
        </Pressable>
        <Text style={styles.above18Text}>
          {SIGN_UP.I_AGREE}
          <Text
            style={styles.linkText}
            onPress={() =>
              (
                navigation.navigate as (
                  route: string,
                  params: {url: string; title: string},
                ) => void
              )(Routes.REPORT_SCREEN, {
                url: BASE_URL.includes('sigma')
                  ? PRIVACY_POLICY_LINK
                  : PRIVACY_POLICY_LINK_LIVE,
                title: 'Privacy Policy',
              })
            }>
            {SIGN_UP.PRIVACY_POLICY}
          </Text>{' '}
          and{' '}
          <Text
            style={styles.linkText}
            onPress={() =>
              (
                navigation.navigate as (
                  route: string,
                  params: {url: string; title: string},
                ) => void
              )(Routes.REPORT_SCREEN, {
                url: BASE_URL.includes('sigma')
                  ? TERMS_AND_CONDITIONS_LINK
                  : TERMS_AND_CONDITIONS_LINK_LIVE,
                title: 'Terms of Service',
              })
            }>
            {SIGN_UP.TERMS_OF_SERVICE}
          </Text>
          .
        </Text>
      </View>

      <AppButton
        text={SIGN_UP.SIGN_UP_BUTTON}
        onPress={_onsubmitForm}
        textColor={Colors.text.white}
      />
      <DividerWithText />
      <View style={styles.appleContainer}>
        <ButtonView style={styles.appleButton} onPress={onGooglePress}>
          <GOOGLE_LOGIN />
        </ButtonView>
        {Platform.OS === 'ios' && (
          <TouchableOpacity
            style={styles.appleButton}
            onPress={onAppleButtonPress}>
            <APPLE_LOGIN />
          </TouchableOpacity>
        )}
      </View>
      <DatePicker
        maximumDate={new Date()}
        modal
        locale="en"
        mode="date"
        open={open}
        date={dob ?? new Date()}
        onConfirm={(date: React.SetStateAction<Date | undefined>) => {
          setShowValue(true);
          setOpen(false);
          setDob(date || new Date());
        }}
        onCancel={() => {
          setOpen(false);
        }}
      />

      <AgeVerificationModal
        visible={showAgeVerificationModal}
        onVerificationComplete={handleAgeVerificationComplete}
        onStartVerification={startYotiSessionRequest}
        userInfoRequest={userInfoRequest}
        logout={() => {
          util.handleUserLogout(userLogout, navigation);
          setShowAgeVerificationModal(false);
        }}
        updateDobRequest={updateDobRequest}
      />

      {footerText()}

      <BottomSheetModal
        visible={visibleModal}
        onClose={hideModal}
        onBackdropPress={hideModal}>
        <View>
          <Text style={styles.title}>{AUTH.AGE_VERIFICATION}</Text>
          <Text style={styles.congratsText}>{AUTH.AGE_VERIFICATION_TEXT}</Text>
          <AppButton
            text={AUTH.DONE}
            textColor={Colors.white}
            onPress={onDonePressed}
            buttonStye={styles.doneButton}
          />
          <AppButton
            text={AUTH.CANCEL}
            textColor={Colors.black}
            onPress={hideModal}
            buttonStye={styles.cancelButton}
          />
        </View>
      </BottomSheetModal>
      <Loader loading={loading} />
    </KeyboardAwareScrollView>
  );
};

const headerText = () => {
  return (
    <Text
      type="semi_bold"
      size={'large'}
      color={Colors.black}
      style={styles.headerText}>
      {SIGN_UP.SIGN_UP}
    </Text>
  );
};
const footerText = () => {
  const navigation = useNavigation();
  return (
    <View style={styles.footerTextContainer}>
      <View style={AppStyles.flexRow}>
        <Text color={Colors.text.gray} type="medium" size={'xSmall'}>
          {SIGN_UP.NEW_IMPRINT}
        </Text>
        <ButtonView
          onPress={() =>
            (navigation.navigate as (route: string) => void)(Routes.LOGIN)
          }>
          <Text
            color={Colors.text.black}
            type="semi_bold"
            textAlign="center"
            size={'xSmall'}
            style={AppStyles.mLeft5}>
            {SIGN_UP.SIGN_IN}
          </Text>
        </ButtonView>
      </View>
    </View>
  );
};

const mapStateToProps = () => ({});

const actions = {
  userSignUPRequest,
  userInfoRequest,
  userGoogleLoginRequest,
  userAppleLoginRequest,
  userLogout,
  startYotiSessionRequest,
  updateDobRequest,
};

export default connect(mapStateToProps, actions)(SignUp);
