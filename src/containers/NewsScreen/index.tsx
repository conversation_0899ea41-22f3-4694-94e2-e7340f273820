import React, {useCallback, useEffect, useRef, useState} from 'react';
import {
  View,
  FlatList,
  ActivityIndicator,
  Text,
  Alert,
  Linking,
} from 'react-native';
import {ENTERTAINMENTS_LIST, NEWS} from '../../constants/StringConstants';
import {CustomNavbar, Loader} from '../../components';
import {useNavigation} from '@react-navigation/native';
import {hasNotch} from 'react-native-device-info';
import {connect} from 'react-redux';
import {getNewsContentRequest} from '../../actions/EntertainmentActions';
import {
  ENTERTAINMENTS_OPTIONS,
  IMPRINT_SCORES_LIST,
  QUIZ_ITEM,
} from '../../constants';
import {
  IEntertainmentContentResponse,
  INewsPayload,
  INewsResponse,
  IProfileOptions,
  IQuizContentResponse,
  ITransformedNews,
} from '../../types';
import styles from './styles';
import ImprintScoresList from '../../components/ImprintScoresList';
import {AppStyles, Colors, Metrics} from '../../theme';
import styes from '../../components/QuizQuestion/styes';
import _ from 'lodash';
import NewsItem from '../../components/EntertainmentComponents/NewsItem';
import NewsTab from '../../components/EntertainmentComponents/NewsTabs';
import Routes from '../../constants/RouteConstants';
import util from '../../util';
import AskRudiActionSheetComponent, {
  AskRudiActonSheetRef,
} from '../../components/EntertainmentComponents/Actionsheet';
import {ActionSheetProvider} from '@expo/react-native-action-sheet';
import ShareModal from '../../components/EntertainmentComponents/ShareModal';
import {BottomSheetModal} from '@gorhom/bottom-sheet';
import {Switch} from 'react-native-gesture-handler';

interface NewsScreenProps {
  getNewsContentRequest: (
    payload: INewsPayload,
    callback: (res: any) => void,
  ) => void;
}

const NewsScreen: React.FC<NewsScreenProps> = ({getNewsContentRequest}) => {
  const [selectedItem] = useState<IProfileOptions>(ENTERTAINMENTS_OPTIONS[0]);
  const [isPersonal, setIsPersonal] = useState<boolean>(false);
  const [scores, setScores] = useState<string[]>(['all']);
  const actionSheetRef = useRef<AskRudiActonSheetRef>(null);
  const [newsData, setNewsData] = useState<INewsResponse[]>([]);
  const [isFetchingMore, setIsFetchingMore] = useState(false);
  const [selectedNews, setSelectedNews] = useState<ITransformedNews[]>([]);
  const shareBottomSheet = useRef<BottomSheetModal>(null);
  const [shareItem, setShareItem] = useState<any>(ENTERTAINMENTS_OPTIONS[0]);

  const [isGlobal, setIsGlobal] = useState<boolean>(false);

  const [loading, setLoading] = useState<boolean>(false);
  const [hasMore, setHasMore] = useState(true);

  const [page, setPage] = useState<number>(1);

  const navigation = useNavigation();
  const QUIZ_FILTER_LIST = [QUIZ_ITEM, ...IMPRINT_SCORES_LIST];
  const flatListRef = useRef<any>();

  const fetchNewsContent = (currentPage: number) => {
    const payload: INewsPayload = {
      page: currentPage,
      personal: isPersonal,
      global: isGlobal,
      filters: scores.includes('all') ? [] : scores,
    };

    if (currentPage === 1) setLoading(true);
    else setIsFetchingMore(true);

    getNewsContentRequest(payload, res => {
      if (currentPage === 1) {
        setNewsData(res);
        setHasMore(res?.length > 0);
      } else {
        setNewsData(prevData => [...prevData, ...res]);
        setHasMore(res?.length > 0);
      }

      setLoading(false);
      setIsFetchingMore(false);
    });
  };

  useEffect(() => {
    fetchNewsContent(page);
  }, [scores, isPersonal, page, isGlobal]);

  const handleLoadMore = () => {
    if (!isFetchingMore) {
      setPage(prevPage => prevPage + 1);
    }
  };

  const onAskRudiPress = (item: INewsResponse) => {
    const convertedNews = util.tranFormNewsData([item]);
    setSelectedNews(convertedNews);
    setTimeout(() => {
      actionSheetRef.current?.handlePress();
    }, 0);
  };

  const handleOptionPress = (text: string) => {
    (navigation.navigate as (route: string, params: any) => void)(
      Routes.RUDI_MESSAGE,
      {
        dailyNews: selectedNews,
        dailyNewsFlag: true,
        customPrompt: text,
      },
    );
  };

  const onSharePress = (
    item: IEntertainmentContentResponse | IQuizContentResponse,
  ) => {
    shareBottomSheet.current?.present();
    setShareItem(item);
  };

  const renderItem = (item: INewsResponse) => {
    return (
      <NewsItem
        item={item as INewsResponse}
        askRudiPress={onAskRudiPress}
        readMorePress={item => Linking.openURL(item.url)}
        onSharePress={onSharePress}
      />
    );
  };

  const handleFilterPress = (scoreName: string) => {
    setPage(1);

    flatListRef.current.scrollToOffset({offset: 0});

    setScores(prevScores => {
      let updatedScores: string[];

      if (scoreName === 'all') {
        updatedScores = ['all'];
      } else {
        updatedScores = prevScores.includes(scoreName)
          ? prevScores.filter(score => score !== scoreName)
          : [...prevScores.filter(score => score !== 'all'), scoreName];

        if (updatedScores.length === 0) {
          updatedScores = ['all'];
        }
      }

      return updatedScores;
    });
  };

  const renderFilter = () => {
    return (
      <View
        style={{
          paddingTop: Metrics.ratio(8),
          backgroundColor: Colors.gray,
        }}>
        <ImprintScoresList
          selectedScores={scores}
          isPersonal={false}
          onScoresPress={handleFilterPress}
          data={QUIZ_FILTER_LIST}
        />
      </View>
    );
  };
  const togglePersonal = (isPersonalSelected: boolean) => {
    setIsPersonal(isPersonalSelected);
    setIsGlobal(false);
    setPage(1);
    flatListRef.current.scrollToOffset({offset: 0});
    setScores(['all']);
  };

  return (
    <ActionSheetProvider>
      <View style={styles.container}>
        <View
          style={
            hasNotch() ? styles.statusBar : styles.statusBarWithoutNotch
          }></View>
        <CustomNavbar
          title={NEWS.TITLE}
          hasBack={true}
          leftBtnPress={() => navigation.goBack()}
          style={AppStyles.mBottom10}
        />
        <View style={styes.separator} />
        <NewsTab
          isPersonalSelect={isPersonal}
          onGeneralPress={() => togglePersonal(false)}
          onPersonalPress={() => togglePersonal(true)}
        />

        {!isPersonal && renderFilter()}

        <FlatList
          ListHeaderComponent={
            <View style={styles.switchContainer}>
              <Text style={styles.switchText}>
                {isGlobal ? NEWS.GLOBAL_NEWS : NEWS.UK_NEWS}
              </Text>
              <Switch
                style={{transform: [{scaleX: 0.7}, {scaleY: 0.7}]}}
                trackColor={{false: Colors.white, true: Colors.warning}}
                thumbColor={isGlobal ? Colors.white : Colors.white}
                ios_backgroundColor={Colors.gray}
                onValueChange={() => {
                  setIsGlobal(!isGlobal), setPage(1);
                }}
                value={isGlobal}
              />
            </View>
          }
          bounces={true}
          ref={flatListRef}
          contentContainerStyle={[
            styles.listContainer,
            selectedItem.name === ENTERTAINMENTS_LIST.QUIZ && styes.paddingTop0,
          ]}
          keyExtractor={(item, index) => index.toString()}
          data={newsData}
          renderItem={({item}: {item: INewsResponse}) => renderItem(item)}
          onEndReached={handleLoadMore}
          onEndReachedThreshold={0.5}
          ListFooterComponent={() =>
            hasMore ? (
              !loading &&
              isFetchingMore && <ActivityIndicator color={Colors.black} />
            ) : (
              <Text style={styles.footerText}>{NEWS.FOOTER_TEXT}</Text>
            )
          }
          extraData={selectedNews}
        />

        <Loader loading={loading} />
        <AskRudiActionSheetComponent
          ref={actionSheetRef}
          onSelect={handleOptionPress}
        />
        <ShareModal
          shareModalRef={shareBottomSheet}
          title={
            'valueType' in shareItem
              ? shareItem?.title
              : shareItem?.reward?.title
          }
          description={
            'valueType' in shareItem
              ? shareItem?.body
              : shareItem.reward?.description
          }
          type={
            'valueType' in shareItem ? shareItem?.valueType : shareItem.category
          }
        />
      </View>
    </ActionSheetProvider>
  );
};

const mapStateToProps = (state: any) => ({
  user: state.user,
});

const actions = {
  getNewsContentRequest,
};

export default connect(mapStateToProps, actions)(NewsScreen);
