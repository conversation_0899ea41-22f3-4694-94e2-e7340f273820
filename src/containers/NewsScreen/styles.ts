import {StyleSheet} from 'react-native';
import {Colors, Fonts, Metrics} from '../../theme';
import {getStatusBarHeight} from 'react-native-iphone-x-helper';

export default StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: 0,
  },
  statusBar: {
    backgroundColor: Colors.white,
    height: getStatusBarHeight() + 30,
  },
  statusBarWithoutNotch: {
    backgroundColor: Colors.white,
    height: Metrics.ratio(30),
  },

  listContainer: {
    flexGrow: 1,
    backgroundColor: Colors.background.home,
  },
  footerText: {
    fontFamily: Fonts.type.semi_bold,
    textAlign: 'center',
    marginVertical: 20,
    color: Colors.black,
    fontSize: 16,
  },
  switchContainer: {
    backgroundColor: Colors.background.home,
    alignSelf: 'flex-end',
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: Metrics.ratio(20),
    marginTop: Metrics.ratio(10),
  },
  switchText: {
    fontFamily: Fonts.type.semi_bold,
    fontSize: Fonts.size.xxxSmall,
    color: Colors.black,
    marginRight: Metrics.ratio(10),
  },
});
