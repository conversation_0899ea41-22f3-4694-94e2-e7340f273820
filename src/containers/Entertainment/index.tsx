import React, {useEffect, useRef, useState} from 'react';
import {View, FlatList, Text} from 'react-native';
import {
  ENTERTAINMENTS,
  ENTERTAINMENTS_LIST,
} from '../../constants/StringConstants';
import {CustomNavbar, Loader} from '../../components';
import {useIsFocused, useNavigation} from '@react-navigation/native';
import {hasNotch} from 'react-native-device-info';
import {connect} from 'react-redux';
import {
  getEntertainmentsContentRequest,
  getQuizPreviewRequest,
  updateQuizItem,
} from '../../actions/EntertainmentActions';
import {
  ENTERTAINMENTS_OPTIONS,
  IMPRINT_SCORES_LIST,
  QUIZ_ITEM,
} from '../../constants';
import {
  ENTERTAINMENTS_ENUMS,
  IEntertainmentContent,
  IEntertainmentContentResponse,
  IProfileOptions,
  IQuizContentResponse,
  UserState,
} from '../../types';
import styles from './styles';
import EntertainmentList from '../../components/EntertainmentComponents/EntertainmentList';
import EntertainmentItem from '../../components/EntertainmentComponents/EntertainmentItem';
import ShareModal from '../../components/EntertainmentComponents/ShareModal';
import {BottomSheetModal} from '@gorhom/bottom-sheet';
import QuizItem from '../../components/EntertainmentComponents/QuizItem';
import Routes from '../../constants/RouteConstants';
import ImprintScoresList from '../../components/ImprintScoresList';
import {Colors, Metrics} from '../../theme';
import styes from '../../components/QuizQuestion/styes';
import _ from 'lodash';

interface EntertainmentsProps {
  getEntertainmentsContentRequest: (
    payload: IEntertainmentContent,
    callback: (res: any) => void,
  ) => void;

  getQuizPreviewRequest: (callback: (res: any) => void) => void;
  user: UserState;
  QuizData: IQuizContentResponse[];
  updateQuizItem: (updatedItem: any) => void;
  route?: {
    params?: {
      tab?: IProfileOptions;
    };
  };
}

const Entertainments: React.FC<EntertainmentsProps> = ({
  getEntertainmentsContentRequest,
  getQuizPreviewRequest,
  QuizData,
  updateQuizItem,
  route,
}) => {
  const [selectedItem, setSelectedItem] = useState<IProfileOptions>(() => {
    const tabFromRoute = route?.params?.tab;
    return tabFromRoute || ENTERTAINMENTS_OPTIONS[0];
  });

  const [entertainmentsList] = useState(ENTERTAINMENTS_OPTIONS);

  const isFocused = useIsFocused();

  const [shareItem, setShareItem] = useState<any>(ENTERTAINMENTS_OPTIONS[0]);

  const [filteredEntertainmentData, setFilteredEntertainmentData] = useState<
    IEntertainmentContentResponse[] | IQuizContentResponse[]
  >([]);

  const QUIZ_FILTER_LIST = [QUIZ_ITEM, ...IMPRINT_SCORES_LIST];

  const [scores, setScores] = useState<string[]>(['all']);

  const flatListRef = useRef<any>();

  const [entertainmentData, setEntertainmentData] = useState<
    IEntertainmentContentResponse[] | IQuizContentResponse[]
  >([]);
  const [loading, setLoading] = useState<boolean>(false);
  const shareBottomSheet = useRef<BottomSheetModal>(null);

  const navigation = useNavigation();

  const onItemPress = (item: React.SetStateAction<IProfileOptions>) => {
    setSelectedItem(item);
    flatListRef.current.scrollToOffset({offset: 0});
    shareBottomSheet.current?.close();
  };
  useEffect(() => {
    setEntertainmentData([]);
    setFilteredEntertainmentData([]); // Reset filtered data when tab changes
    setScores(['all']); // Reset scores when tab changes
    const payload: IEntertainmentContent = {
      type: selectedItem.serverName,
    };
    if (
      [
        ENTERTAINMENTS_ENUMS.GOOD_DEEDS,
        ENTERTAINMENTS_ENUMS.JOKES,
        ENTERTAINMENTS_ENUMS.QUOTES,
      ].includes(selectedItem.serverName)
    ) {
      setLoading(true);
      getEntertainmentsContentRequest(payload, res => {
        setEntertainmentData(res);
        setLoading(false);
      });
    } else {
      getQuizPreviewRequest(res => {
        setEntertainmentData(res);
        setFilteredEntertainmentData(res);
      });
    }
  }, [selectedItem]);

  useEffect(() => {
    if (route?.params?.tab) {
      setSelectedItem(route.params.tab);
    }
  }, [route]);

  const onSharePress = (
    item: IEntertainmentContentResponse | IQuizContentResponse,
  ) => {
    shareBottomSheet.current?.present();
    setShareItem(item);
  };

  const onStartPress = (item: IQuizContentResponse) => {
    (
      navigation.navigate as (
        route: string,
        params: {item: IQuizContentResponse},
      ) => void
    )(Routes.ENTERTAINMENT_QUIZ, {
      item: item,
    });
  };

  const renderItem = (
    item: IEntertainmentContentResponse | IQuizContentResponse,
  ) => {
    if ('valueType' in item) {
      return (
        <EntertainmentItem
          item={item as IEntertainmentContentResponse}
          onSharePress={onSharePress}
        />
      );
    } else {
      return (
        <QuizItem
          item={item as IQuizContentResponse}
          onSharePress={() => onSharePress(item)}
          onQuizPress={item => onStartPress(item)}
        />
      );
    }
  };

  const renderHorizontalList = () => {
    return (
      <View style={styles.white}>
        <View style={styles.border} />
        <EntertainmentList
          entertainmentsList={entertainmentsList}
          onItemPress={item => onItemPress(item)}
          selectedItem={selectedItem}
        />
      </View>
    );
  };

  const handleFilterPress = (scoreName: string) => {
    setScores(prevScores => {
      let updatedScores: string[];

      if (scoreName === 'all') {
        updatedScores = ['all'];
      } else {
        updatedScores = prevScores.includes(scoreName)
          ? prevScores.filter(score => score !== scoreName)
          : [...prevScores.filter(score => score !== 'all'), scoreName];

        if (updatedScores.length === 0) {
          updatedScores = ['all'];
        }
      }
      if (!_.isEqual(prevScores, updatedScores)) {
        updateQuizItem(updatedScores);
      }

      return updatedScores;
    });
  };

  const renderFilter = () => {
    return (
      <View
        style={{
          paddingTop: Metrics.ratio(8),
          backgroundColor: Colors.gray,
        }}>
        <ImprintScoresList
          selectedScores={scores}
          isPersonal={false}
          onScoresPress={handleFilterPress}
          data={QUIZ_FILTER_LIST}
        />
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <View
        style={
          hasNotch() ? styles.statusBar : styles.statusBarWithoutNotch
        }></View>
      <CustomNavbar
        title={ENTERTAINMENTS.TITLE}
        hasBack={true}
        leftBtnPress={() => {
          navigation.goBack();
        }}
      />
      {renderHorizontalList()}
      {selectedItem.name === ENTERTAINMENTS_LIST.QUIZ && renderFilter()}

      <FlatList
        bounces={false}
        ref={flatListRef}
        contentContainerStyle={[
          styles.listContainer,
          selectedItem.name === ENTERTAINMENTS_LIST.QUIZ && styes.paddingTop0,
        ]}
        data={
          selectedItem.name === ENTERTAINMENTS_LIST.QUIZ
            ? QuizData
            : selectedItem.name === ENTERTAINMENTS_LIST.GAMES
            ? []
            : entertainmentData
        }
        renderItem={({
          item,
        }: {
          item: IEntertainmentContentResponse | IQuizContentResponse;
        }) => renderItem(item)}
        ListEmptyComponent={() =>
          selectedItem.name === ENTERTAINMENTS_LIST.GAMES && (
            <Text style={styles.soon}>Coming Soon</Text>
          )
        }
        // ListHeaderComponent={renderFilter}
      />
      <Loader loading={loading} />
      <ShareModal
        shareModalRef={shareBottomSheet}
        title={
          'valueType' in shareItem ? shareItem?.title : shareItem?.reward?.title
        }
        description={
          'valueType' in shareItem
            ? shareItem?.body
            : shareItem.reward?.description
        }
        type={
          'valueType' in shareItem ? shareItem?.valueType : shareItem.category
        }
      />
    </View>
  );
};

const mapStateToProps = (state: any) => ({
  QuizData: state.entertainmentQuiz.filterData,
  user: state.user,
});

const actions = {
  getEntertainmentsContentRequest,
  getQuizPreviewRequest,
  updateQuizItem,
};

export default connect(mapStateToProps, actions)(Entertainments);
