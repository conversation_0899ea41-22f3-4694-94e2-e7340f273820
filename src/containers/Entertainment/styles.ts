import {StyleSheet} from 'react-native';
import {Colors, Metrics} from '../../theme';
import {getStatusBarHeight} from 'react-native-iphone-x-helper';

export default StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: 0,
  },
  statusBar: {
    backgroundColor: Colors.white,
    height: getStatusBarHeight() + 30,
  },
  statusBarWithoutNotch: {
    backgroundColor: Colors.white,
    height: Metrics.ratio(30),
  },

  white: {
    backgroundColor: Colors.white,
    borderBottomColor: Colors.gray,
    width: Metrics.screenWidth,
    zIndex: 9999,
  },
  border: {
    height: Metrics.ratio(1),
    backgroundColor: Colors.gray,
    marginHorizontal: Metrics.ratio(24),
  },
  listContainer: {
    flexGrow: 1,
    paddingVertical: Metrics.ratio(20),
    backgroundColor: Colors.background.home,
  },
  soon: {
    fontSize: Metrics.ratio(20),
    color: Colors.black,
    fontWeight: 'bold',
    marginLeft: Metrics.doubleBaseMargin,
  },
});
