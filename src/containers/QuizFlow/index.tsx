import _ from 'lodash';
import {connect} from 'react-redux';
import React, {useEffect, useState} from 'react';
import {SafeAreaView} from 'react-native-safe-area-context';
import {
  IQuizResponse,
  ISelectedAnswer,
  ISubmitQuizPayload,
  Score,
  UserInfo,
} from '../../types';
import {Text, AppButton, Loader} from '../../components/';

import {View, FlatList} from 'react-native';
import styles from './styles';
import {AppStyles, Colors, Metrics} from '../../theme';
import {
  userLogout,
  userProfileQuizRequest,
  userSubmitProfileQuiz,
} from '../../actions/UserActions';
import {useNavigation} from '@react-navigation/native';
import {COMMON, QUIZ_FLOW} from '../../constants/StringConstants';
import QuestionItem from '../../components/QuizQuestion';
import Routes from '../../constants/RouteConstants';
import ProgressBar from '../../components/CreateProfile/ProgressBar';
import PublishButton from '../../components/PublishButton';
import util from '../../util';

interface QuizFlowProps {
  userProfileQuizRequest: (payload: any, callback: (res: any) => void) => void;
  userSubmitProfileQuiz: (
    payload: ISubmitQuizPayload,
    callback: (res: any) => void,
  ) => void;

  user: UserInfo;
  userLogout: () => void;
}

const QuizFlow: React.FC<QuizFlowProps> = ({
  userProfileQuizRequest,
  userSubmitProfileQuiz,
  userLogout,
  user,
}) => {
  const [questions, setQuestions] = useState<IQuizResponse>();
  const [selectedAnswers, setSelectedAnswers] = useState<ISelectedAnswer[]>([]);
  const [loading, setLoading] = useState<boolean>(false);

  const navigation = useNavigation();

  useEffect(() => {
    const payload = {
      page: 1,
      limit: 10,
    };
    setLoading(true);
    userProfileQuizRequest(payload, res => {
      setLoading(false);

      setQuestions(res.data[0]);
    });
  }, []);

  const handleOptionPress = (questionId: string, optionId: string) => {
    setSelectedAnswers((prevState: ISelectedAnswer[]) => {
      const existingAnswerIndex = prevState.findIndex(
        (answer: ISelectedAnswer) => answer.questionId === questionId,
      );
      if (existingAnswerIndex >= 0) {
        const updatedAnswers = [...prevState];
        updatedAnswers[existingAnswerIndex] = {questionId, optionId};
        return updatedAnswers;
      } else {
        return [...prevState, {questionId, optionId}];
      }
    });
  };

  const handleSubmit = () => {
    const payload: ISubmitQuizPayload = {
      answers: selectedAnswers.map((filter: any) => filter.optionId),
      quizId: questions?.id,
    };
    userSubmitProfileQuiz(payload, res => {
      if (res) {
        (
          navigation.navigate as (
            route: string,
            params: {user: UserInfo},
          ) => void
        )(Routes.QUIZ_PROFILE, {
          user: user,
        });
      }
    });
  };
  const WelcomeMessage = () => {
    return (
      <View style={AppStyles.baseMargin}>
        <Text
          size={'normal'}
          style={{marginHorizontal: Metrics.ratio(20)}}
          color={Colors.text.homeTitleColor}
          textAlign="center">
          {`Welcome, `}
          <Text
            color={Colors.black}
            style={{fontWeight: 'bold'}}
            textAlign="center">
            {user.userName}
          </Text>
          {`${QUIZ_FLOW.TITLE.split('value profile')[0]}`}
          <Text color={Colors.black} style={{fontWeight: 'bold'}}>
            {QUIZ_FLOW.VALUE_PROFILE}
          </Text>
          {`${QUIZ_FLOW.TITLE.split('value profile')[1]}`}
        </Text>
      </View>
    );
  };

  return (
    <SafeAreaView style={[AppStyles.flex]}>
      <View
        style={{
          flexDirection: 'row',
          alignSelf: 'flex-end',
          marginRight: Metrics.ratio(20),
        }}>
        <PublishButton
          size="xxSmall"
          buttonTitle={COMMON.LOGOUT}
          isPublish={true}
          onPress={() => util.handleUserLogout(userLogout, navigation)}
        />
      </View>
      {WelcomeMessage()}
      <Text style={styles.subTitle} size={'xxSmall'} color={Colors.black}>
        {QUIZ_FLOW.SUB_TITLE}
      </Text>
      <ProgressBar
        steps={selectedAnswers.length}
        maxSteps={10}
        width={Metrics.screenWidth * 0.9}
        style={{marginVertical: Metrics.ratio(20), alignSelf: 'center'}}
        unfilledColor={Colors.gray2}
      />

      <View style={styles.container}>
        <FlatList
          data={questions?.questions}
          keyExtractor={item => item.id}
          renderItem={({item}) => (
            <QuestionItem
              item={item}
              selectedAnswers={selectedAnswers}
              handleOptionPress={handleOptionPress}
            />
          )}
        />
        <AppButton
          buttonStye={
            selectedAnswers.length === 10
              ? styles.enableButton
              : styles.disableButton
          }
          text={COMMON.SUBMIT}
          textColor={
            selectedAnswers.length === 10 ? Colors.white : Colors.black
          }
          onPress={handleSubmit}
          disabled={selectedAnswers.length === 10 ? false : true}
        />
        <Loader loading={loading} />
      </View>
    </SafeAreaView>
  );
};

const mapStateToProps = (state: any) => ({
  user: state.user.userInfo,
});

const actions = {userProfileQuizRequest, userSubmitProfileQuiz, userLogout};

export default connect(mapStateToProps, actions)(QuizFlow);
