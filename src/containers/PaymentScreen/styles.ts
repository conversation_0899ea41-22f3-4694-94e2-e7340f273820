// @flow
import {StyleSheet} from 'react-native';
import {Colors, AppStyles, Metrics} from '../../theme';
import {getStatusBarHeight} from 'react-native-iphone-x-helper';

export default StyleSheet.create({
  container: {
    //  marginTop: Metrics.ratio(50),
    flex: 1,
  },
  statusBar: {
    backgroundColor: Colors.white,
    height: getStatusBarHeight() + 30,
  },
  statusBarWithoutNotch: {
    backgroundColor: Colors.white,
    height: 30,
  },
});
