import React, {FunctionComponent, useState} from 'react';
import {KeyboardAvoidingView, Platform, View} from 'react-native';
import styles from './styles'; // Import styles if required
import WebView from 'react-native-webview';
import {Loader} from '../../components';
import {useNavigation} from '@react-navigation/native';
import {hasNotch} from 'react-native-device-info';
import Routes from '../../constants/RouteConstants';
import {showToastMsg} from '../../components/Alert';
import {userInfoRequest} from '../../actions/UserActions';

interface PaymentScreenProps {
  route: {
    params: {
      url: string;
    };
  };
}

const PaymentScreen: FunctionComponent<PaymentScreenProps> = ({route}) => {
  const [loading, setLoading] = useState<boolean>(true);

  const navigation = useNavigation();

  const handleNavigationStateChange = (navState: {
    url: string;
    navigationType: string;
  }) => {
    const {url, navigationType} = navState;

    // if (navigationType === 'click') {
    //   navigation.goBack();
    //   return;
    // }

    // Helper function to get query parameters from a URL
    const getQueryParam = (url, param) => {
      const paramsString = url.split('?')[1];
      if (!paramsString) return null;

      const params = paramsString.split('&').reduce((acc, current) => {
        const [key, value] = current.split('=');
        acc[key] = value ? decodeURIComponent(value) : '';
        return acc;
      }, {});

      return params[param];
    };

    // Check for 'error=true' in the main URL or in 'callbackUrl' parameter
    const callbackUrl = getQueryParam(url, 'callbackUrl');
    if (callbackUrl && callbackUrl.includes('error=true')) {
      navigation.goBack();
    } else if (callbackUrl && callbackUrl.includes('flag=true')) {
      navigation.goBack();
    } else if (callbackUrl && callbackUrl.includes('success=true')) {
      showToastMsg('Payment Successful', 'success');
      navigation.reset({
        routes: [{name: Routes.HOME_TABS as never}],
      });
    }
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior="padding"
      enabled={Platform.OS === 'android'}>
      <View
        style={
          hasNotch() ? styles.statusBar : styles.statusBarWithoutNotch
        }></View>
      <WebView
        cacheMode="LOAD_NO_CACHE"
        source={{uri: route.params.url}}
        allowsInlineMediaPlayback={true}
        mediaPlaybackRequiresUserAction={false}
        javaScriptEnabled={true}
        domStorageEnabled={true}
        originWhitelist={['*']}
        useWebKit={true}
        allowsAirPlayForMediaPlayback={true}
        onLoad={() => setLoading(false)}
        onNavigationStateChange={handleNavigationStateChange}
      />

      <Loader loading={loading} />
    </KeyboardAvoidingView>
  );
};

export default PaymentScreen;
