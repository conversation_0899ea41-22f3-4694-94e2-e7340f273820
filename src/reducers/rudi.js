import Immutable from 'seamless-immutable';
import _ from 'lodash';
import {GET_RUDI_MESSAGE} from '../actions/ActionTypes';

const initialState = Immutable({
  rudiMessage: {},
});

export default (state = initialState, action) => {
  switch (action.type) {
    case GET_RUDI_MESSAGE.SUCCESS: {
      const {responseCallback, payload} = action;
      if (payload.page === 1) {
        return Immutable.merge(state, {
          rudiMessage: responseCallback,
        });
      }
    }

    default:
      return state;
  }
};
