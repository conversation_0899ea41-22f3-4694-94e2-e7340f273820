// @flow
import Immutable from 'seamless-immutable';
import _ from 'lodash';
import {
  GET_FRIEND_REQUEST_SENDER,
  USER_FOLLOWERS,
} from '../actions/ActionTypes';

const initialState = Immutable({
  data: [],
  followRequestData: [],
});

export default (state = initialState, action) => {
  switch (action.type) {
    case USER_FOLLOWERS.SUCCESS: {
      const {response} = action;

      return Immutable.merge(state, {
        data: response,
      });
    }

    case GET_FRIEND_REQUEST_SENDER.SUCCESS: {
      const {response} = action;

      return Immutable.merge(state, {
        followRequestData: response,
      });
    }

    default:
      return state;
  }
};
