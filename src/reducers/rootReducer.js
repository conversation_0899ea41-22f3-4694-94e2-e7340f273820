import {combineReducers} from 'redux';
import user from './user'; // Adjust the import path as necessary
import timeline from './timeline';
import followers from './followers';
import profile from './profile';
import generalReducer from './generalReducer';
import analytics from './analytics';
import notification from './notification';
import rudi from './rudi';
import subscription from './subscription';
import entertainmentQuiz from './entertainmentQuiz';
import search from './search';

const rootReducer = combineReducers({
  user,
  timeline,
  followers,
  profile,
  generalReducer,
  analytics,
  notification,
  rudi,
  subscription,
  entertainmentQuiz,
  search,
});

export default rootReducer;
