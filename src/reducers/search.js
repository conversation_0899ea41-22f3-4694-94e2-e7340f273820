// @flow
import Immutable from 'seamless-immutable';
import _ from 'lodash';
import {
  FREE_TEXT_SEARCH_USERS,
  FREE_TEXT_SEARCH_IMPRINTS,
  IMPRINT_VERIFICATION_REQUEST,
  POST_REACTION,
  IMPRINT_BOOKMARK,
  REQUEST_FOLLOW,
  DELETE_IMPRINT,
  UPDATE_REDFLAG_STATUS,
} from '../actions/ActionTypes';

const initialState = Immutable({
  recentFriendsForEmptySearch: [],
  recentImprintsForEmptySearch: [],
});

export default (state = initialState, action) => {
  switch (action.type) {
    case FREE_TEXT_SEARCH_USERS.SUCCESS: {
      const {responseCallback} = action;

      return Immutable.merge(state, {
        recentFriendsForEmptySearch: responseCallback.items,
      });
    }

    case FREE_TEXT_SEARCH_IMPRINTS.SUCCESS: {
      const {response} = action;
      if (action.payload.page === 1) {
        return Immutable.merge(state, {
          recentImprintsForEmptySearch: response,
        });
      } else {
        return Immutable.merge(state, {
          recentImprintsForEmptySearch:
            state.recentImprintsForEmptySearch.concat(response),
        });
      }
    }

    case IMPRINT_VERIFICATION_REQUEST.SUCCESS: {
      const {response} = action;
      if (action.isGlobal) {
        return Immutable.merge(state, {
          recentImprintsForEmptySearch: state.recentImprintsForEmptySearch.map(
            item => {
              if (item.id === response.id) {
                return Immutable.merge(item, {
                  verificationRequest: response.verificationRequest,
                });
              }
              return item;
            },
          ),
        });
      }
    }

    case UPDATE_REDFLAG_STATUS: {
      const {imprintId, isGlobal} = action.payload;

      if (isGlobal) {
        return Immutable.set(
          state,
          'recentImprintsForEmptySearch',
          state.recentImprintsForEmptySearch.map(item =>
            item.id === imprintId ? {...item, isFlagged: true} : item,
          ),
        );
      }
    }

    case POST_REACTION.SUCCESS: {
      const {response} = action;
      let updatedData;

      if (action.isGlobal) {
        updatedData = state.recentImprintsForEmptySearch.map(item => {
          // Check if the interaction with the response ID exists
          const existingInteractionIndex =
            item.userImprintInteractions.findIndex(
              interaction => interaction.id === response?.id,
            );

          // If the interaction doesn't exist, create a new one and append it
          if (
            existingInteractionIndex === -1 &&
            item.id === response?.imprintId
          ) {
            const newInteraction = Immutable.set({
              reaction: response?.reaction,
              isRedFlagged: response?.isRedFlagged,
              isBookmarked: response?.isBookmarked,
              isHide: response?.isHide,
              imprintId: response?.imprintId,
              id: response?.id,
              userId: response?.userId,
              user: {
                id: response?.userId,
              },
            });

            return Immutable.merge(item, {
              userImprintInteractions:
                item.userImprintInteractions.concat(newInteraction),
            });
          }

          // If the interaction exists, update it
          const updatedInteractions = item.userImprintInteractions.map(
            interaction => {
              if (interaction.id === response?.id) {
                return Immutable.merge(interaction, {
                  reaction: response?.reaction,
                  isRedFlagged: response?.isRedFlagged,
                  isBookmarked: response?.isBookmarked,
                  isHide: response?.isHide,
                });
              }
              return interaction;
            },
          );

          return Immutable.merge(item, {
            userImprintInteractions: updatedInteractions,
          });
        });
      }

      if (action.isGlobal) {
        return Immutable.merge(state, {
          recentImprintsForEmptySearch: updatedData,
        });
      }
    }

    case IMPRINT_BOOKMARK.SUCCESS: {
      const {response} = action;
      let updatedData;

      if (action.isGlobal) {
        updatedData = state.recentImprintsForEmptySearch.map(item => {
          // Check if the interaction with the response ID exists
          const existingInteractionIndex =
            item.userImprintInteractions.findIndex(
              interaction => interaction.id === response?.id,
            );

          // If the interaction doesn't exist, create a new one and append it
          if (
            existingInteractionIndex === -1 &&
            item.id === response?.imprintId
          ) {
            const newInteraction = Immutable.set({
              reaction: response?.reaction,
              isRedFlagged: response?.isRedFlagged,
              isBookmarked: response?.isBookmarked,
              isHide: response?.isHide,
              imprintId: response?.imprintId,
              id: response?.id,
              userId: response?.userId,
              user: {
                id: response?.userId,
              },
            });

            return Immutable.merge(item, {
              userImprintInteractions:
                item.userImprintInteractions.concat(newInteraction),
            });
          }

          // If the interaction exists, update isBookmarked
          const updatedInteractions = item.userImprintInteractions.map(
            interaction => {
              if (interaction.id === response?.id) {
                return Immutable.merge(interaction, {
                  isBookmarked: response?.isBookmarked,
                });
              }
              return interaction;
            },
          );

          return Immutable.merge(item, {
            userImprintInteractions: updatedInteractions,
          });
        });
      }

      if (action.isGlobal) {
        return Immutable.merge(state, {
          recentImprintsForEmptySearch: updatedData,
        });
      }
    }
    case REQUEST_FOLLOW.SUCCESS: {
      const {response} = action;
      return Immutable.merge(state, {
        recentImprintsForEmptySearch: state.recentImprintsForEmptySearch.map(
          item =>
            item.id === response.to
              ? Immutable.merge(item, {followRequestSent: true})
              : item,
        ),
        recentFriendsForEmptySearch: state.recentFriendsForEmptySearch.map(
          item =>
            item.id === response.to
              ? Immutable.merge(item, {followRequestSent: true})
              : item,
        ),
      });
    }

    case DELETE_IMPRINT.SUCCESS: {
      const {response} = action;
      const {id} = response;
      const {userId} = response;

      if (id) {
        const updatedGlobalTimeline = state.recentImprintsForEmptySearch.filter(
          imprint => imprint.id !== id,
        );

        return Immutable.merge(state, {
          recentImprintsForEmptySearch: updatedGlobalTimeline,
        });
      } else {
        const updatedGlobalTimeline = state.recentImprintsForEmptySearch.filter(
          imprint => imprint.userId !== userId,
        );

        return Immutable.merge(state, {
          recentImprintsForEmptySearch: updatedGlobalTimeline,
        });
      }
    }

    default:
      return state;
  }
};
