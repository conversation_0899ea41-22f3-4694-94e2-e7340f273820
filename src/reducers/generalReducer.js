// @flow
import Immutable from 'seamless-immutable';
import _ from 'lodash';
import {REFRESH_PAGE} from '../actions/ActionTypes';

const initialState = Immutable({
  isRefreshPage: false,
});

export default (state = initialState, action) => {
  switch (action.type) {
    case REFRESH_PAGE: {
      const {payload} = action;
      let isRefreshPage = _.cloneDeep(state.isRefreshPage);
      isRefreshPage = payload;
      return Immutable.merge(state, {
        isRefreshPage: isRefreshPage,
      });
    }

    default:
      return state;
  }
};
