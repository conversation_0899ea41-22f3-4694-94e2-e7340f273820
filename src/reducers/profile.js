import Immutable from 'seamless-immutable';
import _ from 'lodash';
import {
  ADD_EDUCATION,
  ADD_EMPLOYMENT,
  ADD_FAMILY,
  COMPLETE_PROFiLE,
  DELETE_EDUCATION,
  EDIT_IMAGE,
  EDUCATION_DATA,
  EMPTY_INSTITUTE,
  EMPTY_USERS,
  GET_INSTITUTE,
  GET_OTHER_USER_DETAILS,
  GET_RELATION_ENUMS,
  UPDATED_PROFILE_OVERVIEW,
  UPDATE_CONTACT,
  UPDATE_EDUCATION,
  UPDATE_EMPLOYMENT,
  UPDATE_PROFILE_ABOUT,
  USER_SEARCH,
  DELETE_FAMILY,
  UPDATE_FAMILY,
} from '../actions/ActionTypes';

const initialState = Immutable({
  userInfo: {},
  completeProfile: {},
  institute: {},
  users: {},
  educationData: {},
  relationshipEnum: {},
  otherUserDetails: {},
});

export default (state = initialState, action) => {
  switch (action.type) {
    case EDIT_IMAGE.SUCCESS: {
      const userInfo = action.payload;
      return Immutable.merge(state, {
        userInfo: userInfo,
      });
    }

    case COMPLETE_PROFiLE.SUCCESS: {
      const {response} = action;
      return Immutable.merge(state, {
        completeProfile: response,
      });
    }

    case UPDATED_PROFILE_OVERVIEW.SUCCESS: {
      const response = action.responseCallback;
      const updatedProfile = Immutable.merge(state.completeProfile, {
        overview: {
          ...state.completeProfile.overview,
          what_i_stand_for: response.what_i_stand_for,
          what_i_value: response.what_i_value,
          displayName: response.name,
          userName: response.username,
        },
      });
      return Immutable.merge(state, {
        completeProfile: updatedProfile,
      });
    }

    case UPDATE_PROFILE_ABOUT.SUCCESS: {
      const response = action.responseCallback;

      const updatedAbout = Immutable.merge(state.completeProfile, {
        about: response.about,
      });
      return Immutable.merge(state, {
        completeProfile: updatedAbout,
      });
    }

    case GET_INSTITUTE.SUCCESS: {
      const response = action.responseCallback;
      return Immutable.merge(state, {
        institute: response,
      });
    }

    case EMPTY_INSTITUTE.SUCCESS: {
      return Immutable.merge(state, {
        institute: {},
      });
    }

    case EMPTY_USERS.SUCCESS: {
      return Immutable.merge(state, {
        users: {},
      });
    }

    case EDUCATION_DATA.SUCCESS: {
      const response = action.payload;
      return Immutable.merge(state, {
        educationData: response,
      });
    }

    case ADD_EDUCATION.SUCCESS: {
      const response = action.responseCallback;
      const updatedEducation = [...state.completeProfile.education, response];
      const updatedProfile = Immutable.merge(state.completeProfile, {
        education: updatedEducation,
      });
      return Immutable.merge(state, {
        completeProfile: updatedProfile,
      });
    }

    case UPDATE_EDUCATION.SUCCESS: {
      const response = action.responseCallback;

      const updatedEducation = state.completeProfile.education.map(edu => {
        if (edu.id === response.id) {
          return {
            ...edu,
            ...response,
          };
        }
        return edu;
      });

      const updatedProfile = {
        ...state.completeProfile,
        education: updatedEducation,
      };

      return Immutable.merge(state, {
        completeProfile: updatedProfile,
      });
    }

    case UPDATE_FAMILY.SUCCESS: {
      const response = action.responseCallback;

      const updatedFamily = state.completeProfile.family.map(fam => {
        if (fam.id === response.id) {
          return {
            ...fam,
            ...response,
          };
        }
        return fam;
      });

      const updatedProfile = {
        ...state.completeProfile,
        family: updatedFamily,
      };

      return Immutable.merge(state, {
        completeProfile: updatedProfile,
      });
    }

    case DELETE_EDUCATION.SUCCESS: {
      const response = action.responseCallback;
      const updatedEducation = state.completeProfile.education.filter(
        edu => edu.id !== response.id,
      );

      const updatedProfile = Immutable.merge(state.completeProfile, {
        education: updatedEducation,
      });

      return Immutable.merge(state, {
        completeProfile: updatedProfile,
      });
    }

    case DELETE_FAMILY.SUCCESS: {
      const id = action.payload?.id;

      const updateFamily = state.completeProfile.family.filter(
        fam => fam.id !== id,
      );

      const updatedFamily = Immutable.merge(state.completeProfile, {
        family: updateFamily,
      });

      return Immutable.merge(state, {
        completeProfile: updatedFamily,
      });
    }

    case UPDATE_EMPLOYMENT.SUCCESS: {
      const response = action.responseCallback;

      const updatedEmployment = state.completeProfile.employment.map(emp => {
        if (emp.id === response.id) {
          return {
            ...emp,
            ...response,
          };
        }
        return emp;
      });

      const updatedProfile = {
        ...state.completeProfile,
        employment: updatedEmployment,
      };

      return Immutable.merge(state, {
        completeProfile: updatedProfile,
      });
    }

    case ADD_EMPLOYMENT.SUCCESS: {
      const response = action.responseCallback;
      const updatedEmployment = [...state.completeProfile.employment, response];
      const updatedProfile = Immutable.merge(state.completeProfile, {
        employment: updatedEmployment,
      });
      return Immutable.merge(state, {
        completeProfile: updatedProfile,
      });
    }

    case ADD_FAMILY.SUCCESS: {
      const response = action.responseCallback;
      const updatedFamily = [...state.completeProfile.family, response];
      const updatedProfile = Immutable.merge(state.completeProfile, {
        family: updatedFamily,
      });
      return Immutable.merge(state, {
        completeProfile: updatedProfile,
      });
    }

    case USER_SEARCH.SUCCESS: {
      const response = action.responseCallback;
      return Immutable.merge(state, {
        users: response,
      });
    }

    case GET_RELATION_ENUMS.SUCCESS: {
      const response = action.responseCallback;
      return Immutable.merge(state, {
        relationshipEnum: response,
      });
    }

    case GET_OTHER_USER_DETAILS.SUCCESS: {
      const response = action.responseCallback;
      return Immutable.merge(state, {
        otherUserDetails: response,
      });
    }

    case UPDATE_CONTACT.SUCCESS: {
      const response = action.responseCallback;
      const updatedContact = Immutable.merge(state.completeProfile, {
        contact: response,
      });
      return Immutable.merge(state, {
        completeProfile: updatedContact,
      });
    }

    default:
      return state;
  }
};
