import Immutable from 'seamless-immutable';
import _ from 'lodash';
import {
  DELETE_IMPRINT,
  GET_FILTERED_TIMELINE,
  GET_USER_TIMELINE,
  GLOBAL_TIMELINE,
  IMPRINT_BOOKMARK,
  IMPRINT_SHOUT_OUT,
  IMPRINT_VERIFICATION_REQUEST,
  PERSONAL_TIMELINE,
  POST_REACTION,
  REQUEST_FOLLOW,
  RESET_TIMELINE,
  USER_LOGOUT,
  UPDATE_REDFLAG_STATUS,
  UPDATE_TIME_LINE,
} from '../actions/ActionTypes';

const initialState = Immutable({
  globalTimeLine: [],
  personalTimeLine: [],
});

export default (state = initialState, action) => {
  switch (action.type) {
    case GLOBAL_TIMELINE.SUCCESS: {
      const {response} = action;
      if (action.payload.page === 1) {
        return Immutable.merge(state, {
          globalTimeLine: response,
        });
      } else {
        return Immutable.merge(state, {
          globalTimeLine: state.globalTimeLine.concat(response),
        });
      }
    }
    case PERSONAL_TIMELINE.SUCCESS: {
      const {response} = action;
      if (action.payload.page === 1) {
        return Immutable.merge(state, {
          personalTimeLine: response,
        });
      } else {
        return Immutable.merge(state, {
          personalTimeLine: state.personalTimeLine.concat(response),
        });
      }
    }

    case GET_USER_TIMELINE.SUCCESS: {
      const {response} = action;

      if (action.payload.page === 1) {
        return Immutable.merge(state, {
          personalTimeLine: response,
        });
      } else {
        return Immutable.merge(state, {
          personalTimeLine: state.personalTimeLine.concat(response),
        });
      }
    }

    case GET_FILTERED_TIMELINE.SUCCESS: {
      const {response} = action;
      if (action.payload.page === 1) {
        return Immutable.merge(state, {
          personalTimeLine: response,
        });
      } else {
        return Immutable.merge(state, {
          personalTimeLine: state.personalTimeLine.concat(response),
        });
      }
    }
    case POST_REACTION.SUCCESS: {
      const {response} = action;
      let updatedData;

      if (action.isGlobal) {
        updatedData = state.globalTimeLine.map(item => {
          // Check if the interaction with the response ID exists
          const existingInteractionIndex =
            item.userImprintInteractions.findIndex(
              interaction => interaction.id === response?.id,
            );

          // If the interaction doesn't exist, create a new one and append it
          if (
            existingInteractionIndex === -1 &&
            item.id === response?.imprintId
          ) {
            const newInteraction = Immutable.set({
              reaction: response?.reaction,
              isRedFlagged: response?.isRedFlagged,
              isBookmarked: response?.isBookmarked,
              isHide: response?.isHide,
              imprintId: response?.imprintId,
              id: response?.id,
              userId: response?.userId,
              user: {
                id: response?.userId,
              },
            });

            return Immutable.merge(item, {
              userImprintInteractions:
                item.userImprintInteractions.concat(newInteraction),
            });
          }

          // If the interaction exists, update it
          const updatedInteractions = item.userImprintInteractions.map(
            interaction => {
              if (interaction.id === response?.id) {
                return Immutable.merge(interaction, {
                  reaction: response?.reaction,
                  isRedFlagged: response?.isRedFlagged,
                  isBookmarked: response?.isBookmarked,
                  isHide: response?.isHide,
                });
              }
              return interaction;
            },
          );

          return Immutable.merge(item, {
            userImprintInteractions: updatedInteractions,
          });
        });
      } else {
        updatedData = state.personalTimeLine.map(item => {
          // Check if the interaction with the response ID exists
          const existingInteractionIndex =
            item.userImprintInteractions.findIndex(
              interaction => interaction.id === response?.id,
            );

          // If the interaction doesn't exist, create a new one and append it
          if (
            existingInteractionIndex === -1 &&
            item.id === response?.imprintId
          ) {
            const newInteraction = Immutable.set({
              reaction: response?.reaction,
              isRedFlagged: response?.isRedFlagged,
              isBookmarked: response?.isBookmarked,
              isHide: response?.isHide,
              imprintId: response?.imprintId,
              id: response?.id,
              userId: response?.userId,
              user: {
                id: response?.userId,
              },
            });

            return Immutable.merge(item, {
              userImprintInteractions:
                item.userImprintInteractions.concat(newInteraction),
            });
          }

          // If the interaction exists, update it
          const updatedInteractions = item.userImprintInteractions.map(
            interaction => {
              if (interaction.id === response?.id) {
                return Immutable.merge(interaction, {
                  reaction: response?.reaction,
                  isRedFlagged: response?.isRedFlagged,
                  isBookmarked: response?.isBookmarked,
                  isHide: response?.isHide,
                });
              }
              return interaction;
            },
          );

          return Immutable.merge(item, {
            userImprintInteractions: updatedInteractions,
          });
        });
      }

      if (action.isGlobal) {
        return Immutable.merge(state, {globalTimeLine: updatedData});
      } else {
        return Immutable.merge(state, {personalTimeLine: updatedData});
      }
    }

    case UPDATE_REDFLAG_STATUS: {
      const {imprintId, isGlobal} = action.payload;
      const {id} = action.response;

      if (isGlobal) {
        return Immutable.set(
          state,
          'globalTimeLine',
          state.globalTimeLine.map(item =>
            item.id === imprintId ? {...item, isFlagged: true} : item,
          ),
        );
      } else {
        return Immutable.set(
          state,
          'personalTimeLine',
          state.personalTimeLine.map(item =>
            item.id === imprintId ? {...item, isFlagged: true} : item,
          ),
        );
      }
    }

    case UPDATE_TIME_LINE: {
      const {payload, isGlobal} = action;

      if (isGlobal) {
        return Immutable.set(
          state,
          'globalTimeLine',
          state.globalTimeLine.map(item =>
            item.id === payload.id
              ? {...item, moderationStatus: 'MODERATED'}
              : item,
          ),
        );
      } else {
        return Immutable.set(
          state,
          'personalTimeLine',
          state.personalTimeLine.map(item =>
            item.id === payload.id
              ? {...item, moderationStatus: 'MODERATED'}
              : item,
          ),
        );
      }
    }

    case USER_LOGOUT.SUCCESS:
    case RESET_TIMELINE.SUCCESS: {
      return Immutable.merge(state, {
        globalTimeLine: [],
        personalTimeLine: [],
      });
    }

    case DELETE_IMPRINT.SUCCESS: {
      const {response} = action;
      const {id} = response;
      const {userId} = response;

      if (id) {
        const updatedGlobalTimeline = state.globalTimeLine.filter(
          imprint => imprint.id !== id,
        );
        const updatedPersonalTimeline = state.personalTimeLine.filter(
          imprint => imprint.id !== id,
        );
        return Immutable.merge(state, {
          globalTimeLine: updatedGlobalTimeline,
          personalTimeLine: updatedPersonalTimeline,
        });
      } else {
        const updatedGlobalTimeline = state.globalTimeLine.filter(
          imprint => imprint.userId !== userId,
        );
        const updatedPersonalTimeline = state.personalTimeLine.filter(
          imprint => imprint.userId !== userId,
        );
        return Immutable.merge(state, {
          globalTimeLine: updatedGlobalTimeline,
          personalTimeLine: updatedPersonalTimeline,
        });
      }
    }

    case IMPRINT_VERIFICATION_REQUEST.SUCCESS: {
      const {response} = action;
      if (action.isGlobal) {
        return Immutable.merge(state, {
          globalTimeLine: state.globalTimeLine.map(item => {
            if (item.id === response.id) {
              return Immutable.merge(item, {
                verificationRequest: response.verificationRequest,
              });
            }
            return item;
          }),
        });
      } else {
        return Immutable.merge(state, {
          personalTimeLine: state.personalTimeLine.map(item => {
            if (item.id === response.id) {
              return Immutable.merge(item, {
                verificationRequest: response.verificationRequest,
              });
            }
            return item;
          }),
        });
      }
    }
    case IMPRINT_SHOUT_OUT.SUCCESS: {
      const {response} = action;
      if (response) {
        return Immutable.merge(state, {
          personalTimeLine: state.personalTimeLine.map(item => {
            if (item.id === response.id) {
              return Immutable.merge(item, {
                isGlobal: true,
              });
            }
            return item;
          }),
        });
      }
    }

    case IMPRINT_BOOKMARK.SUCCESS: {
      const {response} = action;
      let updatedData;

      if (action.isGlobal) {
        updatedData = state.globalTimeLine.map(item => {
          // Check if the interaction with the response ID exists
          const existingInteractionIndex =
            item.userImprintInteractions.findIndex(
              interaction => interaction.id === response?.id,
            );

          // If the interaction doesn't exist, create a new one and append it
          if (
            existingInteractionIndex === -1 &&
            item.id === response?.imprintId
          ) {
            const newInteraction = Immutable.set({
              reaction: response?.reaction,
              isRedFlagged: response?.isRedFlagged,
              isBookmarked: response?.isBookmarked,
              isHide: response?.isHide,
              imprintId: response?.imprintId,
              id: response?.id,
              userId: response?.userId,
              user: {
                id: response?.userId,
              },
            });

            return Immutable.merge(item, {
              userImprintInteractions:
                item.userImprintInteractions.concat(newInteraction),
            });
          }

          // If the interaction exists, update isBookmarked
          const updatedInteractions = item.userImprintInteractions.map(
            interaction => {
              if (interaction.id === response?.id) {
                return Immutable.merge(interaction, {
                  isBookmarked: response?.isBookmarked,
                });
              }
              return interaction;
            },
          );

          return Immutable.merge(item, {
            userImprintInteractions: updatedInteractions,
          });
        });
      } else {
        updatedData = state.personalTimeLine.map(item => {
          // Check if the interaction with the response ID exists
          const existingInteractionIndex =
            item.userImprintInteractions.findIndex(
              interaction => interaction.id === response?.id,
            );

          // If the interaction doesn't exist, create a new one and append it
          if (
            existingInteractionIndex === -1 &&
            item.id === response?.imprintId
          ) {
            const newInteraction = Immutable.set({
              reaction: response?.reaction,
              isRedFlagged: response?.isRedFlagged,
              isBookmarked: response?.isBookmarked,
              isHide: response?.isHide,
              imprintId: response?.imprintId,
              id: response?.id,
              userId: response?.userId,
              user: {
                id: response?.userId,
              },
            });

            return Immutable.merge(item, {
              userImprintInteractions:
                item.userImprintInteractions.concat(newInteraction),
            });
          }

          // If the interaction exists, update isBookmarked
          const updatedInteractions = item.userImprintInteractions.map(
            interaction => {
              if (interaction.id === response?.id) {
                return Immutable.merge(interaction, {
                  isBookmarked: response?.isBookmarked,
                });
              }
              return interaction;
            },
          );

          return Immutable.merge(item, {
            userImprintInteractions: updatedInteractions,
          });
        });
      }

      if (action.isGlobal) {
        return Immutable.merge(state, {globalTimeLine: updatedData});
      } else {
        return Immutable.merge(state, {personalTimeLine: updatedData});
      }
    }

    case REQUEST_FOLLOW.SUCCESS: {
      const {response} = action;
      if (action.isGlobal) {
        return Immutable.merge(state, {
          globalTimeLine: state.globalTimeLine.map(item => {
            if (item.userId === response.to) {
              return Immutable.merge(item, {
                followerRequestSent: true,
              });
            }
            return item;
          }),
        });
      } else {
        return Immutable.merge(state, {
          personalTimeLine: state.personalTimeLine.map(item => {
            if (item.userId === response.to) {
              return Immutable.merge(item, {
                followerRequestSent: true,
              });
            }
            return item;
          }),
        });
      }
    }

    default:
      return state;
  }
};
