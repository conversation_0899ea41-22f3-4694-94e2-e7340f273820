// @flow
import Immutable from 'seamless-immutable';
import _ from 'lodash';
import {
  GET_TIME_OPTIONS,
  GET_VALUE_ANALYTICS,
  GET_VALUE_PORTFOLIO,
} from '../actions/ActionTypes';

const initialState = Immutable({
  value: {},
  portfolio: {},
  timeOption: {},
});

export default (state = initialState, action) => {
  switch (action.type) {
    case GET_VALUE_ANALYTICS.SUCCESS: {
      const {response} = action;
      return state.merge({
        value: response,
      });
    }

    case GET_VALUE_PORTFOLIO.SUCCESS: {
      const {response} = action;
      return state.merge({
        portfolio: response,
      });
    }

    case GET_TIME_OPTIONS.SUCCESS: {
      const {response} = action;
      return state.merge({
        timeOption: response,
      });
    }

    default:
      return state;
  }
};
