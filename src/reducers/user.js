// @flow
import Immutable from 'seamless-immutable';
import _ from 'lodash';
import {
  USER_INFO,
  USER_LOGOUT,
  USER_SIGN_IN,
  UPDATED_PROFILE_OVERVIEW,
  APPLY_VOUCHER,
  SET_SYSTEM_ACTION,
  UPDATE_USER_SUBSCRIPTION,
  CLEAR_LOCAL_SUBSCRIPTION_UPDATE,
} from '../actions/ActionTypes';

const initialState = Immutable({
  data: {},
  access_token: '',
  userInfo: {},
  isAppliedVoucher: false,
  // ✅ Track local subscription updates to prevent overwrites
  localSubscriptionUpdate: null,
});

export default (state = initialState, action) => {
  switch (action.type) {
    case USER_SIGN_IN.SUCCESS: {
      const {response} = action;
      return Immutable.merge(state, {
        data: response,
        access_token: response.tokens.accessToken,
      });
    }
    case USER_INFO.SUCCESS: {
      const {response} = action;
      let updatedResponse = response;
      updatedResponse.userId = response.id;

      // ✅ Preserve local subscription update if it exists and is more recent
      if (state.localSubscriptionUpdate) {
        const localUpdateTime = state.localSubscriptionUpdate.timestamp;
        const currentTime = Date.now();

        // If local update is less than 5 minutes old, preserve it
        if (currentTime - localUpdateTime < 5 * 60 * 1000) {
          updatedResponse.subscriptionStatus =
            state.localSubscriptionUpdate.status;
        } else {
          // Local update is old, clear it and use server response
          return Immutable.merge(state, {
            userInfo: updatedResponse,
            localSubscriptionUpdate: null,
          });
        }
      }

      return Immutable.merge(state, {
        userInfo: updatedResponse,
      });
    }

    case SET_SYSTEM_ACTION: {
      return Immutable.merge(state, {
        userInfo: {
          ...state.user,
          setSystemActionForImprint: null,
          setSystemActionForChat: null,
          systemActionForUserReport: null,
        },
      });
    }

    case UPDATE_USER_SUBSCRIPTION: {
      return Immutable.merge(state, {
        userInfo: {
          ...state.userInfo,
          subscriptionStatus: 'active',
        },
        // ✅ Track this local update with timestamp
        localSubscriptionUpdate: {
          status: 'active',
          timestamp: Date.now(),
        },
      });
    }

    case USER_LOGOUT.SUCCESS: {
      return initialState;
    }

    case UPDATED_PROFILE_OVERVIEW.SUCCESS: {
      const overResponse = action.responseCallback;
      overResponse.userId = overResponse.id;

      return Immutable.merge(state, {
        userInfo: {
          ...state.userInfo,
          ...overResponse,
        },
      });
    }
    case APPLY_VOUCHER: {
      const appliedVoucher = action.payload;
      return Immutable.merge(state, {
        isAppliedVoucher: appliedVoucher,
      });
    }

    case CLEAR_LOCAL_SUBSCRIPTION_UPDATE: {
      return Immutable.merge(state, {
        localSubscriptionUpdate: null,
      });
    }

    default:
      return state;
  }
};
