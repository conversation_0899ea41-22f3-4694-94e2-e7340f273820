import Immutable from 'seamless-immutable';
import _ from 'lodash';
import {GET_NOTIFICATIONS, READ_NOTIFICATION} from '../actions/ActionTypes';

const initialState = Immutable({
  notifications: {},
});

export default (state = initialState, action) => {
  switch (action.type) {
    case GET_NOTIFICATIONS.SUCCESS: {
      const {responseCallback, payload} = action;
      if (payload.page === 1) {
        return Immutable.merge(state, {
          notifications: responseCallback,
        });
      } else {
        return Immutable.merge(state, {
          notifications: state.notifications.concat(responseCallback),
        });
      }
    }

    case READ_NOTIFICATION.SUCCESS: {
      const {responseCallback, payload} = action;
      const notificationId = responseCallback.notificationsModified[0].id;

      const notifications = _.cloneDeep(state.notifications);
      notifications.forEach(notification => {
        if (notification.id === notificationId) {
          notification.isRead = true;
        }
      });
      return Immutable.merge(state, {
        notifications,
      });
    }

    default:
      return state;
  }
};
