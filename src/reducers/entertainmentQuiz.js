import Immutable from 'seamless-immutable';
import {
  QUIZ_PREVIEW_LIST,
  QUIZ_UPDATE_ITEM,
  UPDATE_REWARD_ITEM,
} from '../actions/ActionTypes';

const initialState = Immutable({
  data: [],
  filterData: [],
});

export default (state = initialState, action) => {
  switch (action.type) {
    case QUIZ_PREVIEW_LIST.SUCCESS: {
      const {response} = action;
      return Immutable.merge(state, {
        data: response,
        filterData: response,
      });
    }

    case QUIZ_UPDATE_ITEM: {
      const {updatedItem} = action;

      if (updatedItem.includes('all')) {
        return Immutable.merge(state, {
          filterData: state.data,
        });
      }

      const filteredData = state.data.filter(item =>
        updatedItem.some(
          selectedScore =>
            item.category.toUpperCase() === selectedScore.toUpperCase(),
        ),
      );

      return Immutable.merge(state, {
        filterData: filteredData,
      });
    }
    case UPDATE_REWARD_ITEM: {
      const {updatedItem} = action;

      const updatedData = state.data.map(item => {
        if (item.id === updatedItem.quizId) {
          return {
            ...item,
            reward: updatedItem.reward,
            status: updatedItem.status,
            updatedAt: updatedItem.updatedAt,
          };
        }
        return item;
      });

      const updatedFilterData = state.filterData.map(item => {
        if (item.id === updatedItem.quizId) {
          return {
            ...item,
            reward: updatedItem.reward,
            status: updatedItem.status,
            updatedAt: new Date().toISOString(),
          };
        }
        return item;
      });

      return Immutable.merge(state, {
        data: updatedData,
        filterData: updatedFilterData,
      });
    }

    default:
      return state;
  }
};
