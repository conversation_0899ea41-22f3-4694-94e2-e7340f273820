// @flow
import Immutable from 'seamless-immutable';
import _ from 'lodash';
import {GET_PLANS} from '../actions/ActionTypes';

const initialState = Immutable({
  data: [],
});

export default (state = initialState, action) => {
  switch (action.type) {
    case GET_PLANS.SUCCESS: {
      const {data} = action;
      return Immutable.merge(state, {
        data: data,
      });
    }

    default:
      return state;
  }
};
