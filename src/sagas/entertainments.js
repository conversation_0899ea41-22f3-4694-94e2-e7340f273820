import {take, call, fork, put} from 'redux-saga/effects';
import {
  GET_ENTERTAINMENTS_CONTENT,
  SHARE_IMPRINT,
  QUIZ_PREVIEW_LIST,
  ENTERTAINMENT_QUIZ,
  ENTERTAINMENT_QUIZ_ATTEMPT,
  GET_NEWS,
} from '../actions/ActionTypes';
import {
  callRequest,
  GET_NEWS as GET_NEWS_URL,
  ENTERTAINMENT_QUIZ_ATTEMPT as ENTERTAINMENT_QUIZ_ATTEMPT_URL,
  ENTERTAINMENT_QUIZ as ENTERTAINMENT_QUIZ_URL,
  QUIZ_PREVIEW_LIST as QUIZ_PREVIEW_LIST_URL,
  SHARE_IMPRINT as SHARE_IMPRINT_URL,
  GET_ENTERTAINMENTS_CONTENT as GET_ENTERTAINMENTS_CONTENT_URL,
} from '../config/WebService';
import ApiSauce from '../services/ApiSauce';
import Util from '../util';
import {showToastMsg} from '../components/Alert';
import {APIConstants} from '../constants/APIConstants';
import {getQuizPreviewSuccess} from '../actions/EntertainmentActions';

function alert(message, type = 'error') {
  showToastMsg(message);
}

function* getEntertainmentsContent() {
  while (true) {
    const {payload, responseCallback} = yield take(
      GET_ENTERTAINMENTS_CONTENT.REQUEST,
    );

    const parameters = `${APIConstants.GET_ENTERTAINMENTS_CONTENT}type=${payload.type}`;
    try {
      const response = yield call(
        callRequest,
        GET_ENTERTAINMENTS_CONTENT_URL,
        {},
        parameters,
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}

function* shareImprint() {
  while (true) {
    const {payload, responseCallback} = yield take(SHARE_IMPRINT.REQUEST);

    try {
      const response = yield call(
        callRequest,
        SHARE_IMPRINT_URL,
        payload,
        '',
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}

function* quizPreview() {
  while (true) {
    const {responseCallback} = yield take(QUIZ_PREVIEW_LIST.REQUEST);

    try {
      const response = yield call(
        callRequest,
        QUIZ_PREVIEW_LIST_URL,
        {},
        '',
        {},
        ApiSauce,
      );
      if (response) {
        yield put(getQuizPreviewSuccess(response));
        if (responseCallback) responseCallback(response, null);
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}

function* entertainmentQuiz() {
  while (true) {
    const {payload, responseCallback} = yield take(ENTERTAINMENT_QUIZ.REQUEST);
    const parameters = `${APIConstants.ENTERTAINMENT_QUIZ}id=${payload.id}&page=${payload.page}&limit=${payload.limit}`;

    try {
      const response = yield call(
        callRequest,
        ENTERTAINMENT_QUIZ_URL,
        {},
        parameters,
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}

function* submitQuiz() {
  while (true) {
    const {payload, responseCallback} = yield take(
      ENTERTAINMENT_QUIZ_ATTEMPT.REQUEST,
    );

    try {
      const response = yield call(
        callRequest,
        ENTERTAINMENT_QUIZ_ATTEMPT_URL,
        payload,
        '',
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}

function* newsContent() {
  while (true) {
    const {payload, responseCallback} = yield take(GET_NEWS.REQUEST);
    let queryParams = `personal=${payload.personal}&page=${payload.page}&global=${payload.global}`;
    if (payload.filters && payload.filters.length > 0) {
      queryParams += payload.filters
        .map(filter => `&filters=${filter.toUpperCase()}`)
        .join('');
    }

    const url = `${APIConstants.GET_NEWS}?${queryParams}`;

    try {
      const response = yield call(
        callRequest,
        GET_NEWS_URL,
        {},
        url,
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}

export default function* root() {
  yield fork(getEntertainmentsContent);
  yield fork(shareImprint);
  yield fork(quizPreview);
  yield fork(entertainmentQuiz);
  yield fork(submitQuiz);
  yield fork(newsContent);
}
