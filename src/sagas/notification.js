import {take, call, fork, put} from 'redux-saga/effects';
import {
  GET_IMPRINT_BY_ID,
  GET_NOTIFICATIONS,
  GET_TRANSPARENCY_DATA,
  READ_NOTIFICATION,
  UPDATE_VERIFICATION,
  GET_PUB_SUB_TOKEN,
} from '../actions/ActionTypes';
import {
  callRequest,
  GET_PUB_SUB_TOKEN as GET_PUB_SUB_TOKEN_URL,
  GET_TRANSPARENCY_DATA as GET_TRANSPARENCY_DATA_URL,
  GET_NOTIFICATIONS as GET_NOTIFICATIONS_URL,
  READ_NOTIFICATION as READ_NOTIFICATION_URL,
  GET_IMPRINT_BY_ID as GET_IMPRINT_BY_ID_URL,
  UPDATE_VERIFICATION as UPDATE_VERIFICATION_URL,
} from '../config/WebService';
import ApiSauce from '../services/ApiSauce';
import Util from '../util';
import {showToastMsg} from '../components/Alert';
import {
  getNotificationSuccess,
  readNotificationSuccess,
} from '../actions/NotificationActions';
import {APIConstants} from '../constants/APIConstants';
import {navigateToEmptyScreen} from '../helpers/UserHelper';

function alert(message, type = 'error') {
  showToastMsg(message);
}

function* getNotifications() {
  while (true) {
    const {payload, responseCallback} = yield take(GET_NOTIFICATIONS.REQUEST);
    try {
      const response = yield call(
        callRequest,
        GET_NOTIFICATIONS_URL,
        payload,
        '',
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
        yield put(getNotificationSuccess(response, payload));
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}

function* readNotification() {
  while (true) {
    const {payload, responseCallback} = yield take(READ_NOTIFICATION.REQUEST);
    try {
      const response = yield call(
        callRequest,
        READ_NOTIFICATION_URL,
        payload,
        '',
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
        yield put(readNotificationSuccess(response, payload));
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}

function* getImprintById() {
  while (true) {
    const {payload, responseCallback} = yield take(GET_IMPRINT_BY_ID.REQUEST);
    const parameters = `${APIConstants.GET_IMPRINT}${payload.id}`;
    try {
      const response = yield call(
        callRequest,
        GET_IMPRINT_BY_ID_URL,
        {},
        parameters,
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      navigateToEmptyScreen(Util.getErrorText(err.message));
      //  Alert.alert(Util.getErrorText(err.message));
    }
  }
}

function* getPubSubToken() {
  while (true) {
    const {responseCallback} = yield take(GET_PUB_SUB_TOKEN.REQUEST);
    try {
      const response = yield call(
        callRequest,
        GET_PUB_SUB_TOKEN_URL,
        {},
        '',
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}

function* updateVerification() {
  while (true) {
    const {payload, responseCallback} = yield take(UPDATE_VERIFICATION.REQUEST);
    const parameters = `${APIConstants.GET_IMPRINT}${payload.id}/verification-request?verify=${payload.verify}`;
    try {
      const response = yield call(
        callRequest,
        UPDATE_VERIFICATION_URL,
        {},
        parameters,
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}
function* getTransparencyData() {
  while (true) {
    const {payload, responseCallback} = yield take(
      GET_TRANSPARENCY_DATA.REQUEST,
    );
    const parameters = `${APIConstants.GET_TRANSPARENCY_DATA}${payload.content_id}&complex_type=${payload.complex_type}`;
    try {
      const response = yield call(
        callRequest,
        GET_TRANSPARENCY_DATA_URL,
        {},
        parameters,
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}

export default function* root() {
  yield fork(getNotifications);
  yield fork(readNotification);
  yield fork(getImprintById);
  yield fork(updateVerification);
  yield fork(getTransparencyData);
  yield fork(getPubSubToken);
}
