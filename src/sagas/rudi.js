import {take, call, fork, put} from 'redux-saga/effects';
import {
  DELETE_RUDI_FILE,
  GET_RUDI_FILE,
  GET_RUDI_MESSAGE,
  SEND_RUDI_MESSAGE,
  UPLOAD_RUDI_FILE,
} from '../actions/ActionTypes';
import {
  callRequest,
  DELETE_RUDI_FILE as DELETE_RUDI_FILE_URL,
  GET_RUDI_MESSAGE as GET_RUDI_MESSAGES_URL,
  GET_RUDI_FILE as GET_RUDI_FILE_URL,
  UPLOAD_RUDI_FILE as UPLOAD_RUDI_FILE_URL,
  REQUEST_TYPE,
  RUDI_API_URL,
} from '../config/WebService';
import ApiSauce from '../services/ApiSauce';
import Util from '../util';
import {showToastMsg} from '../components/Alert';
import {getRudiMessagesSuccess} from '../actions/RudiActions';
import {APIConstants} from '../constants/APIConstants';

function alert(message, type = 'error') {
  showToastMsg(message);
}

function* getRudiMessages() {
  while (true) {
    const {payload, responseCallback} = yield take(GET_RUDI_MESSAGE.REQUEST);
    try {
      const response = yield call(
        callRequest,
        GET_RUDI_MESSAGES_URL,
        payload,
        '',
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
        yield put(getRudiMessagesSuccess(payload, response));
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}

function* getRudiFile() {
  while (true) {
    const {responseCallback} = yield take(GET_RUDI_FILE.REQUEST);
    try {
      const response = yield call(
        callRequest,
        GET_RUDI_FILE_URL,
        {},
        '',
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
        // yield put(getRudiMessagesSuccess(payload, response));
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}

function* sendRudiMessage() {
  while (true) {
    const {payload, responseCallback} = yield take(SEND_RUDI_MESSAGE.REQUEST);
    const SEND_RUDI_MESSAGE_UR = {
      route: RUDI_API_URL,
      access_token_required: true,
      type: REQUEST_TYPE.POST,
    };
    try {
      const response = yield call(
        callRequest,
        SEND_RUDI_MESSAGE_UR,
        payload,
        '',
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
        // yield put(getRudiMessagesSuccess(payload, response));
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}

function* uploadRudiFile() {
  while (true) {
    const {payload, responseCallback} = yield take(UPLOAD_RUDI_FILE.REQUEST);
    try {
      const response = yield call(
        callRequest,
        UPLOAD_RUDI_FILE_URL,
        payload,
        '',
        {'Content-Type': 'multipart/form-data'},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}

function* deleteRudiFile() {
  while (true) {
    const {payload, responseCallback} = yield take(DELETE_RUDI_FILE.REQUEST);
    const parameters = `${APIConstants.DELETE_RUDI_FILE}${payload.id}`;
    try {
      const response = yield call(
        callRequest,
        DELETE_RUDI_FILE_URL,
        payload,
        parameters,
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}

export default function* root() {
  yield fork(getRudiMessages);
  yield fork(getRudiFile);
  yield fork(sendRudiMessage);
  yield fork(uploadRudiFile);
  yield fork(deleteRudiFile);
}
