import {call, put, takeLatest} from 'redux-saga/effects';
import {
  FREE_TEXT_SEARCH_USERS,
  FREE_TEXT_SEARCH_IMPRINTS,
} from '../actions/ActionTypes';
import {
  FREE_TEXT_SEARCH_IMPRINTS as FREE_TEXT_SEARCH_IMPRINTS_URL,
  FREE_TEXT_SEARCH_USERS as FREE_TEXT_SEARCH_USERS_URL,
  callRequest,
} from '../config/WebService';
import ApiSauce from '../services/ApiSauce';
import Util from '../util';
import {showToastMsg} from '../components/Alert';
import {getFreeTextImprintsSuccess} from '../actions/SearchActions';
import {APIConstants} from '../constants/APIConstants';
import _ from 'lodash';

function alert(message, type = 'error') {
  showToastMsg(message);
}

function* getFreeTextSearchUsers(action) {
  const {payload, responseCallback} = action;

  let scores = '';
  if (payload?.scores?.length > 0) {
    scores = '&';
    for (let i = 0; i < payload?.scores?.length; i++) {
      scores += `valueFilter=${payload.scores[i]}`;
      if (i < payload.scores.length - 1) {
        scores += '&';
      }
    }
  }
  let query = payload.query;
  let parameters = `${APIConstants.FREE_TEXT_SEARCH_USERS}`;

  if (!_.isEmpty(query)) {
    parameters += `?query=${query}&page=${payload.page}&limit=10`;
    if (scores) {
      parameters += `${scores}`;
    }
  } else {
    parameters += `?page=${payload.page}&limit=10${scores}`;
  }

  try {
    const response = yield call(
      callRequest,
      FREE_TEXT_SEARCH_USERS_URL,
      {},
      parameters,
      {},
      ApiSauce,
    );
    if (response) {
      if (responseCallback) responseCallback(response, null);
      // yield put(getFreeTextSearchUserSuccess(response));
    } else {
      alert('Something went wrong');
    }
  } catch (err) {
    if (responseCallback) responseCallback(null, err);
    alert(Util.getErrorText(err.message));
  }
}

function* getFreeTextImprints(action) {
  const {payload, responseCallback} = action;

  let scores = '';
  if (payload?.scores?.length > 0) {
    scores = '&';
    for (let i = 0; i < payload?.scores?.length; i++) {
      scores += `valueFilter=${payload.scores[i]}`;
      if (i < payload.scores.length - 1) {
        scores += '&';
      }
    }
  }
  let query = payload.query;
  let parameters = `${APIConstants.FREE_TEXT_SEARCH_IMPRINTS}`;

  if (!_.isEmpty(query)) {
    parameters += `?query=${query}&page=${payload.page}&limit=10`;
    if (scores) {
      parameters += `${scores}`;
    }
  } else {
    parameters += `?page=${payload.page}&limit=5&${scores}`;
  }

  try {
    const response = yield call(
      callRequest,
      FREE_TEXT_SEARCH_IMPRINTS_URL,
      {},
      parameters,
      {},
      ApiSauce,
    );
    if (response) {
      if (responseCallback) responseCallback(response, null);
      yield put(getFreeTextImprintsSuccess(response, payload));
    } else {
      alert('Something went wrong');
    }
  } catch (err) {
    if (responseCallback) responseCallback(null, err);
    alert(Util.getErrorText(err.message));
  }
}

export default function* root() {
  yield takeLatest(FREE_TEXT_SEARCH_USERS.REQUEST, getFreeTextSearchUsers);
  yield takeLatest(FREE_TEXT_SEARCH_IMPRINTS.REQUEST, getFreeTextImprints);
}
