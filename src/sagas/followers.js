import {take, call, fork, put} from 'redux-saga/effects';
import {
  USER_FOLLOWERS,
  GET_FRIEND_REQUEST_SENDER,
  RESPONSE_TO_FOLLOWERS,
  UN_FOLLOW,
  BLOCK_IMPRINT,
  BLOCK_USER,
  <PERSON><PERSON><PERSON><PERSON>_MESSAGE,
} from '../actions/ActionTypes';
import {
  callRequest,
  BLOCK_MESSAGE as BLOCK_MESSAGE_URL,
  BL<PERSON>K_USER as BLOCK_USER_URL,
  BLOCK_IMPRINT as BLOCK_IMPRINT_URL,
  UN_FOLLOW as UN_FOLLOW_URL,
  RESPONSE_TO_FOLLOWERS as RESPONSE_TO_FOLLOWERS_URL,
  GET_FRIEND_REQUEST_SENDER as GET_FRIEND_REQUEST_SENDER_URL,
  USER_FOLLOWERS as USER_FOLLOWERS_URL,
} from '../config/WebService';
import ApiSauce from '../services/ApiSauce';
import Util from '../util';
import {showToastMsg} from '../components/Alert';
import {
  getUserFriendsSuccess,
  getFriendsRequestSenderSuccess,
} from '../actions/FollowersActions';
import {APIConstants} from '../constants/APIConstants';

function alert(message, type = 'error') {
  showToastMsg(message);
}

function* getFriends() {
  while (true) {
    const {responseCallback} = yield take(USER_FOLLOWERS.REQUEST);
    try {
      const response = yield call(
        callRequest,
        USER_FOLLOWERS_URL,
        {},
        '',
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
        yield put(getUserFriendsSuccess(response));
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}

function* blockImprint() {
  while (true) {
    const {payload, responseCallback} = yield take(BLOCK_IMPRINT.REQUEST);
    const parameters = `${APIConstants.BLOCK_IMPRINT}?follower=${payload.follower}&status=${payload.status}`;

    try {
      const response = yield call(
        callRequest,
        BLOCK_IMPRINT_URL,
        {},
        parameters,
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response);
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}

function* blockUser() {
  while (true) {
    const {payload, responseCallback} = yield take(BLOCK_USER.REQUEST);
    const parameters = `${APIConstants.BLOCK_USER}?follower=${payload.follower}&status=${payload.status}`;

    try {
      const response = yield call(
        callRequest,
        BLOCK_USER_URL,
        {},
        parameters,
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response);
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}

function* blockMessage() {
  while (true) {
    const {payload, responseCallback} = yield take(BLOCK_MESSAGE.REQUEST);
    const parameters = `${APIConstants.BLOCK_MESSAGE}?followerId=${payload.follower}&status=${payload.status}`;

    try {
      const response = yield call(
        callRequest,
        BLOCK_MESSAGE_URL,
        {},
        parameters,
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response);
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}

function* getFriendsRequestSender() {
  while (true) {
    const {responseCallback} = yield take(GET_FRIEND_REQUEST_SENDER.REQUEST);
    try {
      const response = yield call(
        callRequest,
        GET_FRIEND_REQUEST_SENDER_URL,
        {},
        '',
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
        yield put(getFriendsRequestSenderSuccess(response));
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}

function* unFollow() {
  while (true) {
    const {payload, responseCallback} = yield take(UN_FOLLOW.REQUEST);
    const parameters = `${APIConstants.UN_FOLLOW}?follower=${payload.follower}`;

    try {
      const response = yield call(
        callRequest,
        UN_FOLLOW_URL,
        {},
        parameters,
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
        //  yield put(userFollowersSuccess(response));
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}

function* respondToFollowers() {
  while (true) {
    const {payload, responseCallback} = yield take(
      RESPONSE_TO_FOLLOWERS.REQUEST,
    );
    try {
      const response = yield call(
        callRequest,
        RESPONSE_TO_FOLLOWERS_URL,
        payload,
        '',
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}

export default function* root() {
  yield fork(getFriends);
  yield fork(getFriendsRequestSender);
  yield fork(respondToFollowers);
  yield fork(unFollow);
  yield fork(blockImprint);
  yield fork(blockUser);
  yield fork(blockMessage);
}
