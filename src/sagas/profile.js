import {take, put, call, fork, takeLatest} from 'redux-saga/effects';
import {
  ADD_EDUCATION,
  ADD_EMPLOYMENT,
  ADD_FAMILY,
  COMPLETE_PROFiLE,
  DELETE_EDUCATION,
  DELETE_FAMILY,
  DELETE_EMPLOYMENT,
  EDIT_IMAGE,
  GET_INSTITUTE,
  GET_OTHER_USER_DETAILS,
  GET_RELATION_ENUMS,
  UPDATED_PROFILE_OVERVIEW,
  UPDATE_CONTACT,
  UPDATE_EDUCATION,
  UPDATE_EMPLOYMENT,
  UPDATE_PROFILE_ABOUT,
  USER_SEARCH,
  MODERATION_PROFILE,
  MODERATION_PROFILE_PHOTO,
  UPDATE_FAMILY,
} from '../actions/ActionTypes';
import {
  callRequest,
  EDIT_IMAGE as EDIT_IMAGE_URL,
  COMPLETE_PROFILE as COMPLETE_PROFILE_URL,
  UPDATE_PROFILE_OVERVIEW as UPDATE_PROFILE_OVERVIEW_URL,
  UPDATE_PROFILE_ABOUT as UPDATE_PROFILE_ABOUT_URL,
  GET_INSTITUTE as GET_INSTITUTE_URL,
  UPDATE_EDUCATION as UPDATE_EDUCATION_URL,
  DELETE_EDUCATION as DELETE_EDUCATION_URL,
  DELETE_FAMILY as DELETE_FAMILY_URL,
  UPDATE_EMPLOYMENT as UPDATE_EMPLOYMENT_URL,
  ADD_EDUCATION as ADD_EDUCATION_URL,
  ADD_EMPLOYMENT as ADD_EMPLOYMENT_URL,
  DELETE_EMPLOYMENT as DELETE_EMPLOYMENT_URL,
  ADD_FAMILY as ADD_FAMILY_URL,
  GET_USERS as GET_USERS_URL,
  GET_RELATION_ENUMS as GET_RELATION_ENUMS_URL,
  GET_OTHER_USER_DETAILS as GET_OTHER_USER_DETAILS_URL,
  UPDATE_CONTACT as UPDATE_CONTACT_URL,
  MODERATION_PROFILE as MODERATION_PROFILE_URL,
  MODERATION_PROFILE_PHOTO as MODERATION_PROFILE_PHOTO_URL,
  UPDATE_FAMILY as UPDATE_FAMILY_URL,
} from '../config/WebService';
import ApiSauce from '../services/ApiSauce';
import Util from '../util';
import {showToastMsg} from '../components/Alert';
import {
  addEducationSuccess,
  addEmploymentSuccess,
  addFamilySuccess,
  completeProfileSuccess,
  deleteEducationSuccess,
  deleteEmploymentSuccess,
  deleteFamilySuccess,
  editImageSuccess,
  getInstituteSuccess,
  getOtherUserDetailsSuccess,
  getRelationEnumSuccess,
  updateContactSuccess,
  updateEducationSuccess,
  updateEmploymentSuccess,
  updateFamilySuccess,
  updateProfileAboutSuccess,
  updateProfileOverviewSuccess,
  userSearchSuccess,
} from '../actions/ProfileActions';
import {APIConstants} from '../constants/APIConstants';

function alert(message, type = 'error') {
  showToastMsg(message);
}

function* editUserImage() {
  while (true) {
    const {payload, responseCallback} = yield take(EDIT_IMAGE.REQUEST);
    try {
      const response = yield call(
        callRequest,
        EDIT_IMAGE_URL,
        payload,
        '',
        {'Content-Type': 'multipart/form-data'},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
        yield put(editImageSuccess(response));
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}

function* getCompleteProfile() {
  while (true) {
    const {responseCallback} = yield take(COMPLETE_PROFiLE.REQUEST);
    try {
      const response = yield call(
        callRequest,
        COMPLETE_PROFILE_URL,
        {},
        '',
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
        yield put(completeProfileSuccess(response));
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}

function* updateProfileOverview() {
  while (true) {
    const {payload, responseCallback} = yield take(
      UPDATED_PROFILE_OVERVIEW.REQUEST,
    );
    try {
      const response = yield call(
        callRequest,
        UPDATE_PROFILE_OVERVIEW_URL,
        payload,
        '',
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
        yield put(updateProfileOverviewSuccess(response));
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}

function* updateProfileAbout() {
  while (true) {
    const {payload, responseCallback} = yield take(
      UPDATE_PROFILE_ABOUT.REQUEST,
    );
    try {
      const response = yield call(
        callRequest,
        UPDATE_PROFILE_ABOUT_URL,
        payload,
        '',
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
        yield put(updateProfileAboutSuccess(response));
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}

function* getInstitue() {
  while (true) {
    const {payload, responseCallback} = yield take(GET_INSTITUTE.REQUEST);
    const parameters = `${APIConstants.GET_INSTITUTE}?display%20name=${payload.name}`;
    try {
      const response = yield call(
        callRequest,
        GET_INSTITUTE_URL,
        {},
        parameters,
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
        yield put(getInstituteSuccess(response));
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}

function* updateEducation() {
  while (true) {
    const {payload, responseCallback} = yield take(UPDATE_EDUCATION.REQUEST);
    try {
      const response = yield call(
        callRequest,
        UPDATE_EDUCATION_URL,
        payload,
        '',
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
        yield put(updateEducationSuccess(response));
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}

function* updateEmployment() {
  while (true) {
    const {payload, responseCallback} = yield take(UPDATE_EMPLOYMENT.REQUEST);
    try {
      const response = yield call(
        callRequest,
        UPDATE_EMPLOYMENT_URL,
        payload,
        '',
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
        yield put(updateEmploymentSuccess(response));
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}

function* addEducation() {
  while (true) {
    const {payload, responseCallback} = yield take(ADD_EDUCATION.REQUEST);
    try {
      const response = yield call(
        callRequest,
        ADD_EDUCATION_URL,
        payload,
        '',
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
        yield put(addEducationSuccess(response));
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}

function* addEployment() {
  while (true) {
    const {payload, responseCallback} = yield take(ADD_EMPLOYMENT.REQUEST);
    try {
      const response = yield call(
        callRequest,
        ADD_EMPLOYMENT_URL,
        payload,
        '',
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
        yield put(addEmploymentSuccess(response));
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}

function* deleteEducation() {
  while (true) {
    const {payload, responseCallback} = yield take(DELETE_EDUCATION.REQUEST);
    const parameters = `${APIConstants.UPDATE_EDUCATION}?id=${payload.id}`;
    try {
      const response = yield call(
        callRequest,
        DELETE_EDUCATION_URL,
        {},
        parameters,
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
        yield put(deleteEducationSuccess(response));
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}

function* deleteEmployment() {
  while (true) {
    const {payload, responseCallback} = yield take(DELETE_EMPLOYMENT.REQUEST);
    const parameters = `${APIConstants.UPDATE_EMPLOYMENT}?id=${payload.id}`;
    try {
      const response = yield call(
        callRequest,
        DELETE_EMPLOYMENT_URL,
        {},
        parameters,
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
        yield put(deleteEmploymentSuccess(response));
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}

function* addFamily() {
  while (true) {
    const {payload, responseCallback} = yield take(ADD_FAMILY.REQUEST);
    try {
      const response = yield call(
        callRequest,
        ADD_FAMILY_URL,
        payload,
        '',
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
        yield put(addFamilySuccess(response));
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}

function* updateFamily() {
  while (true) {
    const {payload, responseCallback} = yield take(UPDATE_FAMILY.REQUEST);
    try {
      const response = yield call(
        callRequest,
        UPDATE_FAMILY_URL,
        payload,
        '',
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
        yield put(updateFamilySuccess(response));
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}

function* deleteFamily() {
  while (true) {
    const {payload, responseCallback} = yield take(DELETE_FAMILY.REQUEST);
    const parameters = `${APIConstants.ADD_RELATION}?id=${payload.id}`;
    try {
      const response = yield call(
        callRequest,
        DELETE_FAMILY_URL,
        {},
        parameters,
        {},
        ApiSauce,
      );

      if (response) {
        if (responseCallback) responseCallback(response, null);
        yield put(deleteFamilySuccess(payload));
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}

function* getUsers() {
  while (true) {
    const {payload, responseCallback} = yield take(USER_SEARCH.REQUEST);
    const parameters = `${APIConstants.GET_USERS}?display%20name=${payload.name}`;
    try {
      const response = yield call(
        callRequest,
        GET_USERS_URL,
        {},
        parameters,
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
        yield put(userSearchSuccess(response));
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}

function* getRelationEnums() {
  while (true) {
    const {responseCallback} = yield take(GET_RELATION_ENUMS.REQUEST);
    try {
      const response = yield call(
        callRequest,
        GET_RELATION_ENUMS_URL,
        {},
        '',
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
        yield put(getRelationEnumSuccess(response));
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}

function* getOtherUserDetails() {
  while (true) {
    const {payload, responseCallback} = yield take(
      GET_OTHER_USER_DETAILS.REQUEST,
    );
    try {
      const response = yield call(
        callRequest,
        GET_OTHER_USER_DETAILS_URL,
        payload,
        '',
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
        yield put(getOtherUserDetailsSuccess(response));
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}

function* updateContact() {
  while (true) {
    const {payload, responseCallback} = yield take(UPDATE_CONTACT.REQUEST);
    try {
      const response = yield call(
        callRequest,
        UPDATE_CONTACT_URL,
        payload,
        '',
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
        yield put(updateContactSuccess(response));
        yield put(updateProfileAboutSuccess(response));
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}

function* moderationProfile() {
  while (true) {
    const {payload, responseCallback} = yield take(MODERATION_PROFILE.REQUEST);
    try {
      const response = yield call(
        callRequest,
        MODERATION_PROFILE_URL,
        payload,
        '',
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
    }
  }
}

function* moderationProfilePhoto() {
  while (true) {
    const {payload, responseCallback} = yield take(
      MODERATION_PROFILE_PHOTO.REQUEST,
    );
    try {
      const response = yield call(
        callRequest,
        MODERATION_PROFILE_PHOTO_URL,
        payload,
        '',
        {'Content-Type': 'multipart/form-data'},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      // alert(Util.getErrorText(err.message));
    }
  }
}

export default function* root() {
  yield fork(editUserImage);
  yield fork(getCompleteProfile);
  yield fork(updateProfileOverview);
  yield fork(updateProfileAbout);
  yield fork(getInstitue);
  yield fork(addEducation);
  yield fork(updateEducation);
  yield fork(deleteEducation);
  yield fork(addEployment);
  yield fork(updateEmployment);
  yield fork(deleteEmployment);
  yield fork(addFamily);
  yield fork(getUsers);
  yield fork(getRelationEnums);
  yield fork(getOtherUserDetails);
  yield fork(updateContact);
  yield fork(moderationProfile);
  yield fork(deleteFamily);
  yield fork(updateFamily);
  yield fork(moderationProfilePhoto);
}
