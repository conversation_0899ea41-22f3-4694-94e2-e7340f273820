import {take, put, call, fork} from 'redux-saga/effects';
import {
  VIOLATION_POLICIES,
  GL<PERSON><PERSON>L_TIMELINE,
  POST_REACTION,
  PERSONAL_TIMELINE,
  RESET_TIMELINE,
  IMPRINT_VERIFICATION_REQUEST,
  IMPRINT_SHOUT_OUT,
  GET_USER_TIMELINE,
  GET_FILTERED_TIMELINE,
  IMPRINT_BOOKMARK,
  REQUEST_FOLLOW,
  GET_NEXT_OF_KIN,
  DELETE_NEXT_OF_KIN,
  ADD_NEXT_OF_KIN,
  REMIND_NEXT_OF_KIN,
  DELETE_IMPRINT,
  USER_TIMELINE,
  REPORT_CONTENT,
  UPDATE_REDFLAG_STATUS,
  REMIND_LATER,
} from '../actions/ActionTypes';
import {
  REMIND_LATER as REMIND_LATER_URL,
  VIOLATION_POLICIES as VIOLATION_POLICIES_URL,
  REPORT_CONTENT as REPORT_CONTENT_URL,
  USER_TIME_LINE as USER_TIME_LINE_URL,
  DELETE_IMPRINT as DELETE_IMPRINT_URL,
  GLOBAL_TIMELINE as GLOBAL_TIMELINE_URL,
  POST_REACTION as POST_REACTION_URL,
  PERSONAL_TIMELINE as PERSONAL_TIMELINE_URL,
  IMPRINT_VERIFICATION_REQUEST as IMPRINT_VERIFICATION_REQUEST_URL,
  POST_SHOUT_OUT as POST_SHOUT_OUT_URL,
  GET_USER_TIMELINE as GET_USER_TIMELINE_URL,
  GET_FILTERED_TIMELINE as GET_FILTERED_TIMELINE_URL,
  IMPRINT_BOOKMARK as IMPRINT_BOOKMARK_URL,
  FOLLOW_REQUEST as FOLLOW_REQUEST_URL,
  GET_NEXT_OF_KIN as GET_NEXT_OF_KIN_URL,
  DELETE_NEXT_OF_KIN as DELETE_NEXT_OF_KIN_URL,
  ADD_NEXT_OF_KIN as ADD_NEXT_OF_KIN_URL,
  REMIND_NEXT_OF_KIN as REMIND_NEXT_OF_KIN_URL,
  callRequest,
} from '../config/WebService';
import ApiSauce from '../services/ApiSauce';
import Util from '../util';
import {showToastMsg} from '../components/Alert';
import {
  resetTimeLine,
  globalTimeLineSuccess,
  personalTimeLineSuccess,
  postReactionSuccess,
  imprintVerificationSuccess,
  imprintShoutoutSuccess,
  getUserTimeLineSuccess,
  getFilteredTimeLineSuccess,
  imprintBookmarkSuccess,
  requestFollowSuccess,
  deleteImprintSuccess,
} from '../actions/TimelineActions';
import {APIConstants} from '../constants/APIConstants';

function alert(message, type = 'error') {
  showToastMsg(message);
}

function* getGlobalTimeline() {
  while (true) {
    const {payload, responseCallback} = yield take(GLOBAL_TIMELINE.REQUEST);
    let scores = '';
    if (payload?.scores?.length > 0) {
      scores = '?';
      for (let i = 0; i < payload?.scores?.length; i++) {
        scores += `filters=${payload.scores[i]}`;
        if (i < payload.scores.length - 1) {
          scores += '&';
        }
      }
    }
    const parameters = `${APIConstants.GLOBAL_TIMELINE}${payload.page}${scores}`;
    try {
      const response = yield call(
        callRequest,
        GLOBAL_TIMELINE_URL,
        {},
        parameters,
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
        yield put(globalTimeLineSuccess(response, payload));
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}

function* getPersonalTimeline() {
  while (true) {
    const {payload, responseCallback} = yield take(PERSONAL_TIMELINE.REQUEST);
    let scores = '';
    if (payload?.scores?.length > 0) {
      scores = '?';
      for (let i = 0; i < payload?.scores?.length; i++) {
        scores += `filters=${payload.scores[i]}`;
        if (i < payload.scores.length - 1) {
          scores += '&';
        }
      }
    }
    const parameters = `${APIConstants.PERSONAL_TIMELINE}${payload.page}${scores}`;
    try {
      const response = yield call(
        callRequest,
        PERSONAL_TIMELINE_URL,
        {},
        parameters,
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
        yield put(personalTimeLineSuccess(response, payload));
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}

function* getOwnTimeLine() {
  while (true) {
    const {payload, responseCallback} = yield take(USER_TIMELINE.REQUEST);
    let scores = '';
    if (payload?.scores?.length > 0) {
      scores = '?';
      for (let i = 0; i < payload?.scores?.length; i++) {
        scores += `filters=${payload.scores[i]}`;
        if (i < payload.scores.length - 1) {
          scores += '&';
        }
      }
    }
    const parameters = `${APIConstants.USER_TIMELINE}${payload.page}${scores}`;
    try {
      const response = yield call(
        callRequest,
        USER_TIME_LINE_URL,
        {},
        parameters,
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
        yield put(personalTimeLineSuccess(response, payload));
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}

function* getUserTimeline() {
  while (true) {
    const {payload, responseCallback} = yield take(GET_USER_TIMELINE.REQUEST);
    const parameters = `${APIConstants.USER_TIME_LINE}/${encodeURIComponent(
      payload.userName,
    )}/${payload.page}`;
    try {
      const response = yield call(
        callRequest,
        GET_USER_TIMELINE_URL,
        {},
        parameters,
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
        yield put(getUserTimeLineSuccess(response, payload));
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      yield put(getUserTimeLineSuccess([], payload));

      // alert(Util.getErrorText(err.message));
    }
  }
}

function* getFilteredTimeline() {
  while (true) {
    const {payload, responseCallback} = yield take(
      GET_FILTERED_TIMELINE.REQUEST,
    );
    const parameters = `${APIConstants.FILTERED_TIMELINE}/${payload.name}/${payload.page}`;
    try {
      const response = yield call(
        callRequest,
        GET_FILTERED_TIMELINE_URL,
        {},
        parameters,
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
        yield put(getFilteredTimeLineSuccess(response, payload));
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}

function* postReaction() {
  while (true) {
    const {payload, responseCallback} = yield take(POST_REACTION.REQUEST);
    const isGlobal = payload.isGlobal;
    delete payload.isGlobal;

    try {
      const response = yield call(
        callRequest,
        POST_REACTION_URL,
        payload,
        '',
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
        yield put(postReactionSuccess(response, isGlobal));
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}

function* followRequest() {
  while (true) {
    const {payload, responseCallback} = yield take(REQUEST_FOLLOW.REQUEST);
    const isGlobal = payload.isGlobal;
    delete payload.isGlobal;
    try {
      const response = yield call(
        callRequest,
        FOLLOW_REQUEST_URL,
        payload,
        '',
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
        yield put(requestFollowSuccess(response, isGlobal));
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}

function* resetTimeLineSaga() {
  while (true) {
    const {payload, responseCallback} = yield take(RESET_TIMELINE.REQUEST);
    try {
      yield put(resetTimeLine());
    } catch (err) {
      alert(Util.getErrorText(err.message));
    }
  }
}

function* imprintVerification() {
  while (true) {
    const {payload, responseCallback} = yield take(
      IMPRINT_VERIFICATION_REQUEST.REQUEST,
    );
    const parameters = `${APIConstants.IMPRINT_VERIFICATION_REQUEST_ONE}${payload.id}${APIConstants.IMPRINT_VERIFICATION_REQUEST}`;
    try {
      const response = yield call(
        callRequest,
        IMPRINT_VERIFICATION_REQUEST_URL,
        {},
        parameters,
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
        yield put(imprintVerificationSuccess(response, payload.isGlobal));
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}

function* postShoutOut() {
  while (true) {
    const {payload, responseCallback} = yield take(IMPRINT_SHOUT_OUT.REQUEST);
    const parameters = `${APIConstants.IMPRINT_VERIFICATION_REQUEST_ONE}${payload.imprintId}?global=true`;
    try {
      const response = yield call(
        callRequest,
        POST_SHOUT_OUT_URL,
        {},
        parameters,
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
        yield put(imprintShoutoutSuccess(response, payload));
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}

function* postBookmark() {
  while (true) {
    const {payload, responseCallback} = yield take(IMPRINT_BOOKMARK.REQUEST);
    const parameters = `${APIConstants.IMPRINT_BOOKMARK}/${payload.imprintId}?isBookmark=${payload.isBookmark}`;
    const isGlobal = payload.isGlobal;
    delete payload.isGlobal;
    delete payload.imprintId;
    try {
      const response = yield call(
        callRequest,
        IMPRINT_BOOKMARK_URL,
        {},
        parameters,
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
        yield put(imprintBookmarkSuccess(response, isGlobal));
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}

function* getNextOfKin() {
  while (true) {
    const {responseCallback} = yield take(GET_NEXT_OF_KIN.REQUEST);
    try {
      const response = yield call(
        callRequest,
        GET_NEXT_OF_KIN_URL,
        {},
        '',
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}

function* deleteNextOfKin() {
  while (true) {
    const {payload, responseCallback} = yield take(DELETE_NEXT_OF_KIN.REQUEST);
    const parameters = `${APIConstants.NEXT_OF_KIN}?id=${payload.id}`;
    try {
      const response = yield call(
        callRequest,
        DELETE_NEXT_OF_KIN_URL,
        {},
        parameters,
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}

function* deleteImprint() {
  while (true) {
    const {payload, responseCallback} = yield take(DELETE_IMPRINT.REQUEST);
    const parameters = `${APIConstants.DELETE_IMPRINT}${payload.imprintId}`;
    try {
      const response = yield call(
        callRequest,
        DELETE_IMPRINT_URL,
        {},
        parameters,
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response);
        yield put(deleteImprintSuccess(response));
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}

function* addNextOfKin() {
  while (true) {
    const {payload, responseCallback} = yield take(ADD_NEXT_OF_KIN.REQUEST);
    try {
      const response = yield call(
        callRequest,
        ADD_NEXT_OF_KIN_URL,
        payload,
        '',
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}
function* remindNextOfKin() {
  while (true) {
    const {payload, responseCallback} = yield take(REMIND_NEXT_OF_KIN.REQUEST);
    const parameters = payload.id
      ? `${APIConstants.REMIND_NEXT_OF_KIN}/${payload.id}`
      : APIConstants.REMIND_NEXT_OF_KIN;

    try {
      const response = yield call(
        callRequest,
        REMIND_NEXT_OF_KIN_URL,
        {},
        parameters,
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}

function* reportContent() {
  while (true) {
    const {payload, responseCallback} = yield take(REPORT_CONTENT.REQUEST);
    const {imprintId, userId, isGlobal, ...filteredPayload} = payload;

    try {
      const response = yield call(
        callRequest,
        REPORT_CONTENT_URL,
        filteredPayload,
        '',
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
        yield put({
          type: UPDATE_REDFLAG_STATUS,
          payload: {
            imprintId: payload.imprintId,
            userId: payload.userId,
            isRedFlagged: true,
            isGlobal: payload.isGlobal,
            id: payload.id,
          },
          response: {
            id: response.contentId,
          },
        });
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}
function* violationPolices() {
  while (true) {
    const {payload, responseCallback} = yield take(VIOLATION_POLICIES.REQUEST);
    const parameters = `${APIConstants.VIOLATION_POLICIES}${payload.type}`;

    try {
      const response = yield call(
        callRequest,
        VIOLATION_POLICIES_URL,
        {},
        parameters,
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}

function* remindLater() {
  while (true) {
    const {responseCallback} = yield take(REMIND_LATER.REQUEST);

    try {
      const response = yield call(
        callRequest,
        REMIND_LATER_URL,
        {},
        '',
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}

function* getSubscriptionPackageFeatures() {
  while (true) {
    const {responseCallback} = yield take(
      GET_SUBSCRIPTION_PACKAGE_FEATURES.REQUEST,
    );
    try {
      const response = yield call(
        callRequest,
        GET_SUSBSCRIPTION_PACKAGE_FEATURES_URL,
        {},
        '',
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}

export default function* root() {
  yield fork(getGlobalTimeline);
  yield fork(getPersonalTimeline);
  yield fork(postReaction);
  yield fork(resetTimeLineSaga);
  yield fork(imprintVerification);
  yield fork(postShoutOut);
  yield fork(getUserTimeline);
  yield fork(getFilteredTimeline);
  yield fork(postBookmark);
  yield fork(followRequest);
  yield fork(getNextOfKin);
  yield fork(deleteNextOfKin);
  yield fork(addNextOfKin);
  yield fork(remindNextOfKin);
  yield fork(deleteImprint);
  yield fork(getOwnTimeLine);
  yield fork(reportContent);
  yield fork(violationPolices);
  yield fork(remindLater);
}
