/**
 * This file contains all the API constants
 */

export const APIConstants = {
  LOGIN: '/login',
  SEND_OTP: '/api/v2/auth/otp',
  VALIDATE_OTP: '/api/v2/auth/otp/verify',
  RESET_PASSWORD: '/api/v2/auth/otp/reset-password',
  USER_SIGNUP: '/api/v2/auth/signup',
  USER_SIGN_IN: '/api/v2/auth/login',
  USER_FRIENDS_SUGGESTION: '/api/v2/user/suggestion/list',
  FRIENDS_SUGGESTIONS: 'api/v2/user/friend-suggestions',
  FOLLOW_FRIENDS: '/api/v2/user/social/batch/follow-request',
  VERIFY_USERNAME: '/api/v2/user/verify-username',
  SETUP_PROFILE: '/api/v2/user/onboarding',
  GOOGLE_SIGN_IN: '/api/v2/auth/google-login',
  USER_INFO: '/api/v2/user/info',
  GLOBAL_TIMELINE: 'api/v2/timeline/global-tribe/',
  PERSONAL_TIMELINE: 'api/v2/timeline/personal-tribe/',
  USER_TIMELINE: 'api/v2/timeline/user-profile/',
  POST_REACTION: '/api/v2/timeline/user-interactions',
  IMPRINT_VERIFICATION_REQUEST: '/verification-request',
  IMPRINT_VERIFICATION_REQUEST_ONE: 'api/v2/imprint/',
  USER_POST_IMPRINT: '/api/v2/imprint',
  SEARCH_USER: 'api/v2/user/search',
  USER_FOLLOWERS: 'api/v2/user/followers',
  GET_FRIEND_REQUEST_SENDER: '/api/v2/user/social/follow-request',
  RESPONSE_TO_FOLLOWERS: '/api/v2/user/social/follow-request',
  UN_FOLLOW: 'api/v2/user/unfollow',
  BLOCK_IMPRINT: 'api/v2/user/follower/block-imprints',
  BLOCK_USER: 'api/v2/user/follower/block-user',
  BLOCK_USERS_LISTING: 'api/v2/user/blocked-users',
  BLOCK_MESSAGE: 'api/v2/user/follower/block-messages',
  EDIT_IMAGE: 'api/v2/user/avatar',
  COMPLETE_PROFILE: 'api/v2/user/profile/complete',
  UPDATE_OVERVIEW: 'api/v2/user/overview',
  UPDATE_ABOUT: 'api/v2/user/about',
  GET_INSTITUTE: 'api/v2/user/institutes',
  UPDATE_EDUCATION: 'api/v2/user/education',
  UPDATE_EMPLOYMENT: 'api/v2/user/employment',
  ADD_RELATION: 'api/v2/user/relationship',
  GET_USERS: 'api/v2/user/search',
  RELATIONSHIP_ENUM: 'api/v2/user/relationship-enums',
  MEDIA_CHECK_IN_VIEW: 'api/v2/imprint/my-checkin-or-media?checkin=',
  USER_TIME_LINE: 'api/v2/timeline/user-timeline',
  OTHER_USER_DETAILS: 'api/v2/user/profile/username',
  FILTERED_TIMELINE: 'api/v2/timeline',
  IMPRINT_BOOKMARK: 'api/v2/timeline/bookmark',
  RECENT_CHAT_LIST: 'api/v2/chat/get-conversations',
  CREATE_CHAT_GROUP: '/api/v2/chat/conversation',
  GET_CONVERSATION_MESSAGES: 'api/v2/chat/get-conversation-messages',
  UPDATE_READ_STATUS: 'api/v2/chat/read-status',
  VALUE_ANALYTICS: 'api/v2/user/value-analytics',
  VALUE_PORTFOLIO: 'api/v2/user/value-portfolio',
  TIME_OPTION: 'api/v2/user/time-filter-options',
  CONTACT: 'api/v2/user/contact',
  NOTIFICATION: 'api/v2/notification/get-notifications',
  READ_NOTIFICATION: 'api/v2/notification/read-notification',
  GET_IMPRINT: 'api/v2/imprint/',
  GET_PUB_SUB_TOKEN: '/api/v2/auth/pub-sub/token',
  GET_CHAT_TOKEN: '/api/v2/auth/chat/token',
  LEAVE_CONVERSATION: 'api/v2/chat/leave-conversation',
  RUDI_MESSAGE: 'api/v2/rudimiddleware/get-messages',
  RUDI_FILE: 'api/v2/rudimiddleware/file',
  DELETE_RUDI_FILE: 'api/v2/rudimiddleware/file?id=',
  TAG_MEDIA: 'api/v2/imprint/tagged-media',
  RESPOND_TO_TAG: 'api/v2/imprint/imprint-tag',
  NEXT_OF_KIN: 'api/v2/user/next-of-kin',
  SUBSCRIPTION: 'api/v2/subscription/plans',
  SUBSCRIPTION_PACKAGE_FEATURES: 'api/v2/subscription/feature-access',
  REMIND_NEXT_OF_KIN: 'api/v2/user/remind/next-of-kin',
  FLAG_MESSAGE: '/api/v2/chat/flag-message',
  FLAG_CONVERSATION: '/api/v2/chat/flag-conversation',
  PROFILE_QUIZ: 'api/v2/onboarding-quiz?',
  SUBMIT_PROFILE_QUIZ: '/api/v2/onboarding-quiz',
  DELETE_IMPRINT: 'api/v2/imprint/delete-imprint/',
  DELETE_CHAT: 'api/v2/chat/conversations/',
  DELETE_MESSAGE: 'api/v2/chat/delete-message?',
  MODERATION_PROFILE: 'api/v2/moderation/profile',
  MODERATION_PROFILE_PHOTO: 'api/v2/moderation/profile-photo',
  GET_PLANS: '/api/v2/subscription/get-current-plan',
  GET_PRO_PLANS: 'api/v2/subscription/get-pro-plans',
  PURCHASE_SUBSCRIPTIONS: 'api/v2/subscription/checkout-portal',
  BILLING_PORTAL: 'api/v2/subscription/billing-portal',
  VALID_COUPON: 'api/v2/subscription/validate-coupon',
  VALIDATE_RECEIPT: 'api/v2/subscription/validate-receipt',
  // ✅ Cooling-off period APIs
  CANCEL_COOLING_OFF: 'api/v2/subscription/cancel-cooling-off',
  CHECK_COOLING_OFF_STATUS: 'api/v2/subscription/cooling-off-status',
  GET_ENTERTAINMENTS_CONTENT: 'api/v2/entertainment-content?',
  SHARE_IMPRINT: 'api/v2/imprint/share',
  QUIZ_PREVIEW_LIST: '/api/v2/quiz/preview-list',
  ENTERTAINMENT_QUIZ: 'api/v2/quiz?',
  ENTERTAINMENT_QUIZ_ATTEMPT: '/api/v2/quiz/attempt',
  GET_NEWS: 'api/v2/entertainment-content/get-news',
  FREE_TEXT_SEARCH_USERS: 'api/v2/free-text-search/users',
  FREE_TEXT_SEARCH_IMPRINTS: 'api/v2/free-text-search/imprints',
  VIOLATION_POLICIES: 'api/v2/moderation/policies?type=',
  REPORT_CONTENT: 'api/v2/moderation/reports/analyze',
  GET_TRANSPARENCY_DATA: 'api/v2/moderation/my-case?content_id=',
  APPEAL_DECISION: 'api/v2/moderation/my-case',
  REMIND_LATER: 'api/v2/user/nok/remind-me-later',
  GET_ZEN_DESK_TOKEN: 'api/v2/auth/zendesk/jwt',
  VALIDATE_INVITE_CODE: 'api/v2/code/validate',
  APPLE_SIGN_IN: 'api/v2/auth/apple-login',
  ACCEPT_TERMS_CONDITIONS:
    'api/v2/subscription/accept-terms?termsAndConditions=true&EULA=true',
  START_YOTI_SESSION: 'api/v2/yoti/start',
  UPDATE_DOB: '/api/v2/user/update-dob',
  GET_USER_PORTFOLIO: 'api/v2/user/value-profile',
  GET_HASHTAGS: 'api/v2/charity/hashtags?q=',
};
