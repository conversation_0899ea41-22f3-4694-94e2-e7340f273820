/*
 * contains all the route constants
 */

const Routes = {
  TABS: 'Tabs',
  HOME_TABS: 'HomeTabs',

  //Auth Routes
  //#region
  SPLASH: 'splash',
  LOGIN: 'login',
  OTP_SCREEN: 'OtpScreen',
  RESET_EMAIL_SCREEN: 'ResetEmailScreen',
  RESET_PASSWORD_SCREEN: 'ResetPasswordScreen',

  ONBOARDING: 'onboarding',
  PROFILE_SETUP: 'profilesetup',
  //#endregion
  SIGNUP: 'signup',
  SUGGESTED_FRIENDS: 'suggestedFriends',

  //#region tab
  ANALYTICS: 'analytics',
  FRIEND: 'friend',
  HOME: 'home',
  NOTIFICATION: 'notification',
  PROFILE: 'profile',
  CREATE_IMPRINT: 'createImprint',
  CREATE_IMPRINT_PROFILE: 'createImprintProfile',
  SUBSCRIPTION: 'subscription',
  TIME_LINE_MEDIA_VIEW: 'timeLineMediaView',
  VIEW_MEDIA: 'viewMedia',
  CHECK_IN: 'checkin',
  SEARCH: 'search',
  ALL_FRIENDS: 'AllFreinds',

  USER_PROFILE: 'userprofile',
  MULTIPLE_MEDIA: 'multipleMedia',

  //#endregion
  //#region profile
  TYPE_ONE_EDIT: 'TypeOneEdit',
  TYPE_TWO_EDIT: 'TypeTwoEdit',
  //#region

  AUTH_STACK: 'authStack',
  HOME_STACK: 'homeStack',
  PROFILE_STACK: 'profileStack',
  NOTIFICATION_STACK: 'notificationStack',
  CREATE_IMPRINT_STACK: 'createImprintStack',

  LOGOUT: 'auth',
  CHAT_LIST: 'chatList',
  CREATE_CHAT: 'CREATE_CHAT',
  CHAT: 'Chat',
  RUDI_MESSAGE: 'Rudi_Message',

  QUIZ_FLOW: 'QuizFlow',
  QUIZ_PROFILE: 'QuizProfile',
  REPORT_SCREEN: 'ReportScreen',
  PAYMENT_SCREEN: 'PaymentScreen',
  ENTERTAINMENT_SCREEN: 'EntertainmentScreen',
  ENTERTAINMENT_QUIZ: 'EntertainmentQuiz',
  NEWS_SCREEN: 'NewsScreen',
};

export default Routes;
