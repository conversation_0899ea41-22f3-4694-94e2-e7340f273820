import {CompleteProfile, IAboutCardHeader, IAboutTitleSubtitle} from '../types';
import {CONTACT, FAMILY, OVERVIEW} from '../constants/AssetSVGConstants';
import util from '../util';
import {
  ABOUT,
  IMPRINT_SCORES,
  IMPRINT_SCORES_DISPLAY,
} from '../constants/StringConstants';
import {Colors} from '../theme';
import {DATE_FORMAT6} from '../constants';

const generateAboutData = (profile: CompleteProfile) => {
  const aboutOptions: IAboutCardHeader[] = [
    {
      leftImage: <OVERVIEW />,
      name: ABOUT.OVERVIEW,
      details: [
        {title: 'Name', subtitle: profile.overview.displayName},
        {title: 'Username', subtitle: profile.overview.userName},
        {
          title: 'What I Stand for',
          subtitle:
            profile.overview.what_i_stand_for === null
              ? ''
              : profile.overview.what_i_stand_for,
        },
        {
          title: 'What I Value Most',
          subtitle: profile.overview.what_i_value,
        },
      ],
    },
    // {
    //   leftImage: <PERSONAL_INFO />,
    //   name: ABOUT.PERSONAL_INFO,
    //   details: [{title: 'About You', subtitle: profile.about}],
    //  },
    // {
    //   leftImage: <EDUCATION />,
    //   name: ABOUT.EDUCATION,
    //   details: profile.education.map(educationItem => ({
    //     name: educationItem.institute.name,
    //     logo: educationItem.institute.logo,
    //     date: `${util.getFormattedDateTime(
    //       new Date(educationItem.startDate),
    //       DATE_FORMAT6,
    //     )} to ${util.getFormattedDateTime(
    //       new Date(educationItem.endDate),
    //       DATE_FORMAT6,
    //     )}`,
    //   })),
    // },
    // {
    //   leftImage: <JOB />,
    //   name: ABOUT.JOB,
    //   details: profile.employment.map(employmentItem => ({
    //     name: employmentItem.company.name,
    //     logo: employmentItem.company.logo,
    //     date: `${util.getFormattedDateTime(
    //       new Date(employmentItem.startDate),
    //       DATE_FORMAT6,
    //     )} to ${util.getFormattedDateTime(
    //       new Date(employmentItem.endDate),
    //       DATE_FORMAT6,
    //     )}`,
    //   })),
    // },
    {
      leftImage: <CONTACT />,
      name: ABOUT.CONTACT,
      details: [
        {
          title: 'Date of Birth',
          subtitle: profile.contact.dob,
        },
        {
          title: 'Gender',
          subtitle:
            profile.contact.gender === null ? '' : profile.contact.gender,
        },
        {
          title: 'About You',
          subtitle: profile.about == null ? '' : profile.about,
        },
        // {
        //   title: 'Phone',
        //   subtitle: profile.contact.countryCode + '/' + profile.contact.phone,
        // },
        // {title: 'Email', subtitle: profile.contact.email},
      ],
    },
    {
      leftImage: <FAMILY />,
      name: ABOUT.FAMILY,
      details: profile.family.map(fam => ({
        name: fam.displayName,
        logo: fam.avatarUrl,
        date: `${fam.relationshipName} since ${util.getFormattedDateTime(
          new Date(fam.relationshipSince),
          DATE_FORMAT6,
        )}`,
      })),
    },
  ];

  return aboutOptions;
};

const options = [
  {
    label: IMPRINT_SCORES_DISPLAY.LIFE,
    value: IMPRINT_SCORES.LIFE,
    selectedColor: Colors.symbolSelectedColors.life,
  },
  {
    label: IMPRINT_SCORES_DISPLAY.SAFETY,
    value: IMPRINT_SCORES.SAFETY,
    selectedColor: Colors.symbolSelectedColors.safety,
  },
  {
    label: IMPRINT_SCORES_DISPLAY.LOVE,
    value: IMPRINT_SCORES.LOVE,
    selectedColor: Colors.symbolSelectedColors.love,
  },
  {
    label: IMPRINT_SCORES_DISPLAY.LAUGHTER,
    value: IMPRINT_SCORES.LAUGHTER,
    selectedColor: Colors.symbolSelectedColors.laughter,
  },
  {
    label: IMPRINT_SCORES_DISPLAY.RESPECT,
    value: IMPRINT_SCORES.RESPECT,
    selectedColor: Colors.symbolSelectedColors.respect,
  },
  {
    label: IMPRINT_SCORES_DISPLAY.PURPOSE,
    value: IMPRINT_SCORES.PURPOSE,
    selectedColor: Colors.symbolSelectedColors.purpose,
  },
];

const generateAboutDataForOtherUsers = (profile: any) => {
  const OtherProfile = profile?.profile;
  const aboutOptions: IAboutCardHeader[] = [
    {
      leftImage: <OVERVIEW />,
      name: ABOUT.OVERVIEW,
      details: [
        {title: 'Name', subtitle: profile.displayName},
        {title: 'Username', subtitle: profile.userName},
        {
          title: 'What I Stand for',
          subtitle: profile.profile.what_i_stand_for,
        },
        {
          title: 'What I Value Most ',
          subtitle: profile.profile.what_i_value,
        },
      ],
    },
    {
      leftImage: <CONTACT />,
      name: ABOUT.CONTACT,
      details: [
        {
          title: 'Date of Birth',
          subtitle: OtherProfile?.dob || null,
        },
        {
          title: 'Gender',
          subtitle: OtherProfile?.gender || '',
        },
        {
          title: 'About You',
          subtitle: OtherProfile?.about === 'null' ? '' : OtherProfile?.about,
        },
        // {
        //   title: 'Phone',
        //   subtitle: profile.contact.countryCode + '/' + profile.contact.phone,
        // },
        // {title: 'Email', subtitle: profile.contact.email},
      ],
    },
    {
      leftImage: <FAMILY />,
      name: ABOUT.FAMILY,
      details: profile?.family?.map?.(fam => ({
        name: fam.displayName,
        logo: fam.avatarUrl,
        date: `${fam.relationshipName} since ${util.getFormattedDateTime(
          new Date(fam.relationshipSince),
          DATE_FORMAT6,
        )}`,
      })),
    },
    // {
    //   leftImage: <PERSONAL_INFO />,
    //   name: ABOUT.PERSONAL_INFO,
    //   details: [{title: 'About You', subtitle: profile.profile.about}],
    // },
  ];

  return aboutOptions;
};

const constructEducationData = (
  completeProfile: CompleteProfile,
  data: any,
  editName: string,
) => {
  const payLoad: IAboutTitleSubtitle[] = [];
  if (data.name === ABOUT.EDUCATION) {
    if (editName) {
      completeProfile.education.forEach(education => {
        if (education.institute.name === editName) {
          const objectId = {
            title: 'ID',
            subtitle: education.id,
          };
          const object = {
            title: 'Institute',
            subtitle: education.institute.name,
          };
          const object1 = {
            title: 'Starting Date',
            subtitle: education.startDate,
          };
          const object2 = {
            title: 'Ending Date',
            subtitle: education.endDate,
          };
          const object3 = {title: 'Degree', subtitle: education.degree};
          payLoad.push(objectId, object, object1, object2, object3);
        }
      });
    } else {
      const objectId = {
        title: 'ID',
        subtitle: '',
      };
      const object = {
        title: 'Institute',
        subtitle: '',
      };
      const object1 = {
        title: 'Starting Date',
        subtitle: '',
      };
      const object2 = {
        title: 'Ending Date',
        subtitle: '',
      };
      const object3 = {title: 'Degree', subtitle: ''};
      payLoad.push(objectId, object, object1, object2, object3);
    }
  } else if (data.name === ABOUT.JOB) {
    if (editName) {
      completeProfile.employment.forEach(job => {
        if (job.company.name === editName) {
          const objectId = {
            title: 'ID',
            subtitle: job.id,
          };
          const object = {title: 'Company', subtitle: job.company.name};
          const object1 = {
            title: 'Position',
            subtitle: job.position,
          };
          const object2 = {
            title: 'Starting Date',
            subtitle: job.startDate,
          };
          const object3 = {
            title: 'Ending Date',
            subtitle: job.endDate,
          };
          payLoad.push(objectId, object, object1, object2, object3);
        }
      });
    } else {
      const objectId = {
        title: 'ID',
        subtitle: '',
      };
      const object = {title: 'Company', subtitle: ''};
      const object1 = {
        title: 'Position',
        subtitle: '',
      };
      const object2 = {
        title: 'Starting Date',
        subtitle: '',
      };
      const object3 = {
        title: 'Ending Date',
        subtitle: '',
      };
      payLoad.push(objectId, object, object1, object2, object3);
    }
  } else if (data.name === ABOUT.FAMILY) {
    if (editName) {
      completeProfile.family?.forEach(fam => {
        if (
          fam.userName?.toLowerCase() === editName.toLowerCase() ||
          fam.displayName?.toLowerCase() === editName.toLowerCase()
        ) {
          const objectId = {title: 'ID', subtitle: fam.id};
          const object = {title: 'Relation', subtitle: fam.relationshipName};
          const object1 = {title: 'Name', subtitle: fam.displayName};
          const object2 = {
            title: 'Relation Since',
            subtitle: fam.relationshipSince,
          };
          payLoad.push(objectId, object, object1, object2);
        }
      });
    } else {
      const object = {title: 'Name', subtitle: ''};
      const object1 = {title: 'Relation', subtitle: ''};
      const object2 = {title: 'Relation Since', subtitle: ''};
      payLoad.push(object, object1, object2);
    }
  }
  return payLoad;
};

export {
  generateAboutData,
  constructEducationData,
  generateAboutDataForOtherUsers,
  options,
};
