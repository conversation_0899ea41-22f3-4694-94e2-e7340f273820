import Routes from '../constants/RouteConstants';
import {ONBOARDING} from '../constants/StringConstants';
import {navigationRef} from '../services/RootNavigation';
//import {userSignOutRequest} from '../actions/UserActions';
import AsyncStorage from '@react-native-async-storage/async-storage';

const logoutUserHelper = () => {
  //  DataHandler.getStore().dispatch(userSignOutRequest());
  // NavigationPreloadManager.
  setTimeout(() => {
    /// Util.topAlertError(SESSION_EXPIRED_ERROR);
  }, 1000);
};
const navigateToEmptyScreen = (message: undefined) => {
  //  DataHandler.getStore().dispatch(userSignOutRequest());
  // NavigationPreloadManager.
  setTimeout(() => {
    navigationRef.current.navigate(Routes.TIME_LINE_MEDIA_VIEW, {
      item: {description: message, id: null, imprintMedias: [], user: {id: ''}},
    });
    // Util.topAlertError(SESSION_EXPIRED_ERROR);
  }, 1000);
};

const setOnBoardingStatus = (status: boolean) => {
  AsyncStorage.setItem(ONBOARDING.ONBOARDING_STATUS, String(status)) // Convert boolean to string
    .then(() => {})
    .catch(error => {
      console.error('Error storing data:', error);
    });
};

const getOnBoardingStatus = async () => {
  try {
    const value = await AsyncStorage.getItem(ONBOARDING.ONBOARDING_STATUS);
    if (value !== null) {
      return value;
    }
  } catch (error) {
    console.error('Error retrieving data:', error);
  }
};

export {
  logoutUserHelper,
  setOnBoardingStatus,
  getOnBoardingStatus,
  navigateToEmptyScreen,
};
