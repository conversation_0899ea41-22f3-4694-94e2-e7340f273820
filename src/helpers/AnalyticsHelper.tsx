import {Colors} from '../theme';

const getColor = (value: string) => {
  const colorMap: {[key: string]: string} = {
    life: Colors.symbolColors.life,
    safety: Colors.symbolColors.safety,
    love: Colors.symbolColors.love,
    laughter: Colors.symbolColors.laughter,
    respect: Colors.symbolColors.respect,
    purpose: Colors.symbolColors.purpose,
  };
  return colorMap[value] || '#009E65';
};

export {getColor};
