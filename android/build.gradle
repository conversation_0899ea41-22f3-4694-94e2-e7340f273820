buildscript {
    ext {
        buildToolsVersion = "34.0.0"
        minSdkVersion = 24
        compileSdkVersion = 34
        targetSdkVersion = 34
        ndkVersion = "25.1.8937393"
        kotlinVersion = "1.8.0"
        googlePlayServicesAuthVersion = "20.7.0"
        supportLibVersion = "28.0.0"
        androidXAnnotation = "1.1.0"
        androidXBrowser = "1.0.0"

    }
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlinVersion"
        classpath("com.android.tools.build:gradle")
        classpath 'com.google.gms:google-services:4.4.0'
        classpath("com.facebook.react:react-native-gradle-plugin")
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin")
    }
}


apply plugin: "com.facebook.react.rootproject"
